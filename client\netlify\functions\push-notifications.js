// Push Notifications Service
// Integration & Services Agent: Web push notifications with VAPID

const { createClient } = require('@supabase/supabase-js');
const webpush = require('web-push');

// Initialize Supabase client
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_ANON_KEY
);

// CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS'
};

// Configure VAPID details
webpush.setVapidDetails(
  process.env.VAPID_SUBJECT || 'mailto:<EMAIL>',
  process.env.VAPID_PUBLIC_KEY,
  process.env.VAPID_PRIVATE_KEY
);

// Authenticate user from JWT token
const authenticateUser = async (authHeader) => {
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    throw new Error('Missing or invalid authorization header');
  }

  const token = authHeader.substring(7);
  
  try {
    const { data: { user }, error } = await supabase.auth.getUser(token);
    
    if (error || !user) {
      throw new Error('Invalid authentication token');
    }
    
    return user;
  } catch (error) {
    console.error('Authentication error:', error);
    throw new Error('Authentication failed');
  }
};

// Subscribe user to push notifications
const subscribeToPush = async (user, subscriptionData) => {
  try {
    const { endpoint, keys } = subscriptionData;
    
    if (!endpoint || !keys || !keys.p256dh || !keys.auth) {
      throw new Error('Invalid subscription data');
    }
    
    // Store subscription in database
    const { data: subscription, error } = await supabase
      .from('push_subscriptions')
      .upsert({
        user_id: user.id,
        endpoint: endpoint,
        p256dh_key: keys.p256dh,
        auth_key: keys.auth,
        user_agent: subscriptionData.userAgent || null,
        is_active: true,
        updated_at: new Date().toISOString()
      })
      .select()
      .single();
    
    if (error) {
      throw new Error(`Failed to store subscription: ${error.message}`);
    }
    
    return {
      success: true,
      subscription_id: subscription.id,
      message: 'Successfully subscribed to push notifications'
    };
    
  } catch (error) {
    console.error('Subscribe to push error:', error);
    throw error;
  }
};

// Unsubscribe user from push notifications
const unsubscribeFromPush = async (user, endpoint) => {
  try {
    const { error } = await supabase
      .from('push_subscriptions')
      .update({
        is_active: false,
        unsubscribed_at: new Date().toISOString()
      })
      .eq('user_id', user.id)
      .eq('endpoint', endpoint);
    
    if (error) {
      throw new Error(`Failed to unsubscribe: ${error.message}`);
    }
    
    return {
      success: true,
      message: 'Successfully unsubscribed from push notifications'
    };
    
  } catch (error) {
    console.error('Unsubscribe from push error:', error);
    throw error;
  }
};

// Send push notification to user
const sendPushNotification = async (userId, notificationData) => {
  try {
    const { title, body, icon, badge, data, actions } = notificationData;
    
    // Get user's active push subscriptions
    const { data: subscriptions, error } = await supabase
      .from('push_subscriptions')
      .select('*')
      .eq('user_id', userId)
      .eq('is_active', true);
    
    if (error) {
      throw new Error(`Failed to get subscriptions: ${error.message}`);
    }
    
    if (!subscriptions || subscriptions.length === 0) {
      return {
        success: false,
        message: 'No active push subscriptions found for user'
      };
    }
    
    const payload = JSON.stringify({
      title: title || 'Royaltea Notification',
      body: body || '',
      icon: icon || '/icons/notification-icon.png',
      badge: badge || '/icons/badge-icon.png',
      data: data || {},
      actions: actions || [],
      timestamp: Date.now(),
      requireInteraction: false,
      silent: false
    });
    
    const results = [];
    
    // Send to all active subscriptions
    for (const subscription of subscriptions) {
      try {
        const pushSubscription = {
          endpoint: subscription.endpoint,
          keys: {
            p256dh: subscription.p256dh_key,
            auth: subscription.auth_key
          }
        };
        
        await webpush.sendNotification(pushSubscription, payload);
        
        results.push({
          subscription_id: subscription.id,
          status: 'sent',
          endpoint: subscription.endpoint
        });
        
        // Log successful send
        await supabase
          .from('push_notification_logs')
          .insert({
            user_id: userId,
            subscription_id: subscription.id,
            title: title,
            body: body,
            status: 'sent',
            payload: notificationData
          });
        
      } catch (sendError) {
        console.error('Failed to send to subscription:', sendError);
        
        results.push({
          subscription_id: subscription.id,
          status: 'failed',
          error: sendError.message,
          endpoint: subscription.endpoint
        });
        
        // Log failed send
        await supabase
          .from('push_notification_logs')
          .insert({
            user_id: userId,
            subscription_id: subscription.id,
            title: title,
            body: body,
            status: 'failed',
            error_message: sendError.message,
            payload: notificationData
          });
        
        // If subscription is invalid, deactivate it
        if (sendError.statusCode === 410 || sendError.statusCode === 404) {
          await supabase
            .from('push_subscriptions')
            .update({
              is_active: false,
              error_message: sendError.message,
              updated_at: new Date().toISOString()
            })
            .eq('id', subscription.id);
        }
      }
    }
    
    const successCount = results.filter(r => r.status === 'sent').length;
    const failureCount = results.filter(r => r.status === 'failed').length;
    
    return {
      success: successCount > 0,
      sent: successCount,
      failed: failureCount,
      total: results.length,
      results: results
    };
    
  } catch (error) {
    console.error('Send push notification error:', error);
    throw error;
  }
};

// Send push notification to multiple users
const sendBulkPushNotifications = async (userIds, notificationData) => {
  const results = [];
  
  for (const userId of userIds) {
    try {
      const result = await sendPushNotification(userId, notificationData);
      results.push({
        user_id: userId,
        ...result
      });
    } catch (error) {
      results.push({
        user_id: userId,
        success: false,
        error: error.message
      });
    }
  }
  
  return results;
};

// Get user's push subscription status
const getPushSubscriptionStatus = async (user) => {
  try {
    const { data: subscriptions, error } = await supabase
      .from('push_subscriptions')
      .select('*')
      .eq('user_id', user.id)
      .eq('is_active', true);
    
    if (error) {
      throw new Error(`Failed to get subscription status: ${error.message}`);
    }
    
    return {
      subscribed: subscriptions && subscriptions.length > 0,
      subscription_count: subscriptions ? subscriptions.length : 0,
      subscriptions: subscriptions || []
    };
    
  } catch (error) {
    console.error('Get push subscription status error:', error);
    throw error;
  }
};

// Get VAPID public key
const getVapidPublicKey = () => {
  return {
    public_key: process.env.VAPID_PUBLIC_KEY,
    subject: process.env.VAPID_SUBJECT || 'mailto:<EMAIL>'
  };
};

// Test push notification
const testPushNotification = async (user) => {
  try {
    const testNotification = {
      title: 'Test Notification',
      body: 'This is a test push notification from Royaltea!',
      icon: '/icons/notification-icon.png',
      data: {
        type: 'test',
        timestamp: Date.now()
      }
    };
    
    const result = await sendPushNotification(user.id, testNotification);
    return result;
    
  } catch (error) {
    console.error('Test push notification error:', error);
    throw error;
  }
};

// Main handler function
exports.handler = async (event, context) => {
  // Handle CORS preflight
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers: corsHeaders,
      body: ''
    };
  }

  try {
    // Parse request path and method
    const pathParts = event.path.split('/').filter(Boolean);
    const action = pathParts[pathParts.length - 1];
    const httpMethod = event.httpMethod;
    const body = event.body ? JSON.parse(event.body) : {};

    let result;

    switch (action) {
      case 'subscribe':
        if (httpMethod !== 'POST') {
          throw new Error('Method not allowed');
        }
        const user = await authenticateUser(event.headers.authorization);
        result = await subscribeToPush(user, body);
        break;

      case 'unsubscribe':
        if (httpMethod !== 'POST') {
          throw new Error('Method not allowed');
        }
        const unsubUser = await authenticateUser(event.headers.authorization);
        if (!body.endpoint) {
          throw new Error('Endpoint is required');
        }
        result = await unsubscribeFromPush(unsubUser, body.endpoint);
        break;

      case 'send':
        if (httpMethod !== 'POST') {
          throw new Error('Method not allowed');
        }
        await authenticateUser(event.headers.authorization);
        if (!body.user_id || !body.notification) {
          throw new Error('user_id and notification data are required');
        }
        result = await sendPushNotification(body.user_id, body.notification);
        break;

      case 'send-bulk':
        if (httpMethod !== 'POST') {
          throw new Error('Method not allowed');
        }
        await authenticateUser(event.headers.authorization);
        if (!body.user_ids || !body.notification) {
          throw new Error('user_ids and notification data are required');
        }
        result = await sendBulkPushNotifications(body.user_ids, body.notification);
        break;

      case 'status':
        if (httpMethod !== 'GET') {
          throw new Error('Method not allowed');
        }
        const statusUser = await authenticateUser(event.headers.authorization);
        result = await getPushSubscriptionStatus(statusUser);
        break;

      case 'vapid-key':
        if (httpMethod !== 'GET') {
          throw new Error('Method not allowed');
        }
        result = getVapidPublicKey();
        break;

      case 'test':
        if (httpMethod !== 'POST') {
          throw new Error('Method not allowed');
        }
        const testUser = await authenticateUser(event.headers.authorization);
        result = await testPushNotification(testUser);
        break;

      default:
        throw new Error('Invalid action');
    }

    return {
      statusCode: 200,
      headers: corsHeaders,
      body: JSON.stringify({
        success: true,
        data: result,
        timestamp: new Date().toISOString()
      })
    };

  } catch (error) {
    console.error('Push Notifications API error:', error);
    
    return {
      statusCode: error.message.includes('Authentication') ? 401 : 
                  error.message.includes('not allowed') ? 405 : 400,
      headers: corsHeaders,
      body: JSON.stringify({
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      })
    };
  }
};
