// Security Integration Service
// Integration & Services Agent: Comprehensive security system integration

const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_KEY
);

// CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS'
};

// Security event types
const SECURITY_EVENT_TYPES = {
  LOGIN_SUCCESS: 'login_success',
  LOGIN_FAILURE: 'login_failure',
  SUSPICIOUS_ACTIVITY: 'suspicious_activity',
  RATE_LIMIT_EXCEEDED: 'rate_limit_exceeded',
  ADMIN_ACTION: 'admin_action',
  CONTENT_FLAGGED: 'content_flagged',
  SESSION_TIMEOUT: 'session_timeout',
  UNAUTHORIZED_ACCESS: 'unauthorized_access'
};

// Log security event
const logSecurityEvent = async (eventType, details, userId = null, severity = 'medium') => {
  try {
    const { error } = await supabase
      .from('security_events')
      .insert({
        event_type: eventType,
        user_id: userId,
        severity,
        details: typeof details === 'string' ? { message: details } : details,
        ip_address: details.ip_address || null,
        user_agent: details.user_agent || null,
        risk_score: calculateRiskScore(eventType, details),
        created_at: new Date().toISOString()
      });

    if (error) {
      console.error('Failed to log security event:', error);
    }

    // Trigger alerts for high-severity events
    if (severity === 'high' || severity === 'critical') {
      await triggerSecurityAlert(eventType, details, severity);
    }

  } catch (error) {
    console.error('Security event logging error:', error);
  }
};

// Calculate risk score based on event type and details
const calculateRiskScore = (eventType, details) => {
  let baseScore = 0;
  
  switch (eventType) {
    case SECURITY_EVENT_TYPES.LOGIN_FAILURE:
      baseScore = 30;
      break;
    case SECURITY_EVENT_TYPES.SUSPICIOUS_ACTIVITY:
      baseScore = 70;
      break;
    case SECURITY_EVENT_TYPES.RATE_LIMIT_EXCEEDED:
      baseScore = 50;
      break;
    case SECURITY_EVENT_TYPES.UNAUTHORIZED_ACCESS:
      baseScore = 80;
      break;
    case SECURITY_EVENT_TYPES.CONTENT_FLAGGED:
      baseScore = 40;
      break;
    default:
      baseScore = 20;
  }

  // Adjust score based on details
  if (details.repeated_attempts && details.repeated_attempts > 5) {
    baseScore += 20;
  }
  
  if (details.from_unknown_location) {
    baseScore += 15;
  }
  
  if (details.unusual_time) {
    baseScore += 10;
  }

  return Math.min(baseScore, 100);
};

// Trigger security alert
const triggerSecurityAlert = async (eventType, details, severity) => {
  try {
    // Send notification to admin team
    const alertData = {
      type: 'security_alert',
      event_type: eventType,
      severity,
      details,
      timestamp: new Date().toISOString()
    };

    // Use existing notification service
    await fetch(`${process.env.NETLIFY_URL}/.netlify/functions/email-service/send`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_KEY}`
      },
      body: JSON.stringify({
        to: process.env.ADMIN_EMAIL || '<EMAIL>',
        template: 'security_alert',
        data: alertData
      })
    });

    // Log alert in admin actions
    await supabase
      .from('admin_actions')
      .insert({
        admin_id: null, // System generated
        action_type: 'security_alert_sent',
        target_type: 'system',
        target_id: null,
        details: alertData,
        created_at: new Date().toISOString()
      });

  } catch (error) {
    console.error('Failed to trigger security alert:', error);
  }
};

// Monitor user session activity
const monitorSessionActivity = async (userId, activityType, details = {}) => {
  try {
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('security_data')
      .eq('id', userId)
      .single();

    if (userError || !user) {
      return;
    }

    const securityData = user.security_data || {};
    const now = new Date();
    
    // Update last activity
    securityData.last_activity = now.toISOString();
    securityData.activity_count = (securityData.activity_count || 0) + 1;
    
    // Track activity patterns
    if (!securityData.activity_patterns) {
      securityData.activity_patterns = {};
    }
    
    const hour = now.getHours();
    const dayOfWeek = now.getDay();
    
    securityData.activity_patterns[`hour_${hour}`] = 
      (securityData.activity_patterns[`hour_${hour}`] || 0) + 1;
    securityData.activity_patterns[`day_${dayOfWeek}`] = 
      (securityData.activity_patterns[`day_${dayOfWeek}`] || 0) + 1;

    // Detect unusual activity
    const isUnusualTime = hour < 6 || hour > 22;
    const isWeekend = dayOfWeek === 0 || dayOfWeek === 6;
    
    if (isUnusualTime && !securityData.usual_night_activity) {
      await logSecurityEvent(
        SECURITY_EVENT_TYPES.SUSPICIOUS_ACTIVITY,
        {
          message: 'Unusual time activity detected',
          user_id: userId,
          activity_type: activityType,
          time: now.toISOString(),
          unusual_time: true
        },
        userId,
        'medium'
      );
    }

    // Update user security data
    await supabase
      .from('users')
      .update({ security_data: securityData })
      .eq('id', userId);

  } catch (error) {
    console.error('Session monitoring error:', error);
  }
};

// Check for suspicious patterns
const checkSuspiciousPatterns = async (userId, eventType, details) => {
  try {
    // Get recent security events for this user
    const { data: recentEvents, error } = await supabase
      .from('security_events')
      .select('*')
      .eq('user_id', userId)
      .gte('created_at', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString())
      .order('created_at', { ascending: false });

    if (error || !recentEvents) {
      return false;
    }

    // Check for repeated failed login attempts
    if (eventType === SECURITY_EVENT_TYPES.LOGIN_FAILURE) {
      const failedLogins = recentEvents.filter(e => e.event_type === SECURITY_EVENT_TYPES.LOGIN_FAILURE);
      
      if (failedLogins.length >= 5) {
        await logSecurityEvent(
          SECURITY_EVENT_TYPES.SUSPICIOUS_ACTIVITY,
          {
            message: 'Multiple failed login attempts detected',
            user_id: userId,
            failed_attempts: failedLogins.length,
            repeated_attempts: true
          },
          userId,
          'high'
        );
        return true;
      }
    }

    // Check for rate limiting violations
    const rateLimitEvents = recentEvents.filter(e => e.event_type === SECURITY_EVENT_TYPES.RATE_LIMIT_EXCEEDED);
    if (rateLimitEvents.length >= 3) {
      await logSecurityEvent(
        SECURITY_EVENT_TYPES.SUSPICIOUS_ACTIVITY,
        {
          message: 'Multiple rate limit violations detected',
          user_id: userId,
          violations: rateLimitEvents.length
        },
        userId,
        'high'
      );
      return true;
    }

    return false;

  } catch (error) {
    console.error('Suspicious pattern check error:', error);
    return false;
  }
};

// Get security dashboard data
const getSecurityDashboard = async (adminUserId) => {
  try {
    // Verify admin permissions
    const { data: admin, error: adminError } = await supabase
      .from('users')
      .select('admin_role, permissions')
      .eq('id', adminUserId)
      .single();

    if (adminError || !admin || !admin.admin_role) {
      throw new Error('Unauthorized: Admin access required');
    }

    // Get security metrics for the last 24 hours
    const last24Hours = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString();
    
    const { data: securityEvents, error: eventsError } = await supabase
      .from('security_events')
      .select('*')
      .gte('created_at', last24Hours)
      .order('created_at', { ascending: false });

    if (eventsError) {
      throw new Error(`Failed to fetch security events: ${eventsError.message}`);
    }

    // Get flagged content
    const { data: flaggedContent, error: contentError } = await supabase
      .from('content_flags')
      .select('*')
      .eq('status', 'pending')
      .order('created_at', { ascending: false })
      .limit(10);

    if (contentError) {
      console.error('Failed to fetch flagged content:', contentError);
    }

    // Calculate metrics
    const totalEvents = securityEvents.length;
    const highRiskEvents = securityEvents.filter(e => e.risk_score >= 70).length;
    const loginFailures = securityEvents.filter(e => e.event_type === SECURITY_EVENT_TYPES.LOGIN_FAILURE).length;
    const suspiciousActivity = securityEvents.filter(e => e.event_type === SECURITY_EVENT_TYPES.SUSPICIOUS_ACTIVITY).length;

    // Group events by type
    const eventsByType = securityEvents.reduce((acc, event) => {
      acc[event.event_type] = (acc[event.event_type] || 0) + 1;
      return acc;
    }, {});

    // Group events by hour for trend analysis
    const eventsByHour = securityEvents.reduce((acc, event) => {
      const hour = new Date(event.created_at).getHours();
      acc[hour] = (acc[hour] || 0) + 1;
      return acc;
    }, {});

    return {
      metrics: {
        total_events: totalEvents,
        high_risk_events: highRiskEvents,
        login_failures: loginFailures,
        suspicious_activity: suspiciousActivity,
        flagged_content_count: flaggedContent ? flaggedContent.length : 0
      },
      events_by_type: eventsByType,
      events_by_hour: eventsByHour,
      recent_events: securityEvents.slice(0, 20),
      flagged_content: flaggedContent || [],
      risk_level: highRiskEvents > 10 ? 'high' : highRiskEvents > 5 ? 'medium' : 'low'
    };

  } catch (error) {
    console.error('Security dashboard error:', error);
    throw error;
  }
};

// Main handler function
exports.handler = async (event, context) => {
  // Handle CORS preflight
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers: corsHeaders,
      body: ''
    };
  }

  try {
    // Parse request
    const pathParts = event.path.split('/').filter(Boolean);
    const action = pathParts[pathParts.length - 1];
    const httpMethod = event.httpMethod;
    const body = event.body ? JSON.parse(event.body) : {};

    let result;

    switch (action) {
      case 'log-event':
        if (httpMethod !== 'POST') {
          throw new Error('Method not allowed');
        }
        
        const { event_type, details, user_id, severity = 'medium' } = body;
        if (!event_type || !details) {
          throw new Error('event_type and details are required');
        }

        await logSecurityEvent(event_type, details, user_id, severity);
        
        // Check for suspicious patterns
        if (user_id) {
          await checkSuspiciousPatterns(user_id, event_type, details);
        }
        
        result = { success: true, message: 'Security event logged' };
        break;

      case 'monitor-session':
        if (httpMethod !== 'POST') {
          throw new Error('Method not allowed');
        }
        
        const { user_id: sessionUserId, activity_type, activity_details = {} } = body;
        if (!sessionUserId || !activity_type) {
          throw new Error('user_id and activity_type are required');
        }

        await monitorSessionActivity(sessionUserId, activity_type, activity_details);
        result = { success: true, message: 'Session activity monitored' };
        break;

      case 'dashboard':
        if (httpMethod !== 'GET') {
          throw new Error('Method not allowed');
        }
        
        // Extract admin user ID from authorization header
        const authHeader = event.headers.authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
          throw new Error('Authorization required');
        }

        const token = authHeader.substring(7);
        const { data: { user }, error: authError } = await supabase.auth.getUser(token);
        
        if (authError || !user) {
          throw new Error('Invalid authentication token');
        }

        result = await getSecurityDashboard(user.id);
        break;

      default:
        throw new Error('Invalid action');
    }

    return {
      statusCode: 200,
      headers: corsHeaders,
      body: JSON.stringify({
        success: true,
        data: result,
        timestamp: new Date().toISOString()
      })
    };

  } catch (error) {
    console.error('Security Integration Service error:', error);
    
    return {
      statusCode: error.message.includes('Unauthorized') || error.message.includes('Authorization') ? 401 : 
                  error.message.includes('not allowed') ? 405 : 400,
      headers: corsHeaders,
      body: JSON.stringify({
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      })
    };
  }
};
