/**
 * Generate Sample Agreement Script
 * 
 * Creates a real agreement file to demonstrate alliance-venture separation
 */

// Import just the parts we need to avoid missing dependencies
import fs from 'fs';
import path from 'path';

// Simple agreement generator without dependencies
class SimpleAgreementGenerator {
  constructor() {
    const today = new Date();
    this.currentDate = today.toLocaleDateString('en-US', {
      month: 'long',
      day: 'numeric',
      year: 'numeric'
    });
  }

  async generateAgreement(templateText, project, options = {}) {
    if (!templateText || !project) {
      throw new Error('Template text and project data are required');
    }

    const { contributors = [], currentUser = null, fullName = '' } = options;

    // Get company info (alliance or fallback)
    const companyInfo = await this._getCompanyInfo(project, contributors, currentUser);

    // Process the template
    let result = templateText;

    // Replace dates
    result = result.replace(/\[Date\]/g, this.currentDate);
    result = result.replace(/\[ \], 20\[__\]/g, this.currentDate);

    // Replace company info
    result = result.replace(/City of Gamers Inc\./g, companyInfo.name);
    result = result.replace(/City of Gamers Inc/g, companyInfo.name);
    result = result.replace(/Gynell Journigan/g, companyInfo.signerName);
    result = result.replace(/President/g, companyInfo.signerTitle);

    // Replace project info
    result = result.replace(/Village of The Ages/g, project.name);
    result = result.replace(/village simulation game where players guide communities through historical progressions and manage resource-based challenges/g, project.description);

    // Replace contributor info
    const contributorName = fullName || currentUser?.user_metadata?.full_name || 'Contributor';
    result = result.replace(/\[Contributor\]/g, contributorName);

    // Replace location info
    if (companyInfo.address) {
      result = result.replace(/1205 43rd Street, Suite B, Orlando, Florida 32839/g, companyInfo.address);
    }
    if (companyInfo.state) {
      result = result.replace(/Florida/g, companyInfo.state);
    }

    // Clean up any remaining placeholders
    result = result.replace(/\[.*?\]/g, '');

    return result;
  }

  async _getCompanyInfo(project, contributors, currentUser) {
    // Try to get alliance info if project has alliance_id
    if (project.alliance_id) {
      try {
        // Mock alliance data for demo
        return {
          name: 'Green Tech Alliance LLC',
          signerName: 'Jane Smith',
          signerTitle: 'Founder',
          address: '123 Green Tech Way, San Francisco, CA 94105',
          state: 'California'
        };
      } catch (error) {
        console.log('Could not fetch alliance data, using fallback');
      }
    }

    // Fallback to project owner
    const owner = contributors.find(c => c.permission_level === 'Owner');
    return {
      name: owner?.display_name || 'Project Company',
      signerName: owner?.display_name || 'Project Owner',
      signerTitle: 'Owner',
      address: project.address || 'Business Address',
      state: project.state || 'Delaware'
    };
  }
}
// Use the lawyer-approved template
const templatePath = './public/example-cog-contributor-agreement.md';
const template = fs.readFileSync(templatePath, 'utf8');

async function generateSampleAgreement() {
  console.log('🔄 Generating sample agreement with alliance-venture separation...');
  
  const generator = new SimpleAgreementGenerator();
  
  // Sample project with alliance
  const project = {
    id: 'venture-456',
    name: 'EcoTech Innovations',
    description: 'A platform connecting eco-conscious consumers with sustainable product alternatives using AI recommendations',
    project_type: 'software',
    alliance_id: 'alliance-123',
    team_id: 'alliance-123',
    state: 'California',
    city: 'San Francisco',
    address: '123 Green Tech Way, San Francisco, CA 94105'
  };

  const contributors = [{
    id: 'contrib-1',
    permission_level: 'Owner',
    display_name: 'John Venture Owner',
    email: '<EMAIL>'
  }];

  const currentUser = {
    id: 'user-2',
    email: '<EMAIL>',
    user_metadata: { 
      full_name: 'Sarah Developer' 
    }
  };

  try {
    const agreement = await generator.generateAgreement(template, project, {
      contributors,
      currentUser,
      fullName: 'Sarah Developer'
    });

    // Create output directory
    const outputDir = './sample-agreements';
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }

    // Save the agreement
    const filename = `alliance-venture-agreement-${new Date().toISOString().split('T')[0]}.md`;
    const filepath = path.join(outputDir, filename);

    const metadata = `<!-- ALLIANCE-VENTURE SEPARATED AGREEMENT
Generated: ${new Date().toISOString()}
Alliance (Company): Green Tech Alliance LLC
Venture (Project): EcoTech Innovations
Contributor: Sarah Developer
Signer: Jane Smith (Alliance Founder)

KEY FEATURES:
✅ Alliance is the company entity in the agreement
✅ Venture is the project being worked on
✅ Alliance founder signs for the company
✅ Alliance location used for legal jurisdiction
✅ Proper legal structure maintained
-->

`;

    fs.writeFileSync(filepath, metadata + agreement);
    
    console.log(`✅ Agreement generated successfully!`);
    console.log(`📄 File: ${filepath}`);
    console.log(`\n🏢 Company: Green Tech Alliance LLC (alliance)`);
    console.log(`🚀 Project: EcoTech Innovations (venture)`);
    console.log(`👤 Contributor: Sarah Developer`);
    console.log(`✍️  Signer: Jane Smith, Founder`);
    console.log(`📍 Location: San Francisco, CA`);
    console.log(`\n📋 Agreement shows proper alliance-venture separation throughout!`);

  } catch (error) {
    console.error('❌ Error generating agreement:', error);
  }
}

generateSampleAgreement();
