import { defineConfig } from 'vitest/config'
import react from '@vitejs/plugin-react'
import { resolve } from 'path'

export default defineConfig({
  plugins: [react()],
  test: {
    environment: 'jsdom',
    setupFiles: ['./src/tests/setup.js'],
    globals: true,
    css: true,
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html'],
      exclude: [
        'node_modules/',
        'src/tests/setup.js',
        '**/*.config.js',
        'dist/',
        'build/'
      ]
    }
  },
  resolve: {
    alias: {
      '@': resolve(__dirname, './src'),
      'buffer': 'buffer',
      // Fix HeroUI import issues for tests
      'flat': resolve(__dirname, './src/utils/flat-patch.js'),
      'color': resolve(__dirname, './src/utils/color-patch.js'),
      'deepmerge': resolve(__dirname, './src/utils/deepmerge-patch.js'),
      'tailwindcss/plugin.js': resolve(__dirname, './src/utils/tailwind-plugin-patch.js'),
      'tailwindcss/plugin': resolve(__dirname, './src/utils/tailwind-plugin-patch.js')
    }
  },
  define: {
    global: 'globalThis',
    'process.env': {},
    'process.env.NODE_ENV': JSON.stringify('test'),
  }
})
