// Discord Integration Service
// Integration & Services Agent: Discord bot and webhook integration

const { createClient } = require('@supabase/supabase-js');
const { Client, GatewayIntentBits, EmbedBuilder } = require('discord.js');

// Initialize Supabase client
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_ANON_KEY
);

// CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS'
};

// Discord client configuration
let discordClient = null;

const initializeDiscordClient = () => {
  if (!process.env.DISCORD_BOT_TOKEN) {
    console.warn('Discord bot token not configured');
    return null;
  }

  if (discordClient) {
    return discordClient;
  }

  discordClient = new Client({
    intents: [
      GatewayIntentBits.Guilds,
      GatewayIntentBits.GuildMessages,
      GatewayIntentBits.MessageContent
    ]
  });

  discordClient.login(process.env.DISCORD_BOT_TOKEN);
  return discordClient;
};

// Authenticate user from JWT token
const authenticateUser = async (authHeader) => {
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    throw new Error('Missing or invalid authorization header');
  }

  const token = authHeader.substring(7);
  
  try {
    const { data: { user }, error } = await supabase.auth.getUser(token);
    
    if (error || !user) {
      throw new Error('Invalid authentication token');
    }
    
    return user;
  } catch (error) {
    console.error('Authentication error:', error);
    throw new Error('Authentication failed');
  }
};

// Send Discord webhook message
const sendDiscordWebhook = async (webhookUrl, messageData) => {
  try {
    const { content, embeds, username, avatar_url } = messageData;
    
    const payload = {
      content: content || '',
      username: username || 'Royaltea Bot',
      avatar_url: avatar_url || 'https://royalty.technology/icons/bot-avatar.png'
    };
    
    if (embeds && embeds.length > 0) {
      payload.embeds = embeds;
    }
    
    const response = await fetch(webhookUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(payload)
    });
    
    if (!response.ok) {
      throw new Error(`Discord webhook failed: ${response.status} ${response.statusText}`);
    }
    
    return {
      success: true,
      status: response.status,
      message: 'Discord message sent successfully'
    };
    
  } catch (error) {
    console.error('Send Discord webhook error:', error);
    throw error;
  }
};

// Create Discord embed for different notification types
const createDiscordEmbed = (type, data) => {
  const embed = new EmbedBuilder();
  
  switch (type) {
    case 'payment_received':
      embed
        .setTitle('💰 Payment Received')
        .setDescription(`Payment of $${data.amount} ${data.currency} received`)
        .setColor(0x4CAF50)
        .addFields(
          { name: 'Amount', value: `$${data.amount} ${data.currency}`, inline: true },
          { name: 'From', value: data.senderName || 'Unknown', inline: true },
          { name: 'Description', value: data.description || 'No description', inline: false }
        )
        .setTimestamp();
      break;
      
    case 'project_invitation':
      embed
        .setTitle('📋 Project Invitation')
        .setDescription(`You've been invited to join ${data.projectName}`)
        .setColor(0x2196F3)
        .addFields(
          { name: 'Project', value: data.projectName, inline: true },
          { name: 'Role', value: data.role || 'Contributor', inline: true },
          { name: 'Invited by', value: data.inviterName, inline: true }
        )
        .setTimestamp();
      break;
      
    case 'friend_request':
      embed
        .setTitle('👥 Friend Request')
        .setDescription(`${data.senderName} sent you a friend request`)
        .setColor(0xFF9800)
        .addFields(
          { name: 'From', value: data.senderName, inline: true },
          { name: 'Bio', value: data.senderBio || 'No bio available', inline: false }
        )
        .setTimestamp();
      break;
      
    case 'escrow_release':
      embed
        .setTitle('🔓 Escrow Released')
        .setDescription(`Escrow funds of $${data.amount} released`)
        .setColor(0x9C27B0)
        .addFields(
          { name: 'Amount', value: `$${data.amount} ${data.currency}`, inline: true },
          { name: 'Project', value: data.projectName, inline: true },
          { name: 'Milestone', value: data.milestone || 'General release', inline: true }
        )
        .setTimestamp();
      break;
      
    case 'mission_completed':
      embed
        .setTitle('✅ Mission Completed')
        .setDescription(`Mission "${data.missionName}" has been completed`)
        .setColor(0x4CAF50)
        .addFields(
          { name: 'Mission', value: data.missionName, inline: true },
          { name: 'Reward', value: `${data.reward} ORB`, inline: true },
          { name: 'Completed by', value: data.completedBy, inline: true }
        )
        .setTimestamp();
      break;
      
    default:
      embed
        .setTitle('🔔 Notification')
        .setDescription(data.message || 'New notification from Royaltea')
        .setColor(0x607D8B)
        .setTimestamp();
  }
  
  return embed;
};

// Send notification to Discord
const sendDiscordNotification = async (userId, notificationType, notificationData) => {
  try {
    // Get user's Discord integration settings
    const { data: integration, error } = await supabase
      .from('discord_integrations')
      .select('*')
      .eq('user_id', userId)
      .eq('is_active', true)
      .single();
    
    if (error || !integration) {
      return {
        success: false,
        message: 'No active Discord integration found for user'
      };
    }
    
    const embed = createDiscordEmbed(notificationType, notificationData);
    
    const messageData = {
      embeds: [embed.toJSON()],
      username: 'Royaltea Notifications',
      avatar_url: 'https://royalty.technology/icons/notification-bot.png'
    };
    
    const result = await sendDiscordWebhook(integration.webhook_url, messageData);
    
    // Log the notification
    await supabase
      .from('discord_notification_logs')
      .insert({
        user_id: userId,
        integration_id: integration.id,
        notification_type: notificationType,
        status: 'sent',
        data: notificationData
      });
    
    return result;
    
  } catch (error) {
    console.error('Send Discord notification error:', error);
    
    // Log the error
    await supabase
      .from('discord_notification_logs')
      .insert({
        user_id: userId,
        notification_type: notificationType,
        status: 'failed',
        error_message: error.message,
        data: notificationData
      });
    
    throw error;
  }
};

// Setup Discord integration for user
const setupDiscordIntegration = async (user, integrationData) => {
  try {
    const { webhook_url, guild_id, channel_id, notification_types } = integrationData;
    
    if (!webhook_url) {
      throw new Error('Discord webhook URL is required');
    }
    
    // Test the webhook
    const testMessage = {
      content: '✅ Discord integration successfully configured for Royaltea!',
      username: 'Royaltea Setup',
      avatar_url: 'https://royalty.technology/icons/setup-bot.png'
    };
    
    await sendDiscordWebhook(webhook_url, testMessage);
    
    // Store integration settings
    const { data: integration, error } = await supabase
      .from('discord_integrations')
      .upsert({
        user_id: user.id,
        webhook_url: webhook_url,
        guild_id: guild_id,
        channel_id: channel_id,
        notification_types: notification_types || ['payment_received', 'project_invitation', 'escrow_release'],
        is_active: true,
        updated_at: new Date().toISOString()
      })
      .select()
      .single();
    
    if (error) {
      throw new Error(`Failed to store Discord integration: ${error.message}`);
    }
    
    return {
      success: true,
      integration_id: integration.id,
      message: 'Discord integration configured successfully'
    };
    
  } catch (error) {
    console.error('Setup Discord integration error:', error);
    throw error;
  }
};

// Remove Discord integration
const removeDiscordIntegration = async (user) => {
  try {
    const { error } = await supabase
      .from('discord_integrations')
      .update({
        is_active: false,
        removed_at: new Date().toISOString()
      })
      .eq('user_id', user.id);
    
    if (error) {
      throw new Error(`Failed to remove Discord integration: ${error.message}`);
    }
    
    return {
      success: true,
      message: 'Discord integration removed successfully'
    };
    
  } catch (error) {
    console.error('Remove Discord integration error:', error);
    throw error;
  }
};

// Get Discord integration status
const getDiscordIntegrationStatus = async (user) => {
  try {
    const { data: integration, error } = await supabase
      .from('discord_integrations')
      .select('*')
      .eq('user_id', user.id)
      .eq('is_active', true)
      .single();
    
    if (error && error.code !== 'PGRST116') { // Not found is OK
      throw new Error(`Failed to get Discord integration: ${error.message}`);
    }
    
    return {
      integrated: !!integration,
      integration: integration || null,
      notification_types: integration?.notification_types || []
    };
    
  } catch (error) {
    console.error('Get Discord integration status error:', error);
    throw error;
  }
};

// Test Discord integration
const testDiscordIntegration = async (user) => {
  try {
    const testData = {
      message: 'This is a test notification from Royaltea!',
      timestamp: new Date().toISOString(),
      user: user.user_metadata?.display_name || user.email
    };
    
    const result = await sendDiscordNotification(user.id, 'test', testData);
    return result;
    
  } catch (error) {
    console.error('Test Discord integration error:', error);
    throw error;
  }
};

// Main handler function
exports.handler = async (event, context) => {
  // Handle CORS preflight
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers: corsHeaders,
      body: ''
    };
  }

  try {
    // Parse request path and method
    const pathParts = event.path.split('/').filter(Boolean);
    const action = pathParts[pathParts.length - 1];
    const httpMethod = event.httpMethod;
    const body = event.body ? JSON.parse(event.body) : {};

    let result;

    switch (action) {
      case 'setup':
        if (httpMethod !== 'POST') {
          throw new Error('Method not allowed');
        }
        const user = await authenticateUser(event.headers.authorization);
        result = await setupDiscordIntegration(user, body);
        break;

      case 'remove':
        if (httpMethod !== 'DELETE') {
          throw new Error('Method not allowed');
        }
        const removeUser = await authenticateUser(event.headers.authorization);
        result = await removeDiscordIntegration(removeUser);
        break;

      case 'status':
        if (httpMethod !== 'GET') {
          throw new Error('Method not allowed');
        }
        const statusUser = await authenticateUser(event.headers.authorization);
        result = await getDiscordIntegrationStatus(statusUser);
        break;

      case 'test':
        if (httpMethod !== 'POST') {
          throw new Error('Method not allowed');
        }
        const testUser = await authenticateUser(event.headers.authorization);
        result = await testDiscordIntegration(testUser);
        break;

      case 'send':
        if (httpMethod !== 'POST') {
          throw new Error('Method not allowed');
        }
        await authenticateUser(event.headers.authorization);
        if (!body.user_id || !body.type || !body.data) {
          throw new Error('user_id, type, and data are required');
        }
        result = await sendDiscordNotification(body.user_id, body.type, body.data);
        break;

      case 'webhook':
        if (httpMethod !== 'POST') {
          throw new Error('Method not allowed');
        }
        if (!body.webhook_url || !body.message) {
          throw new Error('webhook_url and message are required');
        }
        result = await sendDiscordWebhook(body.webhook_url, body.message);
        break;

      default:
        throw new Error('Invalid action');
    }

    return {
      statusCode: 200,
      headers: corsHeaders,
      body: JSON.stringify({
        success: true,
        data: result,
        timestamp: new Date().toISOString()
      })
    };

  } catch (error) {
    console.error('Discord Integration API error:', error);
    
    return {
      statusCode: error.message.includes('Authentication') ? 401 : 
                  error.message.includes('not allowed') ? 405 : 400,
      headers: corsHeaders,
      body: JSON.stringify({
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      })
    };
  }
};
