// Quest System Foundation API
// Backend Specialist: Framework for gamified collaboration and achievement quests
const { createClient } = require('@supabase/supabase-js');

// Supabase client
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_KEY
);

// Helper function to get user from request
const getUserFromRequest = (event) => {
  const authHeader = event.headers.authorization;
  if (!authHeader) return null;
  
  try {
    const token = authHeader.replace('Bearer ', '');
    const payload = JSON.parse(Buffer.from(token.split('.')[1], 'base64').toString());
    return payload.sub;
  } catch {
    return null;
  }
};

// Helper function to calculate quest progress
const calculateQuestProgress = async (userId, questId) => {
  try {
    // Get quest requirements
    const { data: quest } = await supabase
      .from('tasks')
      .select('id, title, description, quest_requirements, quest_rewards')
      .eq('id', questId)
      .eq('task_category', 'quest')
      .single();

    if (!quest || !quest.quest_requirements) {
      return { progress: 0, completed: false, requirements_met: {} };
    }

    const requirements = quest.quest_requirements;
    const requirementsMet = {};
    let totalProgress = 0;

    // Check each requirement type
    for (const [reqType, reqData] of Object.entries(requirements)) {
      switch (reqType) {
        case 'complete_missions':
          const { data: completedMissions } = await supabase
            .from('tasks')
            .select('id')
            .eq('assignee_id', userId)
            .eq('status', 'done')
            .eq('task_category', 'mission');
          
          const missionCount = completedMissions?.length || 0;
          requirementsMet[reqType] = {
            current: missionCount,
            required: reqData.count,
            completed: missionCount >= reqData.count
          };
          break;

        case 'complete_bounties':
          const { data: completedBounties } = await supabase
            .from('tasks')
            .select('id')
            .eq('assignee_id', userId)
            .eq('status', 'done')
            .eq('task_category', 'bounty');
          
          const bountyCount = completedBounties?.length || 0;
          requirementsMet[reqType] = {
            current: bountyCount,
            required: reqData.count,
            completed: bountyCount >= reqData.count
          };
          break;

        case 'earn_endorsements':
          const { data: endorsements } = await supabase
            .from('user_endorsements')
            .select('id')
            .eq('endorsed_user_id', userId)
            .eq('status', 'active');
          
          const endorsementCount = endorsements?.length || 0;
          requirementsMet[reqType] = {
            current: endorsementCount,
            required: reqData.count,
            completed: endorsementCount >= reqData.count
          };
          break;

        case 'achieve_skill_level':
          const { data: userSkills } = await supabase
            .from('user_skills')
            .select('skill_name, proficiency_score')
            .eq('user_id', userId)
            .in('skill_name', reqData.skills);
          
          const skillsAchieved = userSkills?.filter(skill => 
            skill.proficiency_score >= reqData.min_level
          ).length || 0;
          
          requirementsMet[reqType] = {
            current: skillsAchieved,
            required: reqData.skills.length,
            completed: skillsAchieved >= reqData.skills.length
          };
          break;

        case 'join_alliances':
          const { data: allianceMemberships } = await supabase
            .from('team_members')
            .select('id')
            .eq('user_id', userId)
            .eq('status', 'active');
          
          const allianceCount = allianceMemberships?.length || 0;
          requirementsMet[reqType] = {
            current: allianceCount,
            required: reqData.count,
            completed: allianceCount >= reqData.count
          };
          break;

        default:
          requirementsMet[reqType] = {
            current: 0,
            required: 1,
            completed: false
          };
      }
    }

    // Calculate overall progress
    const completedRequirements = Object.values(requirementsMet).filter(req => req.completed).length;
    const totalRequirements = Object.keys(requirements).length;
    totalProgress = totalRequirements > 0 ? (completedRequirements / totalRequirements) * 100 : 0;

    return {
      progress: Math.round(totalProgress),
      completed: totalProgress >= 100,
      requirements_met: requirementsMet
    };

  } catch (error) {
    console.error('Calculate quest progress error:', error);
    return { progress: 0, completed: false, requirements_met: {} };
  }
};

// Get Available Quests
const getAvailableQuests = async (event) => {
  try {
    const userId = getUserFromRequest(event);
    if (!userId) {
      return {
        statusCode: 401,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    const queryParams = new URLSearchParams(event.queryStringParameters || {});
    const category = queryParams.get('category'); // 'skill', 'collaboration', 'achievement'
    const difficulty = queryParams.get('difficulty');
    const status = queryParams.get('status') || 'available'; // 'available', 'active', 'completed'

    // Get quests based on status
    let query = supabase
      .from('tasks')
      .select(`
        id,
        title,
        description,
        task_category,
        quest_type,
        quest_requirements,
        quest_rewards,
        difficulty_level,
        difficulty_points,
        estimated_hours,
        deadline,
        is_public,
        status,
        created_at,
        updated_at,
        creator:users!tasks_created_by_fkey(
          id,
          display_name,
          avatar_url
        )
      `)
      .eq('task_category', 'quest')
      .eq('is_public', true);

    // Apply filters
    if (category) {
      query = query.eq('quest_type', category);
    }

    if (difficulty) {
      query = query.eq('difficulty_level', difficulty);
    }

    const { data: quests, error: questsError } = await query
      .order('created_at', { ascending: false })
      .limit(50);

    if (questsError) {
      throw new Error(`Failed to fetch quests: ${questsError.message}`);
    }

    // Get user's quest progress for each quest
    const questsWithProgress = await Promise.all(
      (quests || []).map(async (quest) => {
        const progressData = await calculateQuestProgress(userId, quest.id);
        
        // Check if user has started this quest
        const { data: userQuest } = await supabase
          .from('user_quests')
          .select('id, status, started_at, completed_at')
          .eq('user_id', userId)
          .eq('quest_id', quest.id)
          .single();

        const questStatus = userQuest ? userQuest.status : 
                           progressData.completed ? 'completed' : 'available';

        return {
          id: quest.id,
          title: quest.title,
          description: quest.description,
          category: quest.quest_type || 'general',
          requirements: quest.quest_requirements || {},
          rewards: quest.quest_rewards || {},
          difficulty_level: quest.difficulty_level,
          difficulty_points: quest.difficulty_points,
          estimated_hours: quest.estimated_hours,
          deadline: quest.deadline,
          status: questStatus,
          progress: progressData.progress,
          requirements_met: progressData.requirements_met,
          is_completed: progressData.completed,
          user_quest_id: userQuest?.id || null,
          started_at: userQuest?.started_at || null,
          completed_at: userQuest?.completed_at || null,
          creator: quest.creator,
          created_at: quest.created_at,
          updated_at: quest.updated_at
        };
      })
    );

    // Filter by status if specified
    const filteredQuests = status === 'all' ? questsWithProgress :
      questsWithProgress.filter(quest => {
        switch (status) {
          case 'available':
            return quest.status === 'available';
          case 'active':
            return quest.status === 'active' || quest.status === 'in_progress';
          case 'completed':
            return quest.status === 'completed';
          default:
            return true;
        }
      });

    // Calculate quest statistics
    const stats = {
      total_quests: filteredQuests.length,
      available_quests: questsWithProgress.filter(q => q.status === 'available').length,
      active_quests: questsWithProgress.filter(q => q.status === 'active' || q.status === 'in_progress').length,
      completed_quests: questsWithProgress.filter(q => q.status === 'completed').length,
      categories: [...new Set(filteredQuests.map(q => q.category))],
      difficulty_distribution: filteredQuests.reduce((acc, q) => {
        acc[q.difficulty_level] = (acc[q.difficulty_level] || 0) + 1;
        return acc;
      }, {})
    };

    return {
      statusCode: 200,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        quests: filteredQuests,
        statistics: stats,
        filters: {
          category,
          difficulty,
          status
        }
      })
    };

  } catch (error) {
    console.error('Get available quests error:', error);
    return {
      statusCode: 500,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ error: 'Failed to fetch quests' })
    };
  }
};

// Start Quest
const startQuest = async (event) => {
  try {
    const userId = getUserFromRequest(event);
    if (!userId) {
      return {
        statusCode: 401,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    const questId = event.path.split('/')[2]; // /quest-system/{id}/start

    // Check if quest exists and is available
    const { data: quest, error: questError } = await supabase
      .from('tasks')
      .select('id, title, quest_requirements, quest_rewards, is_public, status')
      .eq('id', questId)
      .eq('task_category', 'quest')
      .single();

    if (questError || !quest) {
      return {
        statusCode: 404,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Quest not found' })
      };
    }

    if (!quest.is_public) {
      return {
        statusCode: 403,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Quest is not public' })
      };
    }

    // Check if user already started this quest
    const { data: existingUserQuest } = await supabase
      .from('user_quests')
      .select('id, status')
      .eq('user_id', userId)
      .eq('quest_id', questId)
      .single();

    if (existingUserQuest) {
      return {
        statusCode: 400,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          error: 'Quest already started',
          current_status: existingUserQuest.status
        })
      };
    }

    // Create user quest record
    const userQuestData = {
      user_id: userId,
      quest_id: questId,
      status: 'active',
      started_at: new Date().toISOString(),
      progress_data: {}
    };

    const { data: userQuest, error: userQuestError } = await supabase
      .from('user_quests')
      .insert([userQuestData])
      .select()
      .single();

    if (userQuestError) {
      throw new Error(`Failed to start quest: ${userQuestError.message}`);
    }

    // Calculate initial progress
    const progressData = await calculateQuestProgress(userId, questId);

    return {
      statusCode: 201,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        user_quest: userQuest,
        quest: {
          id: quest.id,
          title: quest.title,
          requirements: quest.quest_requirements,
          rewards: quest.quest_rewards
        },
        progress: progressData
      })
    };

  } catch (error) {
    console.error('Start quest error:', error);
    return {
      statusCode: 500,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ error: 'Failed to start quest' })
    };
  }
};

// Get Quest Progress
const getQuestProgress = async (event) => {
  try {
    const userId = getUserFromRequest(event);
    if (!userId) {
      return {
        statusCode: 401,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    const questId = event.path.split('/')[2]; // /quest-system/{id}/progress

    // Get user's quest record
    const { data: userQuest, error: userQuestError } = await supabase
      .from('user_quests')
      .select(`
        id,
        status,
        started_at,
        completed_at,
        progress_data,
        quest:tasks!user_quests_quest_id_fkey(
          id,
          title,
          description,
          quest_requirements,
          quest_rewards
        )
      `)
      .eq('user_id', userId)
      .eq('quest_id', questId)
      .single();

    if (userQuestError || !userQuest) {
      return {
        statusCode: 404,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Quest not started or not found' })
      };
    }

    // Calculate current progress
    const progressData = await calculateQuestProgress(userId, questId);

    // Check if quest should be completed
    if (progressData.completed && userQuest.status !== 'completed') {
      // Update quest status to completed
      const { error: updateError } = await supabase
        .from('user_quests')
        .update({
          status: 'completed',
          completed_at: new Date().toISOString(),
          progress_data: progressData
        })
        .eq('id', userQuest.id);

      if (!updateError) {
        userQuest.status = 'completed';
        userQuest.completed_at = new Date().toISOString();
      }
    }

    return {
      statusCode: 200,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        user_quest: userQuest,
        progress: progressData,
        quest: userQuest.quest
      })
    };

  } catch (error) {
    console.error('Get quest progress error:', error);
    return {
      statusCode: 500,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ error: 'Failed to get quest progress' })
    };
  }
};

// Route requests to appropriate handlers
exports.handler = async (event, context) => {
  // Add CORS headers
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS'
  };

  // Handle preflight requests
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers,
      body: ''
    };
  }

  const path = event.path.replace('/.netlify/functions/quest-system', '');

  try {
    let response;

    if (event.httpMethod === 'GET') {
      if (path === '' || path === '/') {
        response = await getAvailableQuests(event);
      } else if (path.includes('/progress')) {
        response = await getQuestProgress(event);
      } else {
        response = {
          statusCode: 404,
          body: JSON.stringify({ error: 'Endpoint not found' })
        };
      }
    } else if (event.httpMethod === 'POST') {
      if (path.includes('/start')) {
        response = await startQuest(event);
      } else {
        response = {
          statusCode: 404,
          body: JSON.stringify({ error: 'Endpoint not found' })
        };
      }
    } else {
      response = {
        statusCode: 405,
        body: JSON.stringify({ error: 'Method not allowed' })
      };
    }

    // Add CORS headers to response
    response.headers = { ...response.headers, ...headers };
    return response;

  } catch (error) {
    console.error('Quest System API error:', error);
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ error: 'Internal server error' })
    };
  }
};
