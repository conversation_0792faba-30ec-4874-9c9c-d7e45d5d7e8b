// Escrow Release Management API
// Backend Specialist: Milestone-based releases and approval workflows
const { createClient } = require('@supabase/supabase-js');

// Supabase client
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_KEY
);

// Helper function to get user from request
const getUserFromRequest = (event) => {
  const authHeader = event.headers.authorization;
  if (!authHeader) return null;
  
  try {
    const token = authHeader.replace('Bearer ', '');
    const payload = JSON.parse(Buffer.from(token.split('.')[1], 'base64').toString());
    return payload.sub;
  } catch {
    return null;
  }
};

// Request Escrow Release
const requestEscrowRelease = async (event) => {
  try {
    const userId = getUserFromRequest(event);
    if (!userId) {
      return {
        statusCode: 401,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    const data = JSON.parse(event.body);
    
    // Validate required fields
    if (!data.escrow_id || !data.amount || !data.milestone_reference) {
      return {
        statusCode: 400,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          error: 'escrow_id, amount, and milestone_reference are required' 
        })
      };
    }

    // Get escrow account details
    const { data: escrow, error: escrowError } = await supabase
      .from('financial_transactions')
      .select(`
        *,
        projects(
          id,
          name,
          created_by,
          teams(
            team_members(user_id, role)
          )
        )
      `)
      .eq('id', data.escrow_id)
      .eq('transaction_type', 'escrow_creation')
      .single();

    if (escrowError) {
      return {
        statusCode: 404,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Escrow account not found' })
      };
    }

    // Check if user can request release (project member or beneficiary)
    const canRequest = escrow.payee_user_id === userId ||
      (escrow.projects?.teams?.team_members?.some(m => m.user_id === userId));

    if (!canRequest) {
      return {
        statusCode: 403,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Not authorized to request release from this escrow' })
      };
    }

    // Validate release amount
    const requestAmount = parseFloat(data.amount);
    if (requestAmount <= 0 || requestAmount > escrow.gross_amount) {
      return {
        statusCode: 400,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Invalid release amount' })
      };
    }

    // Check available balance (calculate from previous releases)
    const { data: previousReleases } = await supabase
      .from('financial_transactions')
      .select('gross_amount')
      .like('reference_number', `ESCROW-RELEASE-${data.escrow_id}%`)
      .eq('status', 'completed');

    const totalReleased = previousReleases?.reduce((sum, r) => sum + r.gross_amount, 0) || 0;
    const availableBalance = escrow.gross_amount - totalReleased;

    if (requestAmount > availableBalance) {
      return {
        statusCode: 400,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          error: `Insufficient escrow balance. Available: $${availableBalance}` 
        })
      };
    }

    // Create release request
    const releaseData = {
      company_id: escrow.company_id,
      project_id: escrow.project_id,
      transaction_type: 'escrow_release',
      transaction_category: 'milestone_payment',
      gross_amount: requestAmount,
      currency: escrow.currency,
      payee_user_id: data.recipient_id || userId,
      description: `Escrow release: ${data.milestone_reference} - ${data.release_reason || 'Milestone completion'}`,
      reference_number: `ESCROW-RELEASE-${data.escrow_id}-${Date.now()}`,
      created_by: userId,
      status: 'pending_approval'
    };

    const { data: release, error: releaseError } = await supabase
      .from('financial_transactions')
      .insert([releaseData])
      .select()
      .single();

    if (releaseError) {
      throw new Error(`Failed to create release request: ${releaseError.message}`);
    }

    // Store release metadata
    const releaseMetadata = {
      revenue_id: release.id,
      project_id: escrow.project_id,
      amount: requestAmount,
      currency: escrow.currency,
      escrow_date: new Date().toISOString().split('T')[0],
      status: 'pending_approval',
      reason: `Release request: ${data.milestone_reference}`,
      release_condition: JSON.stringify({
        milestone_reference: data.milestone_reference,
        release_reason: data.release_reason,
        evidence_urls: data.evidence_urls || [],
        escrow_id: data.escrow_id
      }),
      created_by: userId
    };

    const { data: metadata, error: metadataError } = await supabase
      .from('revenue_escrow')
      .insert([releaseMetadata])
      .select()
      .single();

    if (metadataError) {
      // Rollback release creation
      await supabase.from('financial_transactions').delete().eq('id', release.id);
      throw new Error(`Failed to create release metadata: ${metadataError.message}`);
    }

    // Notify escrow approvers
    await notifyEscrowApprovers(escrow, release, data.milestone_reference);

    return {
      statusCode: 201,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        release: {
          id: release.id,
          escrow_id: data.escrow_id,
          amount: requestAmount,
          milestone_reference: data.milestone_reference,
          release_reason: data.release_reason,
          recipient_id: data.recipient_id || userId,
          status: 'pending_approval',
          created_at: release.created_at,
          metadata_id: metadata.id
        }
      })
    };

  } catch (error) {
    console.error('Request escrow release error:', error);
    return {
      statusCode: 500,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ error: 'Failed to request escrow release' })
    };
  }
};

// Approve/Reject Escrow Release
const processEscrowRelease = async (event) => {
  try {
    const userId = getUserFromRequest(event);
    if (!userId) {
      return {
        statusCode: 401,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    const releaseId = event.path.split('/').pop();
    const data = JSON.parse(event.body);
    
    // Validate action
    if (!['approve', 'reject'].includes(data.action)) {
      return {
        statusCode: 400,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Action must be approve or reject' })
      };
    }

    // Get release details
    const { data: release, error: releaseError } = await supabase
      .from('financial_transactions')
      .select(`
        *,
        projects(
          id,
          created_by,
          teams(
            team_members(user_id, role)
          )
        )
      `)
      .eq('id', releaseId)
      .eq('transaction_type', 'escrow_release')
      .single();

    if (releaseError) {
      return {
        statusCode: 404,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Release request not found' })
      };
    }

    // Check if user can approve (project owner or alliance admin)
    const canApprove = release.projects.created_by === userId ||
      (release.projects.teams?.team_members?.some(m => 
        m.user_id === userId && ['founder', 'owner', 'admin'].includes(m.role)
      ));

    if (!canApprove) {
      return {
        statusCode: 403,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Not authorized to approve releases for this project' })
      };
    }

    // Update release status
    const newStatus = data.action === 'approve' ? 'approved' : 'rejected';
    const updateData = {
      status: newStatus,
      updated_at: new Date().toISOString()
    };

    if (data.action === 'approve') {
      updateData.approved_at = new Date().toISOString();
      updateData.approved_by = userId;
    }

    const { data: updatedRelease, error: updateError } = await supabase
      .from('financial_transactions')
      .update(updateData)
      .eq('id', releaseId)
      .select()
      .single();

    if (updateError) {
      throw new Error(`Failed to update release: ${updateError.message}`);
    }

    // Update metadata
    await supabase
      .from('revenue_escrow')
      .update({ 
        status: newStatus,
        released_by: userId
      })
      .eq('revenue_id', releaseId);

    // If approved, initiate payment transfer
    if (data.action === 'approve') {
      await initiateEscrowPayment(updatedRelease);
    }

    // Notify requester of decision
    await notifyReleaseDecision(updatedRelease, data.action, data.reason);

    return {
      statusCode: 200,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        release: {
          id: updatedRelease.id,
          status: newStatus,
          approved_by: data.action === 'approve' ? userId : null,
          approved_at: updateData.approved_at,
          rejection_reason: data.action === 'reject' ? data.reason : null
        }
      })
    };

  } catch (error) {
    console.error('Process escrow release error:', error);
    return {
      statusCode: 500,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ error: 'Failed to process escrow release' })
    };
  }
};

// Get Release Status
const getReleaseStatus = async (event) => {
  try {
    const userId = getUserFromRequest(event);
    if (!userId) {
      return {
        statusCode: 401,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    const releaseId = event.path.split('/').pop();

    const { data: release, error: releaseError } = await supabase
      .from('financial_transactions')
      .select(`
        *,
        projects(
          id,
          name,
          title
        ),
        payee:users!financial_transactions_payee_user_id_fkey(
          id,
          display_name,
          email
        ),
        creator:users!financial_transactions_created_by_fkey(
          id,
          display_name,
          email
        )
      `)
      .eq('id', releaseId)
      .eq('transaction_type', 'escrow_release')
      .single();

    if (releaseError) {
      return {
        statusCode: 404,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Release not found' })
      };
    }

    // Check access permissions
    const hasAccess = release.created_by === userId ||
      release.payee_user_id === userId ||
      release.approved_by === userId;

    if (!hasAccess) {
      return {
        statusCode: 403,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Access denied' })
      };
    }

    // Get metadata
    const { data: metadata } = await supabase
      .from('revenue_escrow')
      .select('*')
      .eq('revenue_id', releaseId)
      .single();

    const releaseConditions = metadata ? JSON.parse(metadata.release_condition || '{}') : {};

    const response = {
      id: release.id,
      escrow_id: releaseConditions.escrow_id,
      amount: release.gross_amount,
      milestone_reference: releaseConditions.milestone_reference,
      release_reason: releaseConditions.release_reason,
      evidence_urls: releaseConditions.evidence_urls || [],
      status: release.status,
      project: release.projects ? {
        id: release.projects.id,
        name: release.projects.name || release.projects.title
      } : null,
      recipient: release.payee,
      requester: release.creator,
      created_at: release.created_at,
      approved_at: release.approved_at,
      approved_by: release.approved_by
    };

    return {
      statusCode: 200,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(response)
    };

  } catch (error) {
    console.error('Get release status error:', error);
    return {
      statusCode: 500,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ error: 'Failed to get release status' })
    };
  }
};

// Helper function to notify escrow approvers
const notifyEscrowApprovers = async (escrow, release, milestone) => {
  try {
    console.log('Notifying escrow approvers:', {
      escrow_id: escrow.id,
      release_id: release.id,
      milestone: milestone
    });
    
    // TODO: Implement actual notification system
    // - Email notifications to project owners/alliance admins
    // - In-app notifications
    // - Slack/Discord integration
    
  } catch (error) {
    console.error('Notify approvers error:', error);
  }
};

// Helper function to notify release decision
const notifyReleaseDecision = async (release, action, reason) => {
  try {
    console.log('Notifying release decision:', {
      release_id: release.id,
      action: action,
      reason: reason
    });
    
    // TODO: Implement notification to requester
    
  } catch (error) {
    console.error('Notify decision error:', error);
  }
};

// Helper function to initiate payment
const initiateEscrowPayment = async (release) => {
  try {
    console.log('Initiating escrow payment:', {
      release_id: release.id,
      amount: release.gross_amount,
      recipient: release.payee_user_id
    });
    
    // TODO: Integrate with Teller payment system
    // This would call the teller-payments API to process the actual transfer
    
    // For now, mark as processing
    await supabase
      .from('financial_transactions')
      .update({ status: 'processing' })
      .eq('id', release.id);
    
  } catch (error) {
    console.error('Initiate payment error:', error);
  }
};

// Route requests to appropriate handlers
exports.handler = async (event, context) => {
  // Add CORS headers
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS'
  };

  // Handle preflight requests
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers,
      body: ''
    };
  }

  const path = event.path.replace('/.netlify/functions/escrow-releases', '');

  try {
    let response;

    if (event.httpMethod === 'POST') {
      if (path === '' || path === '/') {
        response = await requestEscrowRelease(event);
      } else if (path.includes('/process')) {
        response = await processEscrowRelease(event);
      } else {
        response = {
          statusCode: 404,
          body: JSON.stringify({ error: 'Endpoint not found' })
        };
      }
    } else if (event.httpMethod === 'GET') {
      response = await getReleaseStatus(event);
    } else if (event.httpMethod === 'PUT') {
      response = await processEscrowRelease(event);
    } else {
      response = {
        statusCode: 405,
        body: JSON.stringify({ error: 'Method not allowed' })
      };
    }

    // Add CORS headers to response
    response.headers = { ...response.headers, ...headers };
    return response;

  } catch (error) {
    console.error('Escrow Releases API error:', error);
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ error: 'Internal server error' })
    };
  }
};
