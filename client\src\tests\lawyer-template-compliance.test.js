/**
 * Lawyer Template Compliance Tests
 * 
 * Tests the new enhanced agreement generation system against the lawyer-approved template
 * for comprehensive compliance validation.
 */

import { describe, it, expect, beforeEach } from 'vitest';
import { dynamicContributorManagement } from '../utils/agreement/dynamicContributorManagement.js';
import { allianceCreationSystem } from '../utils/alliance/allianceCreationSystem.js';
import fs from 'fs';
import path from 'path';

describe('Lawyer Template Compliance Tests', () => {
  let testAlliance;
  let testVenture;
  let testContributor;
  let generatedAgreement;
  let lawyerTemplate;

  beforeEach(() => {
    // Load lawyer-approved template for comparison
    const templatePath = path.join(process.cwd(), 'public', 'example-cog-contributor-agreement.md');
    lawyerTemplate = fs.readFileSync(templatePath, 'utf8');

    // Create test alliance (no revenue model - ventures set their own)
    testAlliance = allianceCreationSystem.createAlliance({
      name: 'Test Legal Compliance Alliance',
      description: 'Alliance for testing lawyer template compliance',
      industry: 'TECHNOLOGY',
      jurisdiction: 'Delaware',
      currency: 'USD',
      governanceModel: 'DEMOCRATIC',
      votingThreshold: 0.6,
      platformFeePercentage: 10,
      allianceAdminFee: 2,
      foundingMembers: [
        {
          email: '<EMAIL>',
          role: 'Technical Lead',
          expertise: ['Software Architecture', 'Legal Compliance'],
          commitmentLevel: 'FULL_TIME'
        }
      ]
    });

    // Create test venture with unified pool
    testVenture = dynamicContributorManagement.initializeScalableVenture({
      name: 'Legal Compliance Test Venture',
      description: 'Enterprise software platform for testing legal agreement compliance with comprehensive feature set including AI, analytics, and collaboration tools.',
      allianceId: testAlliance.id,
      coreTeam: [
        {
          email: '<EMAIL>',
          role: 'Chief Executive Officer',
          responsibilities: [
            'Business strategy and market positioning',
            'Legal compliance and risk management',
            'Investor relations and fundraising',
            'Strategic partnerships and alliances'
          ],
          ipRights: 'co_owner',
          expertise: ['Business Strategy', 'Legal Compliance', 'Fundraising']
        }
      ],
      maxContributors: 20,
      allowDynamicJoining: true,
      requireApprovalForNewContributors: true,
      autoGenerateAgreements: true
    });

    // Add test contributor
    testContributor = dynamicContributorManagement.addGigworkContributor(testVenture.id, {
      email: '<EMAIL>',
      role: 'Senior Software Engineer',
      skills: ['React', 'Node.js', 'PostgreSQL', 'AWS', 'Legal Tech'],
      experienceLevel: 'senior',
      platformRating: 4.8,
      completedProjects: 25,
      responsibilities: [
        'Full-stack application development with modern frameworks',
        'Database design and optimization for enterprise scale',
        'Cloud infrastructure setup and security implementation',
        'Legal compliance feature development and testing'
      ],
      participationModel: 'continuous',
      hourlyRate: 160,
      expectedContributionLevel: 'high'
    });

    // Generate agreement for testing
    generatedAgreement = dynamicContributorManagement.generateGigworkAgreement(
      testVenture.id,
      testContributor.id,
      {
        governingLaw: 'Delaware',
        jurisdiction: 'Delaware',
        confidentialityPeriod: 24
      }
    );
  });

  describe('Alliance System Tests', () => {
    it('Should create alliance without revenue models (ventures set their own)', () => {
      expect(testAlliance).toBeDefined();
      expect(testAlliance.name).toBe('Test Legal Compliance Alliance');
      expect(testAlliance.jurisdiction).toBe('Delaware');
      expect(testAlliance.platformFeePercentage).toBe(10);
      expect(testAlliance.allianceAdminFee).toBe(2);
      
      // Alliance should NOT have revenue models (that's for ventures)
      expect(testAlliance.revenueModel).toBeUndefined();
      expect(testAlliance.allianceRevenueShare).toBeUndefined();
      
      console.log('\n🏢 ALLIANCE CREATED:');
      console.log(`   Name: ${testAlliance.name}`);
      console.log(`   Jurisdiction: ${testAlliance.jurisdiction}`);
      console.log(`   Platform Fee: ${testAlliance.platformFeePercentage}%`);
      console.log(`   Admin Fee: ${testAlliance.allianceAdminFee}%`);
      console.log(`   Founding Members: ${testAlliance.foundingMembers.length}`);
    });
  });

  describe('Venture System Tests', () => {
    it('Should create venture with unified pool revenue model', () => {
      expect(testVenture).toBeDefined();
      expect(testVenture.name).toBe('Legal Compliance Test Venture');
      expect(testVenture.revenueModel.calculationMethod).toBe('contribution_points');
      expect(testVenture.revenueModel.gigworkPoolPercentage).toBe(90);
      expect(testVenture.contributorPools.coreTeam.length).toBe(1);
      
      console.log('\n🚀 VENTURE CREATED:');
      console.log(`   Name: ${testVenture.name}`);
      console.log(`   Revenue Model: ${testVenture.revenueModel.calculationMethod}`);
      console.log(`   Unified Pool: ${testVenture.revenueModel.gigworkPoolPercentage}%`);
      console.log(`   Core Team: ${testVenture.contributorPools.coreTeam.length}`);
      console.log(`   Alliance: ${testVenture.allianceId}`);
    });

    it('Should add contributor successfully', () => {
      expect(testContributor).toBeDefined();
      expect(testContributor.email).toBe('<EMAIL>');
      expect(testContributor.role).toBe('Senior Software Engineer');
      expect(testContributor.experienceLevel).toBe('senior');
      
      console.log('\n👤 CONTRIBUTOR ADDED:');
      console.log(`   Email: ${testContributor.email}`);
      console.log(`   Role: ${testContributor.role}`);
      console.log(`   Experience: ${testContributor.experienceLevel}`);
      console.log(`   Rating: ${testContributor.gigworkProfile.platformRating}/5.0`);
    });
  });

  describe('Agreement Generation Tests', () => {
    it('Should generate comprehensive legal agreement', () => {
      expect(generatedAgreement).toBeDefined();
      expect(generatedAgreement.content).toBeDefined();
      expect(generatedAgreement.content.length).toBeGreaterThan(20000); // Comprehensive agreement
      
      console.log('\n📄 AGREEMENT GENERATED:');
      console.log(`   Length: ${generatedAgreement.content.length.toLocaleString()} characters`);
      console.log(`   Word Count: ~${Math.round(generatedAgreement.content.split(' ').length).toLocaleString()} words`);
      console.log(`   Sections: ${(generatedAgreement.content.match(/^##\s/gm) || []).length}`);
    });

    it('Should include all critical legal provisions from lawyer template', () => {
      const criticalProvisions = [
        // Core sections
        'Definitions',
        'Treatment of Confidential Information',
        'Ownership of Work Product',
        'Non-Disparagement',
        'Termination',
        'Equitable Remedies',
        'Assignment',
        'Waivers and Amendments',
        'Survival',
        'Status as Independent Contractor',
        'Representations and Warranties',
        'Indemnification',
        'Entire Agreement',
        'Governing Law',
        'Consent to Jurisdiction',
        'Settlement of Disputes',
        'Severability',
        
        // Enhanced provisions
        'Restrictive Covenants',
        'Non-solicitation',
        'Non-competition',
        'Further Assurances',
        'Maintenance of Records',
        'Ordered Disclosure',
        'Third Party Information',
        'Proprietary Employer Information'
      ];

      console.log('\n⚖️ LEGAL PROVISIONS ANALYSIS:');
      let presentCount = 0;
      const missingProvisions = [];

      criticalProvisions.forEach(provision => {
        const isPresent = generatedAgreement.content.includes(provision);
        if (isPresent) {
          presentCount++;
        } else {
          missingProvisions.push(provision);
        }
        console.log(`   ${provision}: ${isPresent ? '✅' : '❌'}`);
      });

      const compliancePercentage = (presentCount / criticalProvisions.length) * 100;
      console.log(`\n📊 COMPLIANCE SCORE: ${compliancePercentage.toFixed(1)}% (${presentCount}/${criticalProvisions.length})`);
      
      if (missingProvisions.length > 0) {
        console.log(`❌ Missing: ${missingProvisions.join(', ')}`);
      }

      // Should have 95%+ compliance
      expect(compliancePercentage).toBeGreaterThanOrEqual(95);
      expect(presentCount).toBeGreaterThanOrEqual(Math.ceil(criticalProvisions.length * 0.95));
    });

    it('Should include comprehensive legal definitions', () => {
      const requiredDefinitions = [
        'Background IP',
        'Confidential Information',
        'Confidential Documents',
        'Contribution',
        'Developed IP',
        'Work Product',
        'Revenue Tranche',
        'Contribution Points',
        'Intellectual Property Rights',
        'Governmental Authority',
        'Launch',
        'Milestones',
        'Person',
        'Programs',
        'Specification',
        'Termination Date',
        'Work Product Management'
      ];

      console.log('\n📚 LEGAL DEFINITIONS ANALYSIS:');
      let definitionsPresent = 0;
      const missingDefinitions = [];

      requiredDefinitions.forEach(definition => {
        const isPresent = generatedAgreement.content.includes(`"${definition}"`);
        if (isPresent) {
          definitionsPresent++;
        } else {
          missingDefinitions.push(definition);
        }
        console.log(`   "${definition}": ${isPresent ? '✅' : '❌'}`);
      });

      console.log(`\n📊 DEFINITIONS SCORE: ${definitionsPresent}/${requiredDefinitions.length}`);
      
      if (missingDefinitions.length > 0) {
        console.log(`❌ Missing Definitions: ${missingDefinitions.join(', ')}`);
      }

      // Should have most definitions present
      expect(definitionsPresent).toBeGreaterThanOrEqual(15);
    });

    it('Should include unified pool revenue model correctly', () => {
      const unifiedPoolFeatures = [
        'Unified Contribution Pool',
        'All Contributors',
        'contribution points',
        'Revenue Distribution Model',
        '90% of total venture revenue'
      ];

      console.log('\n🎯 UNIFIED POOL FEATURES:');
      let featuresPresent = 0;

      unifiedPoolFeatures.forEach(feature => {
        const isPresent = generatedAgreement.content.toLowerCase().includes(feature.toLowerCase());
        if (isPresent) featuresPresent++;
        console.log(`   ${feature}: ${isPresent ? '✅' : '❌'}`);
      });

      console.log(`\n📊 UNIFIED POOL SCORE: ${featuresPresent}/${unifiedPoolFeatures.length}`);

      // All unified pool features should be present
      expect(featuresPresent).toBe(unifiedPoolFeatures.length);
    });
  });

  describe('Lawyer Template Comparison', () => {
    it('Should analyze structural differences with lawyer template', () => {
      console.log('\n🔍 LAWYER TEMPLATE COMPARISON:');
      
      // Count sections in both documents
      const generatedSections = (generatedAgreement.content.match(/^##\s/gm) || []).length;
      const templateSections = (lawyerTemplate.match(/^##\s/gm) || []).length;
      
      console.log(`   Generated Sections: ${generatedSections}`);
      console.log(`   Template Sections: ${templateSections}`);
      
      // Count definitions
      const generatedDefs = (generatedAgreement.content.match(/\*\*"[^"]+"\*\*/g) || []).length;
      const templateDefs = (lawyerTemplate.match(/\*\*"[^"]+"\*\*/g) || []).length;
      
      console.log(`   Generated Definitions: ${generatedDefs}`);
      console.log(`   Template Definitions: ${templateDefs}`);
      
      // Length comparison
      console.log(`   Generated Length: ${generatedAgreement.content.length.toLocaleString()} chars`);
      console.log(`   Template Length: ${lawyerTemplate.length.toLocaleString()} chars`);
      
      // Should be comprehensive
      expect(generatedSections).toBeGreaterThanOrEqual(18);
      expect(generatedDefs).toBeGreaterThanOrEqual(15);
      expect(generatedAgreement.content.length).toBeGreaterThan(20000);
    });

    it('Should include lawyer template key phrases', () => {
      const keyLawyerPhrases = [
        'irrevocably designates and appoints',
        'attorney-in-fact',
        'Restricted Business',
        'Restricted Territory',
        'Service Provider',
        'Company Customer',
        'Company Vendor',
        'Third Party Confidential Information',
        'Proprietary Information',
        'protective order',
        'binding arbitration',
        'Commercial Arbitration Rules'
      ];

      console.log('\n⚖️ LAWYER TEMPLATE KEY PHRASES:');
      let phrasesPresent = 0;

      keyLawyerPhrases.forEach(phrase => {
        const isPresent = generatedAgreement.content.includes(phrase);
        if (isPresent) phrasesPresent++;
        console.log(`   "${phrase}": ${isPresent ? '✅' : '❌'}`);
      });

      const phraseCompliance = (phrasesPresent / keyLawyerPhrases.length) * 100;
      console.log(`\n📊 PHRASE COMPLIANCE: ${phraseCompliance.toFixed(1)}% (${phrasesPresent}/${keyLawyerPhrases.length})`);

      // Should have high phrase compliance
      expect(phraseCompliance).toBeGreaterThanOrEqual(80);
    });
  });

  describe('Output Generation for Analysis', () => {
    it('Should save generated content for manual review', () => {
      const outputDir = path.join(process.cwd(), 'test-output');
      if (!fs.existsSync(outputDir)) {
        fs.mkdirSync(outputDir, { recursive: true });
      }

      // Save alliance details
      const allianceOutput = `# TEST ALLIANCE OUTPUT
## ${testAlliance.name}

**Industry:** ${testAlliance.industry}
**Jurisdiction:** ${testAlliance.jurisdiction}
**Platform Fee:** ${testAlliance.platformFeePercentage}%
**Admin Fee:** ${testAlliance.allianceAdminFee}%
**Governance:** ${testAlliance.governanceModel}
**Voting Threshold:** ${testAlliance.votingThreshold * 100}%

## Founding Members
${testAlliance.foundingMembers.map(member => 
  `- **${member.email}** (${member.role}): ${member.expertise.join(', ')}`
).join('\n')}

Generated: ${new Date().toISOString()}
`;

      // Save venture details
      const ventureOutput = `# TEST VENTURE OUTPUT
## ${testVenture.name}

**Description:** ${testVenture.description}
**Alliance:** ${testAlliance.name}
**Revenue Model:** ${testVenture.revenueModel.calculationMethod}
**Unified Pool:** ${testVenture.revenueModel.gigworkPoolPercentage}%
**Platform Fee:** ${testVenture.revenueModel.platformFeePercentage}%

## Core Team
${testVenture.contributorPools.coreTeam.map(member => 
  `### ${member.role}
**Email:** ${member.email}
**Responsibilities:** ${member.responsibilities.join(', ')}
**IP Rights:** ${member.ipRights}`
).join('\n\n')}

## Contributors
${testVenture.contributorPools.gigwork.map(member => 
  `### ${member.role}
**Email:** ${member.email}
**Experience:** ${member.experienceLevel}
**Rating:** ${member.gigworkProfile.platformRating}/5.0`
).join('\n\n')}

Generated: ${new Date().toISOString()}
`;

      // Save files
      fs.writeFileSync(path.join(outputDir, 'test-alliance.md'), allianceOutput);
      fs.writeFileSync(path.join(outputDir, 'test-venture.md'), ventureOutput);
      fs.writeFileSync(path.join(outputDir, 'test-agreement.md'), generatedAgreement.content);

      console.log('\n📁 OUTPUT FILES SAVED:');
      console.log(`   Alliance: test-output/test-alliance.md`);
      console.log(`   Venture: test-output/test-venture.md`);
      console.log(`   Agreement: test-output/test-agreement.md`);

      // Verify files exist
      expect(fs.existsSync(path.join(outputDir, 'test-alliance.md'))).toBe(true);
      expect(fs.existsSync(path.join(outputDir, 'test-venture.md'))).toBe(true);
      expect(fs.existsSync(path.join(outputDir, 'test-agreement.md'))).toBe(true);
    });
  });
});
