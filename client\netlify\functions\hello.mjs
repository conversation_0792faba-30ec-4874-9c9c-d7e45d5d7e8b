// Modern ES modules hello function
export default async (req, context) => {
  // Set CORS headers for cross-origin access
  const headers = {
    'Content-Type': 'application/json',
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type',
    'Access-Control-Allow-Methods': 'GET, OPTIONS'
  };

  // Handle OPTIONS request for CORS preflight
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      status: 204,
      headers
    });
  }

  return new Response(
    JSON.stringify({
      message: "Hello from Netlify Functions (ES modules)!",
      timestamp: new Date().toISOString()
    }),
    { headers }
  );
};

// Configure the function path
export const config = {
  path: "/api/hello"
};
