// Endorsements API
// Handles skill endorsements and professional validation
// Based on docs/design-system/systems/social-system.md

const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_ANON_KEY;

const headers = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Content-Type': 'application/json'
};

exports.handler = async (event, context) => {
  // Handle CORS preflight
  if (event.httpMethod === 'OPTIONS') {
    return { statusCode: 200, headers, body: '' };
  }

  try {
    const supabase = createClient(supabaseUrl, supabaseKey);
    
    // Get user from authorization header
    const authHeader = event.headers.authorization;
    if (!authHeader) {
      return {
        statusCode: 401,
        headers,
        body: JSON.stringify({ error: 'Authorization header required' })
      };
    }

    const token = authHeader.replace('Bearer ', '');
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    
    if (authError || !user) {
      return {
        statusCode: 401,
        headers,
        body: JSON.stringify({ error: 'Invalid authentication token' })
      };
    }

    const { httpMethod, path } = event;
    const pathParts = path.split('/').filter(part => part);
    
    switch (httpMethod) {
      case 'GET':
        return await handleGet(supabase, user, pathParts, event.queryStringParameters);
      case 'POST':
        return await handlePost(supabase, user, pathParts, JSON.parse(event.body || '{}'));
      default:
        return {
          statusCode: 405,
          headers,
          body: JSON.stringify({ error: 'Method not allowed' })
        };
    }
  } catch (error) {
    console.error('API Error:', error);
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ error: 'Internal server error', details: error.message })
    };
  }
};

// GET handlers
async function handleGet(supabase, user, pathParts, queryParams) {
  if (pathParts.length === 2) {
    // GET /endorsements/:userId - get user's endorsements
    const userId = pathParts[1];
    return await getUserEndorsements(supabase, userId, queryParams);
  } else if (pathParts.length === 1) {
    // GET /endorsements - get current user's endorsements
    return await getUserEndorsements(supabase, user.id, queryParams);
  }
  
  return {
    statusCode: 404,
    headers,
    body: JSON.stringify({ error: 'Endpoint not found' })
  };
}

// Get user's endorsements
async function getUserEndorsements(supabase, userId, queryParams) {
  const { type = 'received', limit = 50, offset = 0, skill_category } = queryParams || {};
  
  let query = supabase
    .from('skill_endorsements')
    .select(`
      id,
      skill_name,
      skill_category,
      proficiency_level,
      endorsement_message,
      collaboration_context,
      is_verified,
      created_at,
      endorser:endorser_id(id, display_name, email),
      endorsed:endorsed_id(id, display_name, email)
    `)
    .order('created_at', { ascending: false })
    .range(offset, offset + limit - 1);

  if (type === 'received') {
    query = query.eq('endorsed_id', userId);
  } else if (type === 'given') {
    query = query.eq('endorser_id', userId);
  }

  if (skill_category) {
    query = query.eq('skill_category', skill_category);
  }

  const { data, error } = await query;

  if (error) {
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ error: 'Failed to fetch endorsements', details: error.message })
    };
  }

  // Group endorsements by skill
  const skillGroups = {};
  data.forEach(endorsement => {
    const skillName = endorsement.skill_name;
    if (!skillGroups[skillName]) {
      skillGroups[skillName] = {
        skill_name: skillName,
        skill_category: endorsement.skill_category,
        endorsements: [],
        average_proficiency: 0,
        total_endorsements: 0,
        verified_count: 0
      };
    }
    
    skillGroups[skillName].endorsements.push(endorsement);
    skillGroups[skillName].total_endorsements++;
    if (endorsement.is_verified) {
      skillGroups[skillName].verified_count++;
    }
  });

  // Calculate averages
  Object.values(skillGroups).forEach(group => {
    const totalProficiency = group.endorsements.reduce((sum, e) => sum + (e.proficiency_level || 0), 0);
    group.average_proficiency = group.total_endorsements > 0 ? 
      Math.round((totalProficiency / group.total_endorsements) * 10) / 10 : 0;
  });

  return {
    statusCode: 200,
    headers,
    body: JSON.stringify({
      endorsements: data,
      skill_groups: Object.values(skillGroups),
      total: data.length,
      hasMore: data.length === limit
    })
  };
}

// POST handlers
async function handlePost(supabase, user, pathParts, body) {
  if (pathParts.length === 1) {
    // POST /endorsements - create endorsement
    return await createEndorsement(supabase, user.id, body);
  }
  
  return {
    statusCode: 404,
    headers,
    body: JSON.stringify({ error: 'Endpoint not found' })
  };
}

// Create skill endorsement
async function createEndorsement(supabase, userId, body) {
  const { 
    endorsed_id, 
    skill_name, 
    skill_category, 
    proficiency_level, 
    endorsement_message, 
    collaboration_context 
  } = body;
  
  if (!endorsed_id || !skill_name || !proficiency_level) {
    return {
      statusCode: 400,
      headers,
      body: JSON.stringify({ 
        error: 'endorsed_id, skill_name, and proficiency_level are required' 
      })
    };
  }

  if (proficiency_level < 1 || proficiency_level > 5) {
    return {
      statusCode: 400,
      headers,
      body: JSON.stringify({ 
        error: 'proficiency_level must be between 1 and 5' 
      })
    };
  }

  // Verify ally relationship
  const { data: allyCheck } = await supabase
    .from('user_allies')
    .select('id')
    .or(`and(user_id.eq.${userId},ally_id.eq.${endorsed_id}),and(user_id.eq.${endorsed_id},ally_id.eq.${userId})`)
    .eq('status', 'accepted')
    .single();

  if (!allyCheck) {
    return {
      statusCode: 403,
      headers,
      body: JSON.stringify({ error: 'Can only endorse allies' })
    };
  }

  // Check if endorsement already exists
  const { data: existing } = await supabase
    .from('skill_endorsements')
    .select('id')
    .eq('endorser_id', userId)
    .eq('endorsed_id', endorsed_id)
    .eq('skill_name', skill_name)
    .single();

  if (existing) {
    return {
      statusCode: 409,
      headers,
      body: JSON.stringify({ error: 'You have already endorsed this skill for this user' })
    };
  }

  const { data, error } = await supabase
    .from('skill_endorsements')
    .insert({
      endorser_id: userId,
      endorsed_id,
      skill_name,
      skill_category: skill_category || 'technical',
      proficiency_level,
      endorsement_message,
      collaboration_context
    })
    .select(`
      id,
      skill_name,
      skill_category,
      proficiency_level,
      endorsement_message,
      collaboration_context,
      is_verified,
      created_at,
      endorser:endorser_id(id, display_name),
      endorsed:endorsed_id(id, display_name)
    `)
    .single();

  if (error) {
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ error: 'Failed to create endorsement', details: error.message })
    };
  }

  return {
    statusCode: 201,
    headers,
    body: JSON.stringify({ 
      message: 'Endorsement created successfully', 
      endorsement: data 
    })
  };
}

// Get popular skills for endorsement suggestions
exports.getPopularSkills = async (supabase, queryParams) => {
  const { limit = 20, category } = queryParams || {};
  
  let query = supabase
    .from('skill_endorsements')
    .select('skill_name, skill_category')
    .order('created_at', { ascending: false });

  if (category) {
    query = query.eq('skill_category', category);
  }

  const { data, error } = await query;

  if (error) {
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ error: 'Failed to fetch popular skills', details: error.message })
    };
  }

  // Count skill occurrences
  const skillCounts = {};
  data.forEach(item => {
    const key = `${item.skill_name}|${item.skill_category}`;
    if (!skillCounts[key]) {
      skillCounts[key] = {
        skill_name: item.skill_name,
        skill_category: item.skill_category,
        count: 0
      };
    }
    skillCounts[key].count++;
  });

  const popularSkills = Object.values(skillCounts)
    .sort((a, b) => b.count - a.count)
    .slice(0, limit);

  return {
    statusCode: 200,
    headers,
    body: JSON.stringify({
      popular_skills: popularSkills,
      total: popularSkills.length
    })
  };
};
