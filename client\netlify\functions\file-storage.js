// File Storage Service
// Integration & Services Agent: AWS S3 and Cloudinary integration

const { createClient } = require('@supabase/supabase-js');
const AWS = require('aws-sdk');
const { v2: cloudinary } = require('cloudinary');
const multer = require('multer');
const crypto = require('crypto');

// Initialize Supabase client
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_ANON_KEY
);

// CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS'
};

// Configure AWS S3
const s3 = new AWS.S3({
  accessKeyId: process.env.AWS_ACCESS_KEY_ID,
  secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
  region: process.env.AWS_REGION || 'us-east-1'
});

// Configure Cloudinary
cloudinary.config({
  cloud_name: process.env.CLOUDINARY_CLOUD_NAME,
  api_key: process.env.CLOUDINARY_API_KEY,
  api_secret: process.env.CLOUDINARY_API_SECRET
});

// Authenticate user from JWT token
const authenticateUser = async (authHeader) => {
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    throw new Error('Missing or invalid authorization header');
  }

  const token = authHeader.substring(7);
  
  try {
    const { data: { user }, error } = await supabase.auth.getUser(token);
    
    if (error || !user) {
      throw new Error('Invalid authentication token');
    }
    
    return user;
  } catch (error) {
    console.error('Authentication error:', error);
    throw new Error('Authentication failed');
  }
};

// Generate secure file name
const generateSecureFileName = (originalName, userId) => {
  const timestamp = Date.now();
  const random = crypto.randomBytes(8).toString('hex');
  const extension = originalName.split('.').pop();
  return `${userId}/${timestamp}-${random}.${extension}`;
};

// Upload file to S3
const uploadToS3 = async (fileBuffer, fileName, contentType, userId) => {
  try {
    const bucketName = process.env.AWS_S3_BUCKET || 'royaltea-files';
    const key = generateSecureFileName(fileName, userId);
    
    const uploadParams = {
      Bucket: bucketName,
      Key: key,
      Body: fileBuffer,
      ContentType: contentType,
      ACL: 'private', // Files are private by default
      Metadata: {
        'uploaded-by': userId,
        'original-name': fileName,
        'upload-timestamp': Date.now().toString()
      }
    };
    
    const result = await s3.upload(uploadParams).promise();
    
    return {
      success: true,
      url: result.Location,
      key: result.Key,
      bucket: result.Bucket,
      etag: result.ETag,
      storage_provider: 's3'
    };
    
  } catch (error) {
    console.error('S3 upload error:', error);
    throw error;
  }
};

// Upload image to Cloudinary
const uploadToCloudinary = async (fileBuffer, fileName, userId, options = {}) => {
  try {
    const uploadOptions = {
      folder: `royaltea/${userId}`,
      public_id: generateSecureFileName(fileName, userId).replace(/\./g, '_'),
      resource_type: 'auto',
      ...options
    };
    
    return new Promise((resolve, reject) => {
      cloudinary.uploader.upload_stream(
        uploadOptions,
        (error, result) => {
          if (error) {
            reject(error);
          } else {
            resolve({
              success: true,
              url: result.secure_url,
              public_id: result.public_id,
              format: result.format,
              width: result.width,
              height: result.height,
              bytes: result.bytes,
              storage_provider: 'cloudinary'
            });
          }
        }
      ).end(fileBuffer);
    });
    
  } catch (error) {
    console.error('Cloudinary upload error:', error);
    throw error;
  }
};

// Generate presigned URL for S3 upload
const generatePresignedUploadUrl = async (user, fileName, contentType, expiresIn = 3600) => {
  try {
    const bucketName = process.env.AWS_S3_BUCKET || 'royaltea-files';
    const key = generateSecureFileName(fileName, user.id);
    
    const params = {
      Bucket: bucketName,
      Key: key,
      ContentType: contentType,
      Expires: expiresIn,
      ACL: 'private',
      Metadata: {
        'uploaded-by': user.id,
        'original-name': fileName
      }
    };
    
    const uploadUrl = await s3.getSignedUrlPromise('putObject', params);
    
    return {
      success: true,
      upload_url: uploadUrl,
      key: key,
      bucket: bucketName,
      expires_in: expiresIn
    };
    
  } catch (error) {
    console.error('Generate presigned URL error:', error);
    throw error;
  }
};

// Generate presigned URL for S3 download
const generatePresignedDownloadUrl = async (user, fileKey, expiresIn = 3600) => {
  try {
    const bucketName = process.env.AWS_S3_BUCKET || 'royaltea-files';
    
    // Verify user has access to this file
    const { data: fileRecord, error } = await supabase
      .from('file_uploads')
      .select('*')
      .eq('storage_key', fileKey)
      .single();
    
    if (error || !fileRecord) {
      throw new Error('File not found');
    }
    
    if (fileRecord.uploaded_by !== user.id && !fileRecord.is_public) {
      throw new Error('Access denied');
    }
    
    const params = {
      Bucket: bucketName,
      Key: fileKey,
      Expires: expiresIn
    };
    
    const downloadUrl = await s3.getSignedUrlPromise('getObject', params);
    
    return {
      success: true,
      download_url: downloadUrl,
      expires_in: expiresIn,
      file_info: fileRecord
    };
    
  } catch (error) {
    console.error('Generate download URL error:', error);
    throw error;
  }
};

// Store file metadata in database
const storeFileMetadata = async (user, fileData) => {
  try {
    const { data: fileRecord, error } = await supabase
      .from('file_uploads')
      .insert({
        uploaded_by: user.id,
        original_name: fileData.originalName,
        file_name: fileData.fileName,
        file_size: fileData.fileSize,
        content_type: fileData.contentType,
        storage_provider: fileData.storageProvider,
        storage_key: fileData.storageKey,
        storage_url: fileData.storageUrl,
        is_public: fileData.isPublic || false,
        metadata: fileData.metadata || {}
      })
      .select()
      .single();
    
    if (error) {
      throw new Error(`Failed to store file metadata: ${error.message}`);
    }
    
    return fileRecord;
    
  } catch (error) {
    console.error('Store file metadata error:', error);
    throw error;
  }
};

// Get user's files
const getUserFiles = async (user, queryParams) => {
  try {
    let query = supabase
      .from('file_uploads')
      .select('*')
      .eq('uploaded_by', user.id)
      .order('created_at', { ascending: false });
    
    // Apply filters
    if (queryParams.get('content_type')) {
      query = query.ilike('content_type', `${queryParams.get('content_type')}%`);
    }
    
    if (queryParams.get('search')) {
      query = query.ilike('original_name', `%${queryParams.get('search')}%`);
    }
    
    // Apply pagination
    const limit = Math.min(parseInt(queryParams.get('limit')) || 50, 100);
    const offset = parseInt(queryParams.get('offset')) || 0;
    
    query = query.range(offset, offset + limit - 1);
    
    const { data: files, error } = await query;
    
    if (error) {
      throw new Error(`Failed to get user files: ${error.message}`);
    }
    
    return files || [];
    
  } catch (error) {
    console.error('Get user files error:', error);
    throw error;
  }
};

// Delete file
const deleteFile = async (user, fileId) => {
  try {
    // Get file record
    const { data: fileRecord, error: fetchError } = await supabase
      .from('file_uploads')
      .select('*')
      .eq('id', fileId)
      .eq('uploaded_by', user.id)
      .single();
    
    if (fetchError || !fileRecord) {
      throw new Error('File not found or access denied');
    }
    
    // Delete from storage provider
    if (fileRecord.storage_provider === 's3') {
      await s3.deleteObject({
        Bucket: process.env.AWS_S3_BUCKET || 'royaltea-files',
        Key: fileRecord.storage_key
      }).promise();
    } else if (fileRecord.storage_provider === 'cloudinary') {
      await cloudinary.uploader.destroy(fileRecord.storage_key);
    }
    
    // Delete from database
    const { error: deleteError } = await supabase
      .from('file_uploads')
      .delete()
      .eq('id', fileId);
    
    if (deleteError) {
      throw new Error(`Failed to delete file record: ${deleteError.message}`);
    }
    
    return {
      success: true,
      message: 'File deleted successfully'
    };
    
  } catch (error) {
    console.error('Delete file error:', error);
    throw error;
  }
};

// Get storage usage statistics
const getStorageUsage = async (user) => {
  try {
    const { data: stats, error } = await supabase
      .from('file_uploads')
      .select('file_size, content_type, storage_provider')
      .eq('uploaded_by', user.id);
    
    if (error) {
      throw new Error(`Failed to get storage usage: ${error.message}`);
    }
    
    const usage = {
      total_files: stats.length,
      total_size: stats.reduce((sum, file) => sum + (file.file_size || 0), 0),
      by_type: {},
      by_provider: {}
    };
    
    stats.forEach(file => {
      const type = file.content_type?.split('/')[0] || 'unknown';
      usage.by_type[type] = (usage.by_type[type] || 0) + (file.file_size || 0);
      
      const provider = file.storage_provider || 'unknown';
      usage.by_provider[provider] = (usage.by_provider[provider] || 0) + (file.file_size || 0);
    });
    
    return usage;
    
  } catch (error) {
    console.error('Get storage usage error:', error);
    throw error;
  }
};

// Main handler function
exports.handler = async (event, context) => {
  // Handle CORS preflight
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers: corsHeaders,
      body: ''
    };
  }

  try {
    // Parse request path and method
    const pathParts = event.path.split('/').filter(Boolean);
    const action = pathParts[pathParts.length - 1];
    const httpMethod = event.httpMethod;
    const body = event.body ? JSON.parse(event.body) : {};
    const queryParams = new URLSearchParams(event.queryStringParameters || {});

    let result;

    switch (action) {
      case 'presigned-upload':
        if (httpMethod !== 'POST') {
          throw new Error('Method not allowed');
        }
        const user = await authenticateUser(event.headers.authorization);
        if (!body.file_name || !body.content_type) {
          throw new Error('file_name and content_type are required');
        }
        result = await generatePresignedUploadUrl(user, body.file_name, body.content_type, body.expires_in);
        break;

      case 'presigned-download':
        if (httpMethod !== 'POST') {
          throw new Error('Method not allowed');
        }
        const downloadUser = await authenticateUser(event.headers.authorization);
        if (!body.file_key) {
          throw new Error('file_key is required');
        }
        result = await generatePresignedDownloadUrl(downloadUser, body.file_key, body.expires_in);
        break;

      case 'files':
        if (httpMethod === 'GET') {
          const listUser = await authenticateUser(event.headers.authorization);
          result = await getUserFiles(listUser, queryParams);
        } else {
          throw new Error('Method not allowed');
        }
        break;

      case 'delete':
        if (httpMethod !== 'DELETE') {
          throw new Error('Method not allowed');
        }
        const deleteUser = await authenticateUser(event.headers.authorization);
        const fileId = queryParams.get('file_id');
        if (!fileId) {
          throw new Error('file_id is required');
        }
        result = await deleteFile(deleteUser, fileId);
        break;

      case 'usage':
        if (httpMethod !== 'GET') {
          throw new Error('Method not allowed');
        }
        const usageUser = await authenticateUser(event.headers.authorization);
        result = await getStorageUsage(usageUser);
        break;

      default:
        throw new Error('Invalid action');
    }

    return {
      statusCode: 200,
      headers: corsHeaders,
      body: JSON.stringify({
        success: true,
        data: result,
        timestamp: new Date().toISOString()
      })
    };

  } catch (error) {
    console.error('File Storage API error:', error);
    
    return {
      statusCode: error.message.includes('Authentication') ? 401 : 
                  error.message.includes('not allowed') ? 405 : 400,
      headers: corsHeaders,
      body: JSON.stringify({
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      })
    };
  }
};
