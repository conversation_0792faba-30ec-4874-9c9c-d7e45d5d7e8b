import { test, expect } from '@playwright/test';

test.describe('Production Dashboard Navigation', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate directly to production site
    await page.goto('https://royalty.technology');
    
    // Login with test credentials
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'TestPassword123!');
    await page.click('button[type="submit"]');
    
    // Wait for login to complete
    await page.waitForSelector('text=Welcome back', { timeout: 15000 });
    
    // Handle experimental navigation - check if we need to go to content view
    const backToContentButton = page.locator('button:has-text("📄")');
    if (await backToContentButton.isVisible()) {
      await backToContentButton.click();
      await page.waitForTimeout(1000);
    }
    
    // Wait for dashboard buttons to be available
    await page.waitForSelector('button:has-text("Start New Project")', { timeout: 10000 });
    await page.waitForTimeout(2000);
  });

  test('Dashboard buttons are clickable and trigger navigation', async ({ page }) => {
    const initialUrl = page.url();
    console.log(`Initial URL: ${initialUrl}`);
    
    // Test Start New Project button
    console.log('Testing Start New Project button...');
    const startButton = page.locator('button:has-text("Start New Project")');
    await expect(startButton).toBeVisible();
    await startButton.click({ force: true });
    await page.waitForTimeout(3000);
    
    let currentUrl = page.url();
    console.log(`After Start button click: ${currentUrl}`);
    
    // Check if navigation occurred (URL change or content change)
    const startNavigationWorked = currentUrl !== initialUrl || 
                                 await page.locator('text=Start').first().isVisible() ||
                                 await page.locator('text=Project').first().isVisible();
    
    expect(startNavigationWorked).toBeTruthy();
    await page.screenshot({ path: 'test-results/start-button-result.png' });
    
    // Go back to dashboard
    await page.goto('https://royalty.technology');
    const backToContentButton1 = page.locator('button:has-text("📄")');
    if (await backToContentButton1.isVisible()) {
      await backToContentButton1.click();
      await page.waitForTimeout(1000);
    }
    await page.waitForSelector('button:has-text("Track Contribution")', { timeout: 10000 });
    await page.waitForTimeout(1000);
    
    // Test Track Contribution button
    console.log('Testing Track Contribution button...');
    const trackButton = page.locator('button:has-text("Track Contribution")');
    await expect(trackButton).toBeVisible();
    await trackButton.click({ force: true });
    await page.waitForTimeout(3000);
    
    currentUrl = page.url();
    console.log(`After Track button click: ${currentUrl}`);
    
    const trackNavigationWorked = currentUrl !== initialUrl || 
                                 await page.locator('text=Track').first().isVisible() ||
                                 await page.locator('text=Contribution').first().isVisible();
    
    expect(trackNavigationWorked).toBeTruthy();
    await page.screenshot({ path: 'test-results/track-button-result.png' });
    
    // Go back to dashboard
    await page.goto('https://royalty.technology');
    const backToContentButton2 = page.locator('button:has-text("📄")');
    if (await backToContentButton2.isVisible()) {
      await backToContentButton2.click();
      await page.waitForTimeout(1000);
    }
    await page.waitForSelector('button:has-text("View Analytics")', { timeout: 10000 });
    await page.waitForTimeout(1000);
    
    // Test View Analytics button
    console.log('Testing View Analytics button...');
    const analyticsButton = page.locator('button:has-text("View Analytics")');
    await expect(analyticsButton).toBeVisible();
    await analyticsButton.click({ force: true });
    await page.waitForTimeout(3000);
    
    currentUrl = page.url();
    console.log(`After Analytics button click: ${currentUrl}`);
    
    const analyticsNavigationWorked = currentUrl !== initialUrl || 
                                     await page.locator('text=Analytics').first().isVisible() ||
                                     await page.locator('text=Insights').first().isVisible();
    
    expect(analyticsNavigationWorked).toBeTruthy();
    await page.screenshot({ path: 'test-results/analytics-button-result.png' });
    
    console.log('✅ All dashboard buttons are functional!');
  });

  test('Buttons have proper onClick handlers and are not just visual', async ({ page }) => {
    // Test that buttons actually have click handlers by checking if they respond to clicks
    const buttons = [
      'button:has-text("Start New Project")',
      'button:has-text("Track Contribution")',
      'button:has-text("View Analytics")'
    ];

    for (const buttonSelector of buttons) {
      const button = page.locator(buttonSelector);
      await expect(button).toBeVisible();
      await expect(button).toBeEnabled();
      
      // Check that button has proper attributes indicating it's interactive
      const hasOnClick = await button.evaluate(el => {
        return el.onclick !== null || 
               el.getAttribute('onclick') !== null ||
               el.hasAttribute('data-react-aria-pressable') ||
               el.getAttribute('role') === 'button';
      });
      
      expect(hasOnClick).toBeTruthy();
      console.log(`✅ ${buttonSelector} has proper click handling`);
    }
  });

  test('Navigation system responds to button clicks', async ({ page }) => {
    // Record initial state
    const initialUrl = page.url();
    const initialTitle = await page.title();
    
    // Click Start New Project and verify some change occurs
    const startButton = page.locator('button:has-text("Start New Project")');
    await startButton.click({ force: true });
    await page.waitForTimeout(2000);
    
    // Check if anything changed (URL, title, or visible content)
    const newUrl = page.url();
    const newTitle = await page.title();
    const hasNewContent = await page.locator('text=Start').first().isVisible() ||
                         await page.locator('text=Project').first().isVisible();
    
    const somethingChanged = newUrl !== initialUrl || 
                           newTitle !== initialTitle || 
                           hasNewContent;
    
    expect(somethingChanged).toBeTruthy();
    console.log(`✅ Navigation system is responsive - URL: ${newUrl}, Title: ${newTitle}, HasContent: ${hasNewContent}`);
  });
});
