// Error Logging Service
// Integration & Services Agent: Centralized error logging and monitoring

const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_KEY
);

// CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  'Access-Control-Allow-Methods': 'GET, POST, OPTIONS'
};

// Error severity levels
const ERROR_SEVERITY = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high',
  CRITICAL: 'critical'
};

// Determine error severity based on error details
const determineErrorSeverity = (error) => {
  const message = error.message?.toLowerCase() || '';
  const stack = error.stack?.toLowerCase() || '';
  
  // Critical errors
  if (
    message.includes('network error') ||
    message.includes('failed to fetch') ||
    message.includes('database') ||
    message.includes('authentication') ||
    stack.includes('supabase')
  ) {
    return ERROR_SEVERITY.CRITICAL;
  }
  
  // High severity errors
  if (
    message.includes('permission denied') ||
    message.includes('unauthorized') ||
    message.includes('payment') ||
    message.includes('security') ||
    stack.includes('auth')
  ) {
    return ERROR_SEVERITY.HIGH;
  }
  
  // Medium severity errors
  if (
    message.includes('validation') ||
    message.includes('invalid') ||
    message.includes('not found') ||
    stack.includes('component')
  ) {
    return ERROR_SEVERITY.MEDIUM;
  }
  
  // Default to low severity
  return ERROR_SEVERITY.LOW;
};

// Extract user information from request
const extractUserInfo = async (authHeader) => {
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null;
  }

  try {
    const token = authHeader.substring(7);
    const { data: { user }, error } = await supabase.auth.getUser(token);
    
    if (error || !user) {
      return null;
    }
    
    return {
      id: user.id,
      email: user.email,
      display_name: user.user_metadata?.display_name || user.email
    };
  } catch (error) {
    console.error('Failed to extract user info:', error);
    return null;
  }
};

// Log error to database
const logErrorToDatabase = async (errorData) => {
  try {
    const { error } = await supabase
      .from('error_logs')
      .insert({
        error_id: errorData.errorId,
        message: errorData.message,
        stack_trace: errorData.stack,
        component_stack: errorData.componentStack,
        severity: errorData.severity,
        user_id: errorData.userId,
        user_agent: errorData.userAgent,
        url: errorData.url,
        context: errorData.context || {},
        error_type: errorData.type || 'javascript',
        resolved: false,
        created_at: new Date().toISOString()
      });

    if (error) {
      console.error('Failed to log error to database:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Database logging error:', error);
    return false;
  }
};

// Send alert for critical errors
const sendCriticalErrorAlert = async (errorData) => {
  try {
    // Only send alerts for critical and high severity errors
    if (errorData.severity !== ERROR_SEVERITY.CRITICAL && errorData.severity !== ERROR_SEVERITY.HIGH) {
      return;
    }

    const alertData = {
      type: 'error_alert',
      severity: errorData.severity,
      error_id: errorData.errorId,
      message: errorData.message,
      url: errorData.url,
      user_id: errorData.userId,
      timestamp: new Date().toISOString()
    };

    // Send notification via email service
    await fetch(`${process.env.NETLIFY_URL}/.netlify/functions/email-service/send`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_KEY}`
      },
      body: JSON.stringify({
        to: process.env.ADMIN_EMAIL || '<EMAIL>',
        template: 'error_alert',
        data: alertData
      })
    });

    // Log alert in admin actions
    await supabase
      .from('admin_actions')
      .insert({
        admin_id: null, // System generated
        action_type: 'error_alert_sent',
        target_type: 'system',
        target_id: errorData.errorId,
        details: alertData,
        created_at: new Date().toISOString()
      });

  } catch (error) {
    console.error('Failed to send critical error alert:', error);
  }
};

// Get error statistics
const getErrorStatistics = async (timeRange = '24h') => {
  try {
    let startDate;
    switch (timeRange) {
      case '1h':
        startDate = new Date(Date.now() - 60 * 60 * 1000);
        break;
      case '24h':
        startDate = new Date(Date.now() - 24 * 60 * 60 * 1000);
        break;
      case '7d':
        startDate = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
        break;
      case '30d':
        startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
        break;
      default:
        startDate = new Date(Date.now() - 24 * 60 * 60 * 1000);
    }

    const { data: errors, error } = await supabase
      .from('error_logs')
      .select('*')
      .gte('created_at', startDate.toISOString())
      .order('created_at', { ascending: false });

    if (error) {
      throw new Error(`Failed to fetch error statistics: ${error.message}`);
    }

    // Calculate statistics
    const totalErrors = errors.length;
    const errorsBySeverity = errors.reduce((acc, err) => {
      acc[err.severity] = (acc[err.severity] || 0) + 1;
      return acc;
    }, {});

    const errorsByType = errors.reduce((acc, err) => {
      acc[err.error_type] = (acc[err.error_type] || 0) + 1;
      return acc;
    }, {});

    const resolvedErrors = errors.filter(err => err.resolved).length;
    const unresolvedErrors = totalErrors - resolvedErrors;

    // Get most common errors
    const errorFrequency = errors.reduce((acc, err) => {
      const key = err.message.substring(0, 100); // First 100 chars
      acc[key] = (acc[key] || 0) + 1;
      return acc;
    }, {});

    const mostCommonErrors = Object.entries(errorFrequency)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 10)
      .map(([message, count]) => ({ message, count }));

    return {
      total_errors: totalErrors,
      errors_by_severity: errorsBySeverity,
      errors_by_type: errorsByType,
      resolved_errors: resolvedErrors,
      unresolved_errors: unresolvedErrors,
      most_common_errors: mostCommonErrors,
      recent_errors: errors.slice(0, 20),
      time_range: timeRange
    };

  } catch (error) {
    console.error('Error statistics error:', error);
    throw error;
  }
};

// Main handler function
exports.handler = async (event, context) => {
  // Handle CORS preflight
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers: corsHeaders,
      body: ''
    };
  }

  try {
    const httpMethod = event.httpMethod;
    const pathParts = event.path.split('/').filter(Boolean);
    const action = pathParts[pathParts.length - 1];

    if (httpMethod === 'POST' && (action === 'error-logging' || pathParts.includes('error-logging'))) {
      // Log error
      const errorData = JSON.parse(event.body || '{}');
      
      // Validate required fields
      if (!errorData.message) {
        throw new Error('Error message is required');
      }

      // Extract user information
      const userInfo = await extractUserInfo(event.headers.authorization);
      
      // Determine error severity
      const severity = determineErrorSeverity(errorData);
      
      // Prepare error data for logging
      const logData = {
        ...errorData,
        severity,
        userId: userInfo?.id || errorData.userId || null,
        userEmail: userInfo?.email || null,
        userDisplayName: userInfo?.display_name || null
      };

      // Log to database
      const logged = await logErrorToDatabase(logData);
      
      if (!logged) {
        console.error('Failed to log error to database');
      }

      // Send alert for critical errors
      await sendCriticalErrorAlert(logData);

      return {
        statusCode: 200,
        headers: corsHeaders,
        body: JSON.stringify({
          success: true,
          message: 'Error logged successfully',
          error_id: errorData.errorId,
          severity: severity
        })
      };

    } else if (httpMethod === 'GET' && action === 'statistics') {
      // Get error statistics (admin only)
      const userInfo = await extractUserInfo(event.headers.authorization);
      
      if (!userInfo) {
        throw new Error('Authentication required');
      }

      // Check if user is admin
      const { data: user, error: userError } = await supabase
        .from('users')
        .select('admin_role')
        .eq('id', userInfo.id)
        .single();

      if (userError || !user || !user.admin_role) {
        throw new Error('Admin access required');
      }

      const timeRange = new URLSearchParams(event.queryStringParameters || {}).get('time_range') || '24h';
      const statistics = await getErrorStatistics(timeRange);

      return {
        statusCode: 200,
        headers: corsHeaders,
        body: JSON.stringify({
          success: true,
          data: statistics
        })
      };

    } else {
      throw new Error('Invalid endpoint or method');
    }

  } catch (error) {
    console.error('Error logging service error:', error);
    
    return {
      statusCode: error.message.includes('Authentication') || error.message.includes('Admin access') ? 401 : 400,
      headers: corsHeaders,
      body: JSON.stringify({
        success: false,
        error: error.message
      })
    };
  }
};
