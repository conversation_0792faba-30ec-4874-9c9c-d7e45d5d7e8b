// Admin Service API
// Integration & Services Agent: Comprehensive admin and moderation system

const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_ANON_KEY
);

// CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS'
};

// Authenticate user and check admin permissions
const authenticateAdmin = async (authHeader, requiredPermission = null) => {
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    throw new Error('Missing or invalid authorization header');
  }

  const token = authHeader.substring(7);
  
  try {
    const { data: { user }, error } = await supabase.auth.getUser(token);
    
    if (error || !user) {
      throw new Error('Invalid authentication token');
    }

    // Check if user has admin role
    const { data: adminRoles, error: roleError } = await supabase
      .from('user_admin_roles')
      .select(`
        *,
        admin_roles (
          role_name,
          permissions
        )
      `)
      .eq('user_id', user.id)
      .eq('is_active', true);

    if (roleError) {
      throw new Error('Failed to check admin permissions');
    }

    if (!adminRoles || adminRoles.length === 0) {
      throw new Error('Access denied: Admin privileges required');
    }

    // Check specific permission if required
    if (requiredPermission) {
      const hasPermission = adminRoles.some(role => 
        role.admin_roles.permissions[requiredPermission] === true
      );
      
      if (!hasPermission) {
        throw new Error(`Access denied: ${requiredPermission} permission required`);
      }
    }

    return { user, adminRoles };
  } catch (error) {
    console.error('Admin authentication error:', error);
    throw error;
  }
};

// Log admin action
const logAdminAction = async (adminId, actionType, targetType, targetId, reason, details = {}) => {
  try {
    await supabase
      .from('admin_actions')
      .insert({
        admin_id: adminId,
        action_type: actionType,
        target_type: targetType,
        target_id: targetId,
        reason,
        details
      });
  } catch (error) {
    console.error('Failed to log admin action:', error);
  }
};

// Get admin dashboard data
const getAdminDashboard = async (user) => {
  try {
    // Get platform statistics
    const [
      { count: totalUsers },
      { count: activeUsers },
      { count: pendingModeration },
      { count: openTickets },
      { count: criticalIssues }
    ] = await Promise.all([
      supabase.from('users').select('*', { count: 'exact', head: true }),
      supabase.from('users').select('*', { count: 'exact', head: true }).gte('last_sign_in_at', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()),
      supabase.from('moderation_queue').select('*', { count: 'exact', head: true }).eq('status', 'pending'),
      supabase.from('support_tickets').select('*', { count: 'exact', head: true }).in('status', ['open', 'in_progress']),
      supabase.from('system_monitoring').select('*', { count: 'exact', head: true }).eq('status', 'critical')
    ]);

    // Get recent admin actions
    const { data: recentActions, error: actionsError } = await supabase
      .from('admin_actions')
      .select(`
        *,
        admin:admin_id (
          email,
          user_metadata
        )
      `)
      .order('created_at', { ascending: false })
      .limit(10);

    if (actionsError) {
      console.error('Failed to fetch recent actions:', actionsError);
    }

    // Get system health metrics
    const { data: systemMetrics, error: metricsError } = await supabase
      .from('system_monitoring')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(20);

    if (metricsError) {
      console.error('Failed to fetch system metrics:', metricsError);
    }

    return {
      statistics: {
        total_users: totalUsers || 0,
        active_users_24h: activeUsers || 0,
        pending_moderation: pendingModeration || 0,
        open_tickets: openTickets || 0,
        critical_issues: criticalIssues || 0
      },
      recent_actions: recentActions || [],
      system_metrics: systemMetrics || []
    };

  } catch (error) {
    console.error('Get admin dashboard error:', error);
    throw error;
  }
};

// Get users for management
const getUsers = async (user, queryParams) => {
  try {
    let query = supabase
      .from('users')
      .select(`
        *,
        user_suspensions!inner (
          id,
          reason,
          suspension_type,
          is_active,
          expires_at
        )
      `)
      .order('created_at', { ascending: false });

    // Apply filters
    const search = queryParams.get('search');
    if (search) {
      query = query.or(`email.ilike.%${search}%,display_name.ilike.%${search}%`);
    }

    const status = queryParams.get('status');
    if (status === 'suspended') {
      query = query.eq('user_suspensions.is_active', true);
    } else if (status === 'active') {
      query = query.is('user_suspensions.id', null);
    }

    // Apply pagination
    const limit = Math.min(parseInt(queryParams.get('limit')) || 50, 100);
    const offset = parseInt(queryParams.get('offset')) || 0;
    
    query = query.range(offset, offset + limit - 1);

    const { data: users, error } = await query;

    if (error) {
      throw new Error(`Failed to fetch users: ${error.message}`);
    }

    return users || [];

  } catch (error) {
    console.error('Get users error:', error);
    throw error;
  }
};

// Suspend user
const suspendUser = async (adminUser, suspensionData) => {
  try {
    const {
      user_id,
      reason,
      suspension_type = 'temporary',
      severity = 'moderate',
      duration_hours,
      restrictions = {}
    } = suspensionData;

    if (!user_id || !reason) {
      throw new Error('user_id and reason are required');
    }

    // Calculate expiration date for temporary suspensions
    let expires_at = null;
    if (suspension_type === 'temporary' && duration_hours) {
      expires_at = new Date(Date.now() + duration_hours * 60 * 60 * 1000).toISOString();
    }

    // Create suspension record
    const { data: suspension, error } = await supabase
      .from('user_suspensions')
      .insert({
        user_id,
        suspended_by: adminUser.id,
        reason,
        suspension_type,
        severity,
        restrictions,
        expires_at
      })
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to suspend user: ${error.message}`);
    }

    // Log admin action
    await logAdminAction(
      adminUser.id,
      'user_suspend',
      'user',
      user_id,
      reason,
      { suspension_type, severity, duration_hours }
    );

    return {
      success: true,
      suspension_id: suspension.id,
      message: `User suspended successfully (${suspension_type})`
    };

  } catch (error) {
    console.error('Suspend user error:', error);
    throw error;
  }
};

// Get moderation queue
const getModerationQueue = async (user, queryParams) => {
  try {
    let query = supabase
      .from('moderation_queue')
      .select(`
        *,
        flagged_by_user:flagged_by (
          email,
          display_name
        ),
        reviewed_by_user:reviewed_by (
          email,
          display_name
        )
      `)
      .order('priority', { ascending: true })
      .order('created_at', { ascending: true });

    // Apply filters
    const status = queryParams.get('status');
    if (status) {
      query = query.eq('status', status);
    }

    const contentType = queryParams.get('content_type');
    if (contentType) {
      query = query.eq('content_type', contentType);
    }

    const priority = queryParams.get('priority');
    if (priority) {
      query = query.eq('priority', parseInt(priority));
    }

    // Apply pagination
    const limit = Math.min(parseInt(queryParams.get('limit')) || 20, 50);
    const offset = parseInt(queryParams.get('offset')) || 0;
    
    query = query.range(offset, offset + limit - 1);

    const { data: items, error } = await query;

    if (error) {
      throw new Error(`Failed to fetch moderation queue: ${error.message}`);
    }

    return items || [];

  } catch (error) {
    console.error('Get moderation queue error:', error);
    throw error;
  }
};

// Review moderation item
const reviewModerationItem = async (adminUser, itemId, reviewData) => {
  try {
    const {
      review_action, // 'approve', 'remove', 'edit', 'warn_user', 'suspend_user'
      review_notes,
      user_action_data = {} // Additional data for user actions
    } = reviewData;

    if (!review_action) {
      throw new Error('review_action is required');
    }

    // Update moderation item
    const { data: item, error } = await supabase
      .from('moderation_queue')
      .update({
        status: review_action === 'approve' ? 'approved' : 'removed',
        reviewed_by: adminUser.id,
        review_notes,
        review_action,
        reviewed_at: new Date().toISOString()
      })
      .eq('id', itemId)
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to update moderation item: ${error.message}`);
    }

    // Take additional actions based on review decision
    if (review_action === 'suspend_user' && item.flagged_by) {
      await suspendUser(adminUser, {
        user_id: item.flagged_by,
        reason: `Content moderation: ${review_notes}`,
        suspension_type: user_action_data.suspension_type || 'temporary',
        severity: user_action_data.severity || 'moderate',
        duration_hours: user_action_data.duration_hours || 24
      });
    }

    // Log admin action
    await logAdminAction(
      adminUser.id,
      'content_moderate',
      item.content_type,
      item.content_id,
      review_notes,
      { review_action, original_flag_reason: item.flag_reason }
    );

    return {
      success: true,
      item_id: itemId,
      action_taken: review_action,
      message: 'Moderation review completed successfully'
    };

  } catch (error) {
    console.error('Review moderation item error:', error);
    throw error;
  }
};

// Get support tickets
const getSupportTickets = async (user, queryParams) => {
  try {
    let query = supabase
      .from('support_tickets')
      .select(`
        *,
        user:user_id (
          email,
          display_name
        ),
        assigned_admin:assigned_to (
          email,
          display_name
        )
      `)
      .order('priority', { ascending: true })
      .order('created_at', { ascending: false });

    // Apply filters
    const status = queryParams.get('status');
    if (status) {
      query = query.eq('status', status);
    }

    const priority = queryParams.get('priority');
    if (priority) {
      query = query.eq('priority', priority);
    }

    const assignedTo = queryParams.get('assigned_to');
    if (assignedTo === 'me') {
      query = query.eq('assigned_to', user.id);
    } else if (assignedTo === 'unassigned') {
      query = query.is('assigned_to', null);
    }

    // Apply pagination
    const limit = Math.min(parseInt(queryParams.get('limit')) || 20, 50);
    const offset = parseInt(queryParams.get('offset')) || 0;
    
    query = query.range(offset, offset + limit - 1);

    const { data: tickets, error } = await query;

    if (error) {
      throw new Error(`Failed to fetch support tickets: ${error.message}`);
    }

    return tickets || [];

  } catch (error) {
    console.error('Get support tickets error:', error);
    throw error;
  }
};

// Update support ticket
const updateSupportTicket = async (adminUser, ticketId, updateData) => {
  try {
    const {
      status,
      priority,
      assigned_to,
      resolution_notes,
      tags
    } = updateData;

    // Prepare update data
    const updates = {};
    if (status !== undefined) updates.status = status;
    if (priority !== undefined) updates.priority = priority;
    if (assigned_to !== undefined) updates.assigned_to = assigned_to;
    if (resolution_notes !== undefined) updates.resolution_notes = resolution_notes;
    if (tags !== undefined) updates.tags = tags;

    if (status === 'resolved') {
      updates.resolved_at = new Date().toISOString();
    }

    const { data: ticket, error } = await supabase
      .from('support_tickets')
      .update(updates)
      .eq('id', ticketId)
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to update support ticket: ${error.message}`);
    }

    // Log admin action
    await logAdminAction(
      adminUser.id,
      'ticket_update',
      'support_ticket',
      ticketId,
      'Ticket updated',
      updates
    );

    return {
      success: true,
      ticket_id: ticketId,
      ticket: ticket,
      message: 'Support ticket updated successfully'
    };

  } catch (error) {
    console.error('Update support ticket error:', error);
    throw error;
  }
};

// Get system monitoring data
const getSystemMonitoring = async (user, queryParams) => {
  try {
    let query = supabase
      .from('system_monitoring')
      .select('*')
      .order('created_at', { ascending: false });

    // Apply filters
    const metricType = queryParams.get('metric_type');
    if (metricType) {
      query = query.eq('metric_type', metricType);
    }

    const status = queryParams.get('status');
    if (status) {
      query = query.eq('status', status);
    }

    const timeRange = queryParams.get('time_range') || '24h';
    const hoursBack = timeRange === '1h' ? 1 : timeRange === '24h' ? 24 : timeRange === '7d' ? 168 : 24;
    const startTime = new Date(Date.now() - hoursBack * 60 * 60 * 1000).toISOString();
    
    query = query.gte('created_at', startTime);

    // Apply pagination
    const limit = Math.min(parseInt(queryParams.get('limit')) || 100, 500);
    const offset = parseInt(queryParams.get('offset')) || 0;
    
    query = query.range(offset, offset + limit - 1);

    const { data: metrics, error } = await query;

    if (error) {
      throw new Error(`Failed to fetch system monitoring data: ${error.message}`);
    }

    return metrics || [];

  } catch (error) {
    console.error('Get system monitoring error:', error);
    throw error;
  }
};

// Main handler function
exports.handler = async (event, context) => {
  // Handle CORS preflight
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers: corsHeaders,
      body: ''
    };
  }

  try {
    // Parse request path and method
    const pathParts = event.path.split('/').filter(Boolean);
    const action = pathParts[pathParts.length - 1];
    const httpMethod = event.httpMethod;
    const body = event.body ? JSON.parse(event.body) : {};
    const queryParams = new URLSearchParams(event.queryStringParameters || {});

    let result;

    switch (action) {
      case 'dashboard':
        if (httpMethod !== 'GET') {
          throw new Error('Method not allowed');
        }
        const { user: dashboardUser } = await authenticateAdmin(event.headers.authorization);
        result = await getAdminDashboard(dashboardUser);
        break;

      case 'users':
        if (httpMethod !== 'GET') {
          throw new Error('Method not allowed');
        }
        const { user: usersUser } = await authenticateAdmin(event.headers.authorization, 'user_management');
        result = await getUsers(usersUser, queryParams);
        break;

      case 'suspend-user':
        if (httpMethod !== 'POST') {
          throw new Error('Method not allowed');
        }
        const { user: suspendUser } = await authenticateAdmin(event.headers.authorization, 'user_management');
        result = await suspendUser(suspendUser, body);
        break;

      case 'moderation-queue':
        if (httpMethod !== 'GET') {
          throw new Error('Method not allowed');
        }
        const { user: moderationUser } = await authenticateAdmin(event.headers.authorization, 'content_moderation');
        result = await getModerationQueue(moderationUser, queryParams);
        break;

      case 'review-moderation':
        if (httpMethod !== 'POST') {
          throw new Error('Method not allowed');
        }
        const { user: reviewUser } = await authenticateAdmin(event.headers.authorization, 'content_moderation');
        const itemId = body.item_id;
        if (!itemId) {
          throw new Error('item_id is required');
        }
        result = await reviewModerationItem(reviewUser, itemId, body);
        break;

      case 'support-tickets':
        if (httpMethod !== 'GET') {
          throw new Error('Method not allowed');
        }
        const { user: ticketsUser } = await authenticateAdmin(event.headers.authorization, 'support_management');
        result = await getSupportTickets(ticketsUser, queryParams);
        break;

      case 'update-ticket':
        if (httpMethod !== 'PUT') {
          throw new Error('Method not allowed');
        }
        const { user: updateUser } = await authenticateAdmin(event.headers.authorization, 'support_management');
        const ticketId = body.ticket_id;
        if (!ticketId) {
          throw new Error('ticket_id is required');
        }
        result = await updateSupportTicket(updateUser, ticketId, body);
        break;

      case 'system-monitoring':
        if (httpMethod !== 'GET') {
          throw new Error('Method not allowed');
        }
        const { user: monitoringUser } = await authenticateAdmin(event.headers.authorization, 'system_monitoring');
        result = await getSystemMonitoring(monitoringUser, queryParams);
        break;

      default:
        throw new Error('Invalid action');
    }

    return {
      statusCode: 200,
      headers: corsHeaders,
      body: JSON.stringify({
        success: true,
        data: result,
        timestamp: new Date().toISOString()
      })
    };

  } catch (error) {
    console.error('Admin Service API error:', error);
    
    return {
      statusCode: error.message.includes('Authentication') || error.message.includes('Access denied') ? 401 : 
                  error.message.includes('not allowed') ? 405 : 400,
      headers: corsHeaders,
      body: JSON.stringify({
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      })
    };
  }
};
