/**
 * Multi-Currency Support System
 * 
 * Comprehensive system for international collaborations with currency conversion and exchange rate handling:
 * - Currency conversion and exchange rates
 * - Multi-currency revenue calculations
 * - International payment processing
 * - Currency risk management
 * - Regional compliance and tax considerations
 */

// ============================================================================
// SUPPORTED CURRENCIES AND REGIONS
// ============================================================================

export const SUPPORTED_CURRENCIES = {
  // Major Currencies
  USD: { code: 'USD', name: 'US Dollar', symbol: '$', decimals: 2, region: 'North America' },
  EUR: { code: 'EUR', name: 'Euro', symbol: '€', decimals: 2, region: 'Europe' },
  GBP: { code: 'GBP', name: 'British Pound', symbol: '£', decimals: 2, region: 'Europe' },
  JPY: { code: 'JPY', name: 'Japanese Yen', symbol: '¥', decimals: 0, region: 'Asia' },
  
  // Asia Pacific
  AUD: { code: 'AUD', name: 'Australian Dollar', symbol: 'A$', decimals: 2, region: 'Asia Pacific' },
  CAD: { code: 'CAD', name: 'Canadian Dollar', symbol: 'C$', decimals: 2, region: 'North America' },
  CHF: { code: 'CHF', name: 'Swiss Franc', symbol: 'CHF', decimals: 2, region: 'Europe' },
  CNY: { code: 'CNY', name: 'Chinese Yuan', symbol: '¥', decimals: 2, region: 'Asia' },
  
  // Emerging Markets
  INR: { code: 'INR', name: 'Indian Rupee', symbol: '₹', decimals: 2, region: 'Asia' },
  BRL: { code: 'BRL', name: 'Brazilian Real', symbol: 'R$', decimals: 2, region: 'South America' },
  MXN: { code: 'MXN', name: 'Mexican Peso', symbol: '$', decimals: 2, region: 'North America' },
  ZAR: { code: 'ZAR', name: 'South African Rand', symbol: 'R', decimals: 2, region: 'Africa' },
  
  // Digital Currencies (for future support)
  BTC: { code: 'BTC', name: 'Bitcoin', symbol: '₿', decimals: 8, region: 'Digital' },
  ETH: { code: 'ETH', name: 'Ethereum', symbol: 'Ξ', decimals: 18, region: 'Digital' }
};

export const CURRENCY_CONVERSION_METHODS = {
  REAL_TIME: 'real_time',           // Live exchange rates
  DAILY_AVERAGE: 'daily_average',   // Daily average rates
  MONTHLY_AVERAGE: 'monthly_average', // Monthly average rates
  FIXED_RATE: 'fixed_rate',         // Fixed rate agreed upon
  SPOT_RATE: 'spot_rate'            // Rate at time of transaction
};

export const EXCHANGE_RATE_PROVIDERS = {
  FIXER: 'fixer.io',
  EXCHANGERATE_API: 'exchangerate-api.com',
  CURRENCYLAYER: 'currencylayer.com',
  OPENEXCHANGERATES: 'openexchangerates.org',
  ECB: 'ecb.europa.eu',             // European Central Bank
  MANUAL: 'manual'                  // Manually entered rates
};

// ============================================================================
// MULTI-CURRENCY SYSTEM CLASS
// ============================================================================

export class MultiCurrencySystem {
  constructor() {
    this.exchangeRates = new Map();
    this.rateHistory = [];
    this.currencyPreferences = new Map();
    this.conversionCache = new Map();
    this.defaultBaseCurrency = 'USD';
    this.rateProvider = EXCHANGE_RATE_PROVIDERS.EXCHANGERATE_API;
  }

  /**
   * Set currency preferences for a user or alliance
   */
  setCurrencyPreferences(entityId, preferences) {
    const currencyPrefs = {
      entityId,
      baseCurrency: preferences.baseCurrency || this.defaultBaseCurrency,
      acceptedCurrencies: preferences.acceptedCurrencies || [preferences.baseCurrency],
      conversionMethod: preferences.conversionMethod || CURRENCY_CONVERSION_METHODS.DAILY_AVERAGE,
      rateProvider: preferences.rateProvider || this.rateProvider,
      
      // Risk management
      hedgingEnabled: preferences.hedgingEnabled || false,
      maxCurrencyExposure: preferences.maxCurrencyExposure || 100, // Percentage
      autoConvertThreshold: preferences.autoConvertThreshold || 1000, // Amount
      
      // Payment preferences
      preferredPaymentCurrencies: preferences.preferredPaymentCurrencies || [preferences.baseCurrency],
      minimumPayoutAmounts: preferences.minimumPayoutAmounts || {},
      
      // Regional settings
      region: preferences.region,
      timezone: preferences.timezone,
      taxReportingCurrency: preferences.taxReportingCurrency || preferences.baseCurrency,
      
      // Metadata
      updatedAt: new Date().toISOString()
    };

    this.currencyPreferences.set(entityId, currencyPrefs);
    return currencyPrefs;
  }

  /**
   * Get current exchange rate between two currencies
   */
  async getExchangeRate(fromCurrency, toCurrency, method = CURRENCY_CONVERSION_METHODS.REAL_TIME) {
    if (fromCurrency === toCurrency) return 1.0;

    const rateKey = `${fromCurrency}_${toCurrency}_${method}`;
    
    // Check cache first
    const cachedRate = this.conversionCache.get(rateKey);
    if (cachedRate && this.isCacheValid(cachedRate)) {
      return cachedRate.rate;
    }

    try {
      let rate;
      
      switch (method) {
        case CURRENCY_CONVERSION_METHODS.REAL_TIME:
          rate = await this.fetchRealTimeRate(fromCurrency, toCurrency);
          break;
        case CURRENCY_CONVERSION_METHODS.DAILY_AVERAGE:
          rate = await this.fetchDailyAverageRate(fromCurrency, toCurrency);
          break;
        case CURRENCY_CONVERSION_METHODS.MONTHLY_AVERAGE:
          rate = await this.fetchMonthlyAverageRate(fromCurrency, toCurrency);
          break;
        case CURRENCY_CONVERSION_METHODS.FIXED_RATE:
          rate = this.getFixedRate(fromCurrency, toCurrency);
          break;
        default:
          rate = await this.fetchRealTimeRate(fromCurrency, toCurrency);
      }

      // Cache the rate
      this.conversionCache.set(rateKey, {
        rate,
        timestamp: new Date().toISOString(),
        method,
        fromCurrency,
        toCurrency
      });

      // Store in rate history
      this.rateHistory.push({
        fromCurrency,
        toCurrency,
        rate,
        method,
        provider: this.rateProvider,
        timestamp: new Date().toISOString()
      });

      return rate;

    } catch (error) {
      console.error(`Failed to get exchange rate for ${fromCurrency} to ${toCurrency}:`, error);
      
      // Fallback to cached rate if available
      const fallbackRate = this.getFallbackRate(fromCurrency, toCurrency);
      if (fallbackRate) {
        return fallbackRate;
      }
      
      throw new Error(`Unable to get exchange rate for ${fromCurrency} to ${toCurrency}`);
    }
  }

  /**
   * Convert amount from one currency to another
   */
  async convertCurrency(amount, fromCurrency, toCurrency, method = CURRENCY_CONVERSION_METHODS.REAL_TIME) {
    if (fromCurrency === toCurrency) {
      return {
        originalAmount: amount,
        originalCurrency: fromCurrency,
        convertedAmount: amount,
        convertedCurrency: toCurrency,
        exchangeRate: 1.0,
        conversionMethod: method,
        timestamp: new Date().toISOString()
      };
    }

    const exchangeRate = await this.getExchangeRate(fromCurrency, toCurrency, method);
    const convertedAmount = this.roundCurrencyAmount(amount * exchangeRate, toCurrency);

    return {
      originalAmount: amount,
      originalCurrency: fromCurrency,
      convertedAmount,
      convertedCurrency: toCurrency,
      exchangeRate,
      conversionMethod: method,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Calculate multi-currency revenue distribution
   */
  async calculateMultiCurrencyRevenue(revenueData, contributors, options = {}) {
    const baseCurrency = options.baseCurrency || this.defaultBaseCurrency;
    const conversionMethod = options.conversionMethod || CURRENCY_CONVERSION_METHODS.DAILY_AVERAGE;

    // Convert all revenue to base currency first
    const convertedRevenue = await this.convertRevenueToBaseCurrency(revenueData, baseCurrency, conversionMethod);

    // Calculate distributions in base currency
    const baseDistributions = await this.calculateBaseDistributions(convertedRevenue, contributors);

    // Convert distributions to each contributor's preferred currency
    const multiCurrencyDistributions = await Promise.all(
      baseDistributions.map(async (distribution) => {
        const contributorPrefs = this.currencyPreferences.get(distribution.contributorId);
        const targetCurrency = contributorPrefs?.baseCurrency || baseCurrency;

        if (targetCurrency === baseCurrency) {
          return {
            ...distribution,
            currency: baseCurrency,
            exchangeRate: 1.0,
            conversionMethod
          };
        }

        const conversion = await this.convertCurrency(
          distribution.amount,
          baseCurrency,
          targetCurrency,
          conversionMethod
        );

        return {
          ...distribution,
          originalAmount: distribution.amount,
          originalCurrency: baseCurrency,
          amount: conversion.convertedAmount,
          currency: targetCurrency,
          exchangeRate: conversion.exchangeRate,
          conversionMethod,
          conversionTimestamp: conversion.timestamp
        };
      })
    );

    return {
      baseCurrency,
      totalRevenueInBaseCurrency: convertedRevenue.totalAmount,
      distributions: multiCurrencyDistributions,
      conversionSummary: {
        method: conversionMethod,
        timestamp: new Date().toISOString(),
        ratesUsed: this.getUsedRates(multiCurrencyDistributions)
      }
    };
  }

  /**
   * Convert revenue data to base currency
   */
  async convertRevenueToBaseCurrency(revenueData, baseCurrency, conversionMethod) {
    const conversions = [];
    let totalAmount = 0;

    for (const [currency, amount] of Object.entries(revenueData.amounts || {})) {
      if (currency === baseCurrency) {
        totalAmount += amount;
        conversions.push({
          currency,
          originalAmount: amount,
          convertedAmount: amount,
          exchangeRate: 1.0
        });
      } else {
        const conversion = await this.convertCurrency(amount, currency, baseCurrency, conversionMethod);
        totalAmount += conversion.convertedAmount;
        conversions.push({
          currency,
          originalAmount: amount,
          convertedAmount: conversion.convertedAmount,
          exchangeRate: conversion.exchangeRate
        });
      }
    }

    return {
      totalAmount,
      baseCurrency,
      conversions,
      conversionMethod,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Handle currency risk management
   */
  assessCurrencyRisk(entityId, exposureData) {
    const preferences = this.currencyPreferences.get(entityId);
    if (!preferences) return { riskLevel: 'unknown', recommendations: [] };

    const baseCurrency = preferences.baseCurrency;
    const maxExposure = preferences.maxCurrencyExposure;
    
    const riskAssessment = {
      entityId,
      baseCurrency,
      totalExposure: 0,
      currencyExposures: {},
      riskLevel: 'low',
      recommendations: [],
      assessmentDate: new Date().toISOString()
    };

    // Calculate exposure by currency
    for (const [currency, amount] of Object.entries(exposureData)) {
      if (currency !== baseCurrency) {
        const exposurePercentage = (amount / exposureData.total) * 100;
        riskAssessment.currencyExposures[currency] = {
          amount,
          percentage: exposurePercentage,
          riskLevel: this.assessCurrencyRiskLevel(currency, exposurePercentage)
        };
        riskAssessment.totalExposure += exposurePercentage;
      }
    }

    // Determine overall risk level
    if (riskAssessment.totalExposure > maxExposure) {
      riskAssessment.riskLevel = 'high';
      riskAssessment.recommendations.push('Consider hedging currency exposure');
      riskAssessment.recommendations.push('Diversify revenue sources');
    } else if (riskAssessment.totalExposure > maxExposure * 0.7) {
      riskAssessment.riskLevel = 'medium';
      riskAssessment.recommendations.push('Monitor currency exposure closely');
    }

    // Currency-specific recommendations
    for (const [currency, exposure] of Object.entries(riskAssessment.currencyExposures)) {
      if (exposure.riskLevel === 'high') {
        riskAssessment.recommendations.push(`Consider reducing ${currency} exposure`);
      }
    }

    return riskAssessment;
  }

  /**
   * Generate currency conversion report
   */
  generateCurrencyReport(entityId, period) {
    const preferences = this.currencyPreferences.get(entityId);
    const relevantRates = this.rateHistory.filter(rate => 
      rate.timestamp >= period.startDate && 
      rate.timestamp <= period.endDate
    );

    const report = {
      entityId,
      period,
      baseCurrency: preferences?.baseCurrency || this.defaultBaseCurrency,
      
      // Rate statistics
      rateStatistics: this.calculateRateStatistics(relevantRates),
      
      // Conversion summary
      totalConversions: relevantRates.length,
      currenciesUsed: [...new Set(relevantRates.map(r => r.fromCurrency))],
      
      // Volatility analysis
      volatilityAnalysis: this.analyzeVolatility(relevantRates),
      
      // Cost analysis
      conversionCosts: this.estimateConversionCosts(relevantRates),
      
      generatedAt: new Date().toISOString()
    };

    return report;
  }

  // ============================================================================
  // HELPER METHODS
  // ============================================================================

  /**
   * Fetch real-time exchange rate from API
   */
  async fetchRealTimeRate(fromCurrency, toCurrency) {
    // Mock implementation - in production, this would call actual API
    const mockRates = {
      'USD_EUR': 0.85,
      'USD_GBP': 0.73,
      'USD_JPY': 110.0,
      'EUR_USD': 1.18,
      'GBP_USD': 1.37,
      'JPY_USD': 0.009
    };

    const rateKey = `${fromCurrency}_${toCurrency}`;
    const reverseKey = `${toCurrency}_${fromCurrency}`;

    if (mockRates[rateKey]) {
      return mockRates[rateKey];
    } else if (mockRates[reverseKey]) {
      return 1 / mockRates[reverseKey];
    }

    // Fallback calculation through USD
    if (fromCurrency !== 'USD' && toCurrency !== 'USD') {
      const toUsdRate = await this.fetchRealTimeRate(fromCurrency, 'USD');
      const fromUsdRate = await this.fetchRealTimeRate('USD', toCurrency);
      return toUsdRate * fromUsdRate;
    }

    throw new Error(`Exchange rate not available for ${fromCurrency} to ${toCurrency}`);
  }

  /**
   * Fetch daily average rate
   */
  async fetchDailyAverageRate(fromCurrency, toCurrency) {
    // In production, this would fetch historical daily average
    return this.fetchRealTimeRate(fromCurrency, toCurrency);
  }

  /**
   * Fetch monthly average rate
   */
  async fetchMonthlyAverageRate(fromCurrency, toCurrency) {
    // In production, this would fetch historical monthly average
    return this.fetchRealTimeRate(fromCurrency, toCurrency);
  }

  /**
   * Get fixed rate (manually set)
   */
  getFixedRate(fromCurrency, toCurrency) {
    const rateKey = `${fromCurrency}_${toCurrency}`;
    const fixedRates = this.exchangeRates.get('fixed_rates') || {};
    return fixedRates[rateKey] || null;
  }

  /**
   * Get fallback rate from cache
   */
  getFallbackRate(fromCurrency, toCurrency) {
    const recentRates = this.rateHistory
      .filter(rate => rate.fromCurrency === fromCurrency && rate.toCurrency === toCurrency)
      .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

    return recentRates.length > 0 ? recentRates[0].rate : null;
  }

  /**
   * Check if cached rate is still valid
   */
  isCacheValid(cachedRate) {
    const cacheAge = Date.now() - new Date(cachedRate.timestamp).getTime();
    const maxAge = 5 * 60 * 1000; // 5 minutes
    return cacheAge < maxAge;
  }

  /**
   * Round amount to appropriate decimal places for currency
   */
  roundCurrencyAmount(amount, currency) {
    const currencyInfo = SUPPORTED_CURRENCIES[currency];
    const decimals = currencyInfo ? currencyInfo.decimals : 2;
    return Math.round(amount * Math.pow(10, decimals)) / Math.pow(10, decimals);
  }

  /**
   * Calculate base distributions (simplified)
   */
  async calculateBaseDistributions(convertedRevenue, contributors) {
    // Simplified distribution calculation
    const perContributor = convertedRevenue.totalAmount / contributors.length;
    
    return contributors.map(contributor => ({
      contributorId: contributor.id,
      contributorName: contributor.name,
      amount: perContributor,
      percentage: 100 / contributors.length
    }));
  }

  /**
   * Get rates used in distributions
   */
  getUsedRates(distributions) {
    const rates = {};
    distributions.forEach(dist => {
      if (dist.exchangeRate && dist.exchangeRate !== 1.0) {
        rates[`${dist.originalCurrency}_${dist.currency}`] = dist.exchangeRate;
      }
    });
    return rates;
  }

  /**
   * Assess currency risk level
   */
  assessCurrencyRiskLevel(currency, exposurePercentage) {
    // Simplified risk assessment
    if (exposurePercentage > 50) return 'high';
    if (exposurePercentage > 25) return 'medium';
    return 'low';
  }

  /**
   * Calculate rate statistics
   */
  calculateRateStatistics(rates) {
    // Implementation for rate statistics
    return {
      averageRate: rates.reduce((sum, r) => sum + r.rate, 0) / rates.length,
      minRate: Math.min(...rates.map(r => r.rate)),
      maxRate: Math.max(...rates.map(r => r.rate)),
      volatility: this.calculateVolatility(rates)
    };
  }

  /**
   * Analyze volatility
   */
  analyzeVolatility(rates) {
    if (rates.length < 2) return { level: 'insufficient_data' };
    
    const rateValues = rates.map(r => r.rate);
    const mean = rateValues.reduce((sum, rate) => sum + rate, 0) / rateValues.length;
    const variance = rateValues.reduce((sum, rate) => sum + Math.pow(rate - mean, 2), 0) / rateValues.length;
    const standardDeviation = Math.sqrt(variance);
    const volatility = (standardDeviation / mean) * 100;

    return {
      level: volatility > 5 ? 'high' : volatility > 2 ? 'medium' : 'low',
      percentage: volatility,
      standardDeviation
    };
  }

  /**
   * Calculate volatility from rate values
   */
  calculateVolatility(rates) {
    if (rates.length < 2) return 0;
    
    const rateValues = rates.map(r => r.rate);
    const mean = rateValues.reduce((sum, rate) => sum + rate, 0) / rateValues.length;
    const variance = rateValues.reduce((sum, rate) => sum + Math.pow(rate - mean, 2), 0) / rateValues.length;
    
    return Math.sqrt(variance);
  }

  /**
   * Estimate conversion costs
   */
  estimateConversionCosts(rates) {
    // Simplified cost estimation
    const totalConversions = rates.length;
    const estimatedCostPerConversion = 0.005; // 0.5% spread
    
    return {
      totalConversions,
      estimatedCostPerConversion,
      totalEstimatedCosts: totalConversions * estimatedCostPerConversion
    };
  }

  /**
   * Format currency amount for display
   */
  formatCurrencyAmount(amount, currency) {
    const currencyInfo = SUPPORTED_CURRENCIES[currency];
    if (!currencyInfo) return `${amount} ${currency}`;
    
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: currencyInfo.decimals,
      maximumFractionDigits: currencyInfo.decimals
    }).format(amount);
  }

  /**
   * Get supported currencies for a region
   */
  getCurrenciesByRegion(region) {
    return Object.values(SUPPORTED_CURRENCIES).filter(currency => 
      currency.region === region
    );
  }

  /**
   * Set exchange rate provider
   */
  setRateProvider(provider) {
    if (Object.values(EXCHANGE_RATE_PROVIDERS).includes(provider)) {
      this.rateProvider = provider;
    } else {
      throw new Error(`Unsupported rate provider: ${provider}`);
    }
  }

  /**
   * Clear conversion cache
   */
  clearCache() {
    this.conversionCache.clear();
  }

  /**
   * Export currency data
   */
  exportCurrencyData(entityId) {
    return {
      preferences: this.currencyPreferences.get(entityId),
      rateHistory: this.rateHistory,
      supportedCurrencies: SUPPORTED_CURRENCIES,
      exportedAt: new Date().toISOString()
    };
  }
}

// Export the multi-currency system
export const multiCurrencySystem = new MultiCurrencySystem();
