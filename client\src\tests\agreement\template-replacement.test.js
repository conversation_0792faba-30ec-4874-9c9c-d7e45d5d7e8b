/**
 * Template Replacement Tests
 * 
 * Tests that the agreement generator correctly replaces all placeholders
 * and template variables throughout the document
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { NewAgreementGenerator } from '../../utils/agreement/newAgreementGenerator.js';

// Mock Supabase
const mockSupabase = {
  from: vi.fn(() => ({
    select: vi.fn(() => ({
      eq: vi.fn(() => ({
        single: vi.fn()
      }))
    }))
  }))
};

vi.mock('../../utils/supabase/supabase.utils', () => ({
  supabase: mockSupabase
}));

describe('Template Replacement in Agreement Generation', () => {
  let generator;
  let mockProject;
  let mockContributors;
  let mockCurrentUser;

  beforeEach(() => {
    generator = new NewAgreementGenerator();
    
    mockProject = {
      id: 'test-project',
      name: 'Test Project',
      description: 'A test project for template replacement',
      project_type: 'software'
    };

    mockContributors = [{
      id: 'contributor-1',
      permission_level: 'Owner',
      display_name: 'Project Owner',
      email: '<EMAIL>'
    }];

    mockCurrentUser = {
      id: 'user-1',
      email: '<EMAIL>',
      user_metadata: {
        full_name: 'Test Contributor'
      }
    };

    vi.clearAllMocks();
  });

  describe('Date Replacement', () => {
    it('should replace all date placeholders with current date', async () => {
      const template = `
Agreement effective as of [Date]
Effective Date: [Date]
Date: [Date]
`;

      const result = await generator.generateAgreement(template, mockProject, {
        contributors: mockContributors,
        currentUser: mockCurrentUser,
        fullName: 'Test Contributor'
      });

      const today = new Date().toLocaleDateString('en-US', {
        month: 'long',
        day: 'numeric',
        year: 'numeric'
      });

      expect(result).toContain(today);
      expect(result).not.toContain('[Date]');
    });

    it('should use custom date when provided', async () => {
      const template = `Agreement effective as of [Date]`;
      const customDate = new Date('2025-12-25');

      const result = await generator.generateAgreement(template, mockProject, {
        contributors: mockContributors,
        currentUser: mockCurrentUser,
        fullName: 'Test Contributor',
        agreementDate: customDate
      });

      expect(result).toContain('December 25, 2025');
    });
  });

  describe('Project Information Replacement', () => {
    it('should replace project name placeholders', async () => {
      const template = `
Project: [Project Name]
Work on Village of The Ages
PROJECT NAME: [PROJECT NAME]
`;

      const result = await generator.generateAgreement(template, mockProject, {
        contributors: mockContributors,
        currentUser: mockCurrentUser,
        fullName: 'Test Contributor'
      });

      expect(result).toContain('Project: Test Project');
      expect(result).toContain('Work on Test Project');
      expect(result).toContain('PROJECT NAME: TEST PROJECT');
    });

    it('should replace project description', async () => {
      const template = `
Description: [Project Description]
Default: village simulation game where players guide communities through historical progressions and manage resource-based challenges
`;

      const result = await generator.generateAgreement(template, mockProject, {
        contributors: mockContributors,
        currentUser: mockCurrentUser,
        fullName: 'Test Contributor'
      });

      expect(result).toContain('Description: A test project for template replacement');
      expect(result).toContain('A test project for template replacement');
      expect(result).not.toContain('village simulation game');
    });
  });

  describe('Contributor Information Replacement', () => {
    it('should replace contributor name placeholders', async () => {
      const template = `
Contributor: [Contributor]
Name: [Contributor]
CONTRIBUTOR: [CONTRIBUTOR]
`;

      const result = await generator.generateAgreement(template, mockProject, {
        contributors: mockContributors,
        currentUser: mockCurrentUser,
        fullName: 'John Smith'
      });

      expect(result).toContain('Contributor: John Smith');
      expect(result).toContain('Name: John Smith');
      expect(result).toContain('CONTRIBUTOR: JOHN SMITH');
    });

    it('should handle missing contributor name gracefully', async () => {
      const template = `Contributor: [Contributor]`;

      const result = await generator.generateAgreement(template, mockProject, {
        contributors: mockContributors,
        currentUser: mockCurrentUser
        // No fullName provided
      });

      expect(result).toContain('Contributor: Test Contributor'); // Falls back to currentUser name
    });
  });

  describe('Company Information Replacement', () => {
    it('should replace default company information', async () => {
      const template = `
Company: City of Gamers Inc.
COG reference
Address: 1205 43rd Street, Suite B, Orlando, Florida 32839
Signer: Gynell Journigan
`;

      const result = await generator.generateAgreement(template, mockProject, {
        contributors: mockContributors,
        currentUser: mockCurrentUser,
        fullName: 'Test Contributor'
      });

      expect(result).toContain('Company: Project Owner');
      expect(result).toContain('Project Owner reference');
      expect(result).not.toContain('City of Gamers Inc.');
      expect(result).not.toContain('Gynell Journigan');
    });
  });

  describe('Placeholder Cleanup', () => {
    it('should remove any remaining empty placeholders', async () => {
      const template = `
Valid content
[Unknown Placeholder]
[Another Unknown]
[]
[   ]
More valid content
`;

      const result = await generator.generateAgreement(template, mockProject, {
        contributors: mockContributors,
        currentUser: mockCurrentUser,
        fullName: 'Test Contributor'
      });

      expect(result).toContain('Valid content');
      expect(result).toContain('More valid content');
      expect(result).not.toContain('[Unknown Placeholder]');
      expect(result).not.toContain('[Another Unknown]');
      expect(result).not.toContain('[]');
      expect(result).not.toContain('[   ]');
    });
  });

  describe('Project Type Specific Replacements', () => {
    it('should replace game-specific terms for software projects', async () => {
      const template = `
This game will have players who enjoy gameplay.
Game builds will be delivered to players.
`;

      const softwareProject = {
        ...mockProject,
        project_type: 'software'
      };

      const result = await generator.generateAgreement(template, softwareProject, {
        contributors: mockContributors,
        currentUser: mockCurrentUser,
        fullName: 'Test Contributor'
      });

      expect(result).toContain('software application');
      expect(result).toContain('users');
      expect(result).toContain('user experience');
      expect(result).toContain('software releases');
      expect(result).not.toContain('game');
      expect(result).not.toContain('players');
      expect(result).not.toContain('gameplay');
    });

    it('should replace game-specific terms for music projects', async () => {
      const template = `
This game will have players who enjoy gameplay.
Characters and levels will be created.
`;

      const musicProject = {
        ...mockProject,
        project_type: 'music'
      };

      const result = await generator.generateAgreement(template, musicProject, {
        contributors: mockContributors,
        currentUser: mockCurrentUser,
        fullName: 'Test Contributor'
      });

      expect(result).toContain('music');
      expect(result).toContain('listeners');
      expect(result).toContain('listening experience');
      expect(result).toContain('artists');
      expect(result).toContain('tracks');
    });
  });

  describe('Schedule and Exhibit Generation', () => {
    it('should generate Schedule A with project information', async () => {
      const template = `
## SCHEDULE A
### Description of Services
[Schedule A will be generated]
`;

      const result = await generator.generateAgreement(template, mockProject, {
        contributors: mockContributors,
        currentUser: mockCurrentUser,
        fullName: 'Test Contributor'
      });

      expect(result).toContain('## SCHEDULE A');
      expect(result).toContain('Description of Services');
      expect(result).toContain('software development work on "Test Project"');
      expect(result).toContain('A test project for template replacement');
    });
  });

  describe('Text Formatting and Cleanup', () => {
    it('should clean up excessive newlines', async () => {
      const template = `
Line 1


Line 2




Line 3
`;

      const result = await generator.generateAgreement(template, mockProject, {
        contributors: mockContributors,
        currentUser: mockCurrentUser,
        fullName: 'Test Contributor'
      });

      // Should not have more than 2 consecutive newlines
      expect(result).not.toMatch(/\n{3,}/);
    });

    it('should ensure document ends with newline', async () => {
      const template = `Content without ending newline`;

      const result = await generator.generateAgreement(template, mockProject, {
        contributors: mockContributors,
        currentUser: mockCurrentUser,
        fullName: 'Test Contributor'
      });

      expect(result.endsWith('\n')).toBe(true);
    });
  });
});
