/**
 * Performance Metrics System
 * 
 * Comprehensive system for tracking and calculating performance-based payments and bonuses:
 * - Performance tracking and measurement
 * - KPI definition and monitoring
 * - Bonus calculation and distribution
 * - Quality metrics and scoring
 * - Achievement tracking and rewards
 */

// ============================================================================
// PERFORMANCE METRICS DEFINITIONS
// ============================================================================

export const PERFORMANCE_METRIC_TYPES = {
  // Quantitative Metrics
  REVENUE_GENERATED: 'revenue_generated',
  SALES_VOLUME: 'sales_volume',
  UNITS_SOLD: 'units_sold',
  LEADS_CONVERTED: 'leads_converted',
  CUSTOMER_ACQUISITION: 'customer_acquisition',
  RETENTION_RATE: 'retention_rate',
  
  // Quality Metrics
  CUSTOMER_SATISFACTION: 'customer_satisfaction',
  CODE_QUALITY_SCORE: 'code_quality_score',
  BUG_RATE: 'bug_rate',
  REVIEW_SCORE: 'review_score',
  COMPLETION_RATE: 'completion_rate',
  
  // Time-based Metrics
  DELIVERY_SPEED: 'delivery_speed',
  RESPONSE_TIME: 'response_time',
  UPTIME: 'uptime',
  MILESTONE_ADHERENCE: 'milestone_adherence',
  
  // Collaboration Metrics
  TEAM_CONTRIBUTION: 'team_contribution',
  KNOWLEDGE_SHARING: 'knowledge_sharing',
  MENTORING_IMPACT: 'mentoring_impact',
  INNOVATION_SCORE: 'innovation_score'
};

export const PERFORMANCE_MEASUREMENT_PERIODS = {
  DAILY: 'daily',
  WEEKLY: 'weekly',
  MONTHLY: 'monthly',
  QUARTERLY: 'quarterly',
  ANNUALLY: 'annually',
  PROJECT_BASED: 'project_based',
  MILESTONE_BASED: 'milestone_based'
};

// ============================================================================
// PERFORMANCE METRICS SYSTEM CLASS
// ============================================================================

export class PerformanceMetricsSystem {
  constructor() {
    this.metrics = new Map();
    this.performanceHistory = [];
    this.bonusCalculations = [];
  }

  /**
   * Define a performance metric for tracking
   */
  defineMetric(metricDefinition) {
    const metric = {
      id: metricDefinition.id || this.generateMetricId(),
      name: metricDefinition.name,
      type: metricDefinition.type,
      description: metricDefinition.description,
      
      // Measurement configuration
      measurementPeriod: metricDefinition.measurementPeriod || PERFORMANCE_MEASUREMENT_PERIODS.MONTHLY,
      dataSource: metricDefinition.dataSource, // Where to get the data
      calculationMethod: metricDefinition.calculationMethod,
      
      // Target and threshold configuration
      targets: {
        minimum: metricDefinition.targets?.minimum || 0,
        target: metricDefinition.targets?.target || 100,
        stretch: metricDefinition.targets?.stretch || 150,
        maximum: metricDefinition.targets?.maximum || null
      },
      
      // Scoring configuration
      scoring: {
        method: metricDefinition.scoring?.method || 'linear', // linear, exponential, threshold
        weight: metricDefinition.scoring?.weight || 1.0,
        scale: metricDefinition.scoring?.scale || { min: 0, max: 100 }
      },
      
      // Bonus configuration
      bonusStructure: metricDefinition.bonusStructure || null,
      
      // Metadata
      industry: metricDefinition.industry,
      collaborationType: metricDefinition.collaborationType,
      isActive: true,
      createdAt: new Date().toISOString()
    };

    this.metrics.set(metric.id, metric);
    return metric;
  }

  /**
   * Record performance data for a contributor
   */
  recordPerformance(contributorId, metricId, value, period, metadata = {}) {
    const metric = this.metrics.get(metricId);
    if (!metric) {
      throw new Error(`Metric ${metricId} not found`);
    }

    const performanceRecord = {
      id: this.generateRecordId(),
      contributorId,
      metricId,
      metricName: metric.name,
      value,
      period,
      recordedAt: new Date().toISOString(),
      
      // Calculated fields
      score: this.calculateMetricScore(metric, value),
      percentageOfTarget: this.calculateTargetPercentage(metric, value),
      performanceLevel: this.determinePerformanceLevel(metric, value),
      
      // Metadata
      metadata,
      dataSource: metadata.dataSource || 'manual',
      verificationStatus: metadata.verified || false
    };

    this.performanceHistory.push(performanceRecord);
    return performanceRecord;
  }

  /**
   * Calculate performance score for a metric value
   */
  calculateMetricScore(metric, value) {
    const { targets, scoring } = metric;
    
    switch (scoring.method) {
      case 'linear':
        return this.calculateLinearScore(value, targets, scoring.scale);
      
      case 'exponential':
        return this.calculateExponentialScore(value, targets, scoring.scale);
      
      case 'threshold':
        return this.calculateThresholdScore(value, targets, scoring.scale);
      
      default:
        return this.calculateLinearScore(value, targets, scoring.scale);
    }
  }

  /**
   * Calculate linear performance score
   */
  calculateLinearScore(value, targets, scale) {
    if (value <= targets.minimum) {
      return scale.min;
    }
    
    if (value >= targets.stretch) {
      return scale.max;
    }
    
    // Linear interpolation between minimum and stretch
    const range = targets.stretch - targets.minimum;
    const position = (value - targets.minimum) / range;
    const scoreRange = scale.max - scale.min;
    
    return scale.min + (position * scoreRange);
  }

  /**
   * Calculate exponential performance score (rewards high performance more)
   */
  calculateExponentialScore(value, targets, scale) {
    const linearScore = this.calculateLinearScore(value, targets, { min: 0, max: 1 });
    const exponentialScore = Math.pow(linearScore, 0.5); // Square root for gentler curve
    
    return scale.min + (exponentialScore * (scale.max - scale.min));
  }

  /**
   * Calculate threshold-based score (step function)
   */
  calculateThresholdScore(value, targets, scale) {
    if (value >= targets.stretch) {
      return scale.max;
    } else if (value >= targets.target) {
      return scale.max * 0.8;
    } else if (value >= targets.minimum) {
      return scale.max * 0.5;
    } else {
      return scale.min;
    }
  }

  /**
   * Calculate percentage of target achieved
   */
  calculateTargetPercentage(metric, value) {
    if (metric.targets.target === 0) return 0;
    return (value / metric.targets.target) * 100;
  }

  /**
   * Determine performance level based on value
   */
  determinePerformanceLevel(metric, value) {
    const { targets } = metric;
    
    if (value >= targets.stretch) {
      return 'exceptional';
    } else if (value >= targets.target) {
      return 'exceeds_expectations';
    } else if (value >= targets.minimum) {
      return 'meets_expectations';
    } else {
      return 'below_expectations';
    }
  }

  /**
   * Calculate performance-based bonuses for a contributor
   */
  calculatePerformanceBonus(contributorId, period, bonusConfiguration) {
    const contributorPerformance = this.getContributorPerformance(contributorId, period);
    
    if (contributorPerformance.length === 0) {
      return {
        contributorId,
        period,
        totalBonus: 0,
        bonusBreakdown: [],
        overallScore: 0
      };
    }

    const bonusBreakdown = [];
    let totalBonus = 0;
    let weightedScoreSum = 0;
    let totalWeight = 0;

    // Calculate bonus for each metric
    for (const performance of contributorPerformance) {
      const metric = this.metrics.get(performance.metricId);
      if (!metric || !metric.bonusStructure) continue;

      const metricBonus = this.calculateMetricBonus(performance, metric, bonusConfiguration);
      
      if (metricBonus.amount > 0) {
        bonusBreakdown.push(metricBonus);
        totalBonus += metricBonus.amount;
      }

      // Calculate weighted score for overall performance
      weightedScoreSum += performance.score * metric.scoring.weight;
      totalWeight += metric.scoring.weight;
    }

    const overallScore = totalWeight > 0 ? weightedScoreSum / totalWeight : 0;

    // Apply overall performance multipliers
    const performanceMultiplier = this.getPerformanceMultiplier(overallScore, bonusConfiguration);
    const finalBonus = totalBonus * performanceMultiplier;

    const bonusCalculation = {
      contributorId,
      period,
      overallScore,
      performanceLevel: this.getOverallPerformanceLevel(overallScore),
      baseBonus: totalBonus,
      performanceMultiplier,
      totalBonus: finalBonus,
      bonusBreakdown,
      calculatedAt: new Date().toISOString()
    };

    this.bonusCalculations.push(bonusCalculation);
    return bonusCalculation;
  }

  /**
   * Calculate bonus for a specific metric
   */
  calculateMetricBonus(performance, metric, bonusConfiguration) {
    const bonusStructure = metric.bonusStructure;
    let bonusAmount = 0;
    let bonusType = 'none';
    let bonusDetails = {};

    switch (bonusStructure.type) {
      case 'percentage_of_base':
        if (performance.performanceLevel === 'exceptional') {
          bonusAmount = bonusConfiguration.baseSalary * (bonusStructure.percentage / 100);
          bonusType = 'exceptional_performance';
        }
        break;

      case 'fixed_amount':
        if (performance.score >= bonusStructure.threshold) {
          bonusAmount = bonusStructure.amount;
          bonusType = 'threshold_achievement';
        }
        break;

      case 'tiered':
        const tier = this.findBonusTier(performance.score, bonusStructure.tiers);
        if (tier) {
          bonusAmount = tier.amount;
          bonusType = 'tiered_performance';
          bonusDetails = { tier: tier.name, threshold: tier.threshold };
        }
        break;

      case 'linear_scale':
        if (performance.score >= bonusStructure.minimumScore) {
          const scoreRange = 100 - bonusStructure.minimumScore;
          const achievedRange = performance.score - bonusStructure.minimumScore;
          const bonusPercentage = (achievedRange / scoreRange) * bonusStructure.maxPercentage;
          bonusAmount = bonusConfiguration.baseSalary * (bonusPercentage / 100);
          bonusType = 'linear_scale';
        }
        break;
    }

    return {
      metricId: metric.id,
      metricName: metric.name,
      performanceValue: performance.value,
      performanceScore: performance.score,
      bonusType,
      amount: bonusAmount,
      details: bonusDetails
    };
  }

  /**
   * Find appropriate bonus tier for a score
   */
  findBonusTier(score, tiers) {
    const qualifyingTiers = tiers.filter(tier => score >= tier.threshold);
    if (qualifyingTiers.length === 0) return null;
    
    // Return the highest qualifying tier
    return qualifyingTiers.sort((a, b) => b.threshold - a.threshold)[0];
  }

  /**
   * Get performance multiplier based on overall score
   */
  getPerformanceMultiplier(overallScore, bonusConfiguration) {
    const multipliers = bonusConfiguration.performanceMultipliers || {
      exceptional: 1.5,      // 90+ score
      exceeds: 1.2,         // 80-89 score
      meets: 1.0,           // 70-79 score
      below: 0.5            // <70 score
    };

    if (overallScore >= 90) return multipliers.exceptional;
    if (overallScore >= 80) return multipliers.exceeds;
    if (overallScore >= 70) return multipliers.meets;
    return multipliers.below;
  }

  /**
   * Get overall performance level from score
   */
  getOverallPerformanceLevel(score) {
    if (score >= 90) return 'exceptional';
    if (score >= 80) return 'exceeds_expectations';
    if (score >= 70) return 'meets_expectations';
    return 'below_expectations';
  }

  /**
   * Get contributor performance for a specific period
   */
  getContributorPerformance(contributorId, period) {
    return this.performanceHistory.filter(record => 
      record.contributorId === contributorId && 
      record.period === period
    );
  }

  /**
   * Get performance analytics for a contributor
   */
  getPerformanceAnalytics(contributorId, options = {}) {
    const { startDate, endDate, metricIds } = options;
    
    let records = this.performanceHistory.filter(record => 
      record.contributorId === contributorId
    );

    // Filter by date range if provided
    if (startDate || endDate) {
      records = records.filter(record => {
        const recordDate = new Date(record.recordedAt);
        if (startDate && recordDate < new Date(startDate)) return false;
        if (endDate && recordDate > new Date(endDate)) return false;
        return true;
      });
    }

    // Filter by specific metrics if provided
    if (metricIds && metricIds.length > 0) {
      records = records.filter(record => metricIds.includes(record.metricId));
    }

    // Calculate analytics
    const analytics = {
      totalRecords: records.length,
      averageScore: this.calculateAverageScore(records),
      performanceTrend: this.calculatePerformanceTrend(records),
      metricBreakdown: this.calculateMetricBreakdown(records),
      achievementSummary: this.calculateAchievementSummary(records),
      improvementAreas: this.identifyImprovementAreas(records)
    };

    return analytics;
  }

  /**
   * Calculate average performance score
   */
  calculateAverageScore(records) {
    if (records.length === 0) return 0;
    const totalScore = records.reduce((sum, record) => sum + record.score, 0);
    return totalScore / records.length;
  }

  /**
   * Calculate performance trend over time
   */
  calculatePerformanceTrend(records) {
    if (records.length < 2) return 'insufficient_data';
    
    const sortedRecords = records.sort((a, b) => new Date(a.recordedAt) - new Date(b.recordedAt));
    const firstHalf = sortedRecords.slice(0, Math.floor(sortedRecords.length / 2));
    const secondHalf = sortedRecords.slice(Math.floor(sortedRecords.length / 2));
    
    const firstHalfAvg = this.calculateAverageScore(firstHalf);
    const secondHalfAvg = this.calculateAverageScore(secondHalf);
    
    const improvement = secondHalfAvg - firstHalfAvg;
    
    if (improvement > 5) return 'improving';
    if (improvement < -5) return 'declining';
    return 'stable';
  }

  /**
   * Calculate breakdown by metric
   */
  calculateMetricBreakdown(records) {
    const breakdown = {};
    
    records.forEach(record => {
      if (!breakdown[record.metricId]) {
        breakdown[record.metricId] = {
          metricName: record.metricName,
          count: 0,
          averageScore: 0,
          totalScore: 0,
          bestScore: 0,
          worstScore: 100
        };
      }
      
      const metric = breakdown[record.metricId];
      metric.count++;
      metric.totalScore += record.score;
      metric.averageScore = metric.totalScore / metric.count;
      metric.bestScore = Math.max(metric.bestScore, record.score);
      metric.worstScore = Math.min(metric.worstScore, record.score);
    });
    
    return breakdown;
  }

  /**
   * Calculate achievement summary
   */
  calculateAchievementSummary(records) {
    const summary = {
      exceptional: 0,
      exceeds_expectations: 0,
      meets_expectations: 0,
      below_expectations: 0
    };
    
    records.forEach(record => {
      summary[record.performanceLevel]++;
    });
    
    return summary;
  }

  /**
   * Identify areas for improvement
   */
  identifyImprovementAreas(records) {
    const metricBreakdown = this.calculateMetricBreakdown(records);
    const improvementAreas = [];
    
    Object.values(metricBreakdown).forEach(metric => {
      if (metric.averageScore < 70) {
        improvementAreas.push({
          metricName: metric.metricName,
          averageScore: metric.averageScore,
          priority: metric.averageScore < 50 ? 'high' : 'medium'
        });
      }
    });
    
    return improvementAreas.sort((a, b) => a.averageScore - b.averageScore);
  }

  /**
   * Generate unique metric ID
   */
  generateMetricId() {
    return 'metric_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  /**
   * Generate unique record ID
   */
  generateRecordId() {
    return 'record_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  /**
   * Get all defined metrics
   */
  getAllMetrics() {
    return Array.from(this.metrics.values());
  }

  /**
   * Get metrics by industry or collaboration type
   */
  getMetricsByCategory(industry, collaborationType) {
    return Array.from(this.metrics.values()).filter(metric => 
      (!industry || metric.industry === industry) &&
      (!collaborationType || metric.collaborationType === collaborationType)
    );
  }

  /**
   * Export performance data
   */
  exportPerformanceData(contributorId, format = 'json') {
    const data = {
      contributorId,
      metrics: this.getAllMetrics(),
      performanceHistory: this.getContributorPerformance(contributorId),
      bonusCalculations: this.bonusCalculations.filter(calc => calc.contributorId === contributorId),
      analytics: this.getPerformanceAnalytics(contributorId),
      exportedAt: new Date().toISOString()
    };
    
    if (format === 'csv') {
      return this.convertToCSV(data);
    }
    
    return data;
  }

  /**
   * Convert data to CSV format
   */
  convertToCSV(data) {
    // Implementation for CSV conversion
    // This would convert the performance data to CSV format
    return 'CSV conversion not implemented yet';
  }
}

// ============================================================================
// INDUSTRY-SPECIFIC METRIC TEMPLATES
// ============================================================================

export const INDUSTRY_METRIC_TEMPLATES = {
  technology: {
    software_development: [
      {
        name: 'Code Quality Score',
        type: PERFORMANCE_METRIC_TYPES.CODE_QUALITY_SCORE,
        targets: { minimum: 70, target: 85, stretch: 95 },
        bonusStructure: { type: 'threshold', threshold: 90, amount: 500 }
      },
      {
        name: 'Sprint Velocity',
        type: PERFORMANCE_METRIC_TYPES.COMPLETION_RATE,
        targets: { minimum: 80, target: 100, stretch: 120 },
        bonusStructure: { type: 'linear_scale', minimumScore: 80, maxPercentage: 10 }
      }
    ]
  },
  
  creative: {
    music_production: [
      {
        name: 'Track Completion Rate',
        type: PERFORMANCE_METRIC_TYPES.COMPLETION_RATE,
        targets: { minimum: 80, target: 95, stretch: 100 },
        bonusStructure: { type: 'fixed_amount', threshold: 95, amount: 1000 }
      }
    ]
  },
  
  service: {
    consulting: [
      {
        name: 'Client Satisfaction',
        type: PERFORMANCE_METRIC_TYPES.CUSTOMER_SATISFACTION,
        targets: { minimum: 3.5, target: 4.5, stretch: 5.0 },
        bonusStructure: { type: 'percentage_of_base', percentage: 15 }
      }
    ]
  }
};

// Export the performance metrics system
export const performanceMetricsSystem = new PerformanceMetricsSystem();
