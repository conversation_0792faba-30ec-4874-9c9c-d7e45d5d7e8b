// Collaboration Requests API
// Handles project partnership requests and opportunities
// Based on docs/design-system/systems/social-system.md

const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_ANON_KEY;

const headers = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Content-Type': 'application/json'
};

exports.handler = async (event, context) => {
  // Handle CORS preflight
  if (event.httpMethod === 'OPTIONS') {
    return { statusCode: 200, headers, body: '' };
  }

  try {
    const supabase = createClient(supabaseUrl, supabaseKey);
    
    // Get user from authorization header
    const authHeader = event.headers.authorization;
    if (!authHeader) {
      return {
        statusCode: 401,
        headers,
        body: JSON.stringify({ error: 'Authorization header required' })
      };
    }

    const token = authHeader.replace('Bearer ', '');
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    
    if (authError || !user) {
      return {
        statusCode: 401,
        headers,
        body: JSON.stringify({ error: 'Invalid authentication token' })
      };
    }

    const { httpMethod, path } = event;
    const pathParts = path.split('/').filter(part => part);
    
    switch (httpMethod) {
      case 'GET':
        return await handleGet(supabase, user, pathParts, event.queryStringParameters);
      case 'POST':
        return await handlePost(supabase, user, pathParts, JSON.parse(event.body || '{}'));
      case 'PUT':
        return await handlePut(supabase, user, pathParts, JSON.parse(event.body || '{}'));
      default:
        return {
          statusCode: 405,
          headers,
          body: JSON.stringify({ error: 'Method not allowed' })
        };
    }
  } catch (error) {
    console.error('API Error:', error);
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ error: 'Internal server error', details: error.message })
    };
  }
};

// GET handlers
async function handleGet(supabase, user, pathParts, queryParams) {
  if (pathParts.length === 1) {
    // GET /collaboration-requests - get collaboration opportunities
    return await getCollaborationRequests(supabase, user.id, queryParams);
  } else if (pathParts.length === 2) {
    // GET /collaboration-requests/:id - get specific request
    const requestId = pathParts[1];
    return await getCollaborationRequest(supabase, user.id, requestId);
  }
  
  return {
    statusCode: 404,
    headers,
    body: JSON.stringify({ error: 'Endpoint not found' })
  };
}

// Get collaboration requests
async function getCollaborationRequests(supabase, userId, queryParams) {
  const { 
    type = 'available', 
    status = 'open', 
    limit = 20, 
    offset = 0,
    skills,
    project_type,
    experience_level
  } = queryParams || {};
  
  let query = supabase
    .from('collaboration_requests')
    .select(`
      id,
      project_title,
      project_description,
      required_skills,
      budget_range_min,
      budget_range_max,
      timeline_weeks,
      project_type,
      experience_level,
      availability_requirement,
      additional_requirements,
      status,
      response_deadline,
      created_at,
      requester:requester_id(id, display_name, email),
      target_user:target_user_id(id, display_name, email)
    `)
    .order('created_at', { ascending: false })
    .range(offset, offset + limit - 1);

  if (type === 'my_requests') {
    query = query.eq('requester_id', userId);
  } else if (type === 'targeted') {
    query = query.eq('target_user_id', userId);
  } else if (type === 'available') {
    // Show public requests and network requests for allies
    query = query.or(`target_audience.eq.public,and(target_audience.eq.network,requester_id.in.(
      SELECT CASE 
        WHEN user_id = '${userId}' THEN ally_id 
        ELSE user_id 
      END 
      FROM user_allies 
      WHERE (user_id = '${userId}' OR ally_id = '${userId}') 
      AND status = 'accepted'
    ))`);
  }

  if (status) {
    query = query.eq('status', status);
  }

  if (project_type) {
    query = query.eq('project_type', project_type);
  }

  if (experience_level) {
    query = query.eq('experience_level', experience_level);
  }

  const { data, error } = await query;

  if (error) {
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ error: 'Failed to fetch collaboration requests', details: error.message })
    };
  }

  // Filter by skills if provided
  let filteredData = data;
  if (skills) {
    const skillsArray = Array.isArray(skills) ? skills : [skills];
    filteredData = data.filter(request => 
      request.required_skills && 
      skillsArray.some(skill => 
        request.required_skills.some(reqSkill => 
          reqSkill.toLowerCase().includes(skill.toLowerCase())
        )
      )
    );
  }

  return {
    statusCode: 200,
    headers,
    body: JSON.stringify({
      requests: filteredData,
      total: filteredData.length,
      hasMore: filteredData.length === limit
    })
  };
}

// Get specific collaboration request
async function getCollaborationRequest(supabase, userId, requestId) {
  const { data, error } = await supabase
    .from('collaboration_requests')
    .select(`
      id,
      project_title,
      project_description,
      required_skills,
      budget_range_min,
      budget_range_max,
      timeline_weeks,
      project_type,
      experience_level,
      availability_requirement,
      additional_requirements,
      status,
      response_deadline,
      created_at,
      updated_at,
      requester:requester_id(id, display_name, email),
      target_user:target_user_id(id, display_name, email)
    `)
    .eq('id', requestId)
    .single();

  if (error) {
    return {
      statusCode: 404,
      headers,
      body: JSON.stringify({ error: 'Collaboration request not found' })
    };
  }

  // Check if user has access to this request
  const hasAccess = 
    data.requester.id === userId ||
    data.target_user?.id === userId ||
    data.target_audience === 'public' ||
    (data.target_audience === 'network' && await isAlly(supabase, userId, data.requester.id));

  if (!hasAccess) {
    return {
      statusCode: 403,
      headers,
      body: JSON.stringify({ error: 'Access denied to this collaboration request' })
    };
  }

  return {
    statusCode: 200,
    headers,
    body: JSON.stringify({ request: data })
  };
}

// Check if users are allies
async function isAlly(supabase, userId1, userId2) {
  const { data } = await supabase
    .from('user_allies')
    .select('id')
    .or(`and(user_id.eq.${userId1},ally_id.eq.${userId2}),and(user_id.eq.${userId2},ally_id.eq.${userId1})`)
    .eq('status', 'accepted')
    .single();
  
  return !!data;
}

// POST handlers
async function handlePost(supabase, user, pathParts, body) {
  if (pathParts.length === 1) {
    // POST /collaboration-requests - create collaboration request
    return await createCollaborationRequest(supabase, user.id, body);
  }
  
  return {
    statusCode: 404,
    headers,
    body: JSON.stringify({ error: 'Endpoint not found' })
  };
}

// Create collaboration request
async function createCollaborationRequest(supabase, userId, body) {
  const {
    target_user_id,
    target_audience = 'specific',
    project_title,
    project_description,
    required_skills,
    budget_range_min,
    budget_range_max,
    timeline_weeks,
    project_type,
    experience_level,
    availability_requirement,
    additional_requirements,
    response_deadline
  } = body;

  if (!project_title || !project_description || !required_skills) {
    return {
      statusCode: 400,
      headers,
      body: JSON.stringify({ 
        error: 'project_title, project_description, and required_skills are required' 
      })
    };
  }

  // Validate target audience
  if (!['specific', 'network', 'public'].includes(target_audience)) {
    return {
      statusCode: 400,
      headers,
      body: JSON.stringify({ 
        error: 'target_audience must be specific, network, or public' 
      })
    };
  }

  // If specific target, verify ally relationship
  if (target_audience === 'specific' && target_user_id) {
    const isAllyRelation = await isAlly(supabase, userId, target_user_id);
    if (!isAllyRelation) {
      return {
        statusCode: 403,
        headers,
        body: JSON.stringify({ error: 'Can only send specific requests to allies' })
      };
    }
  }

  const { data, error } = await supabase
    .from('collaboration_requests')
    .insert({
      requester_id: userId,
      target_user_id: target_audience === 'specific' ? target_user_id : null,
      target_audience,
      project_title,
      project_description,
      required_skills: Array.isArray(required_skills) ? required_skills : [required_skills],
      budget_range_min,
      budget_range_max,
      timeline_weeks,
      project_type,
      experience_level,
      availability_requirement,
      additional_requirements,
      response_deadline: response_deadline ? new Date(response_deadline).toISOString() : null
    })
    .select(`
      id,
      project_title,
      project_description,
      required_skills,
      budget_range_min,
      budget_range_max,
      timeline_weeks,
      project_type,
      experience_level,
      availability_requirement,
      additional_requirements,
      status,
      response_deadline,
      created_at,
      requester:requester_id(id, display_name)
    `)
    .single();

  if (error) {
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ error: 'Failed to create collaboration request', details: error.message })
    };
  }

  return {
    statusCode: 201,
    headers,
    body: JSON.stringify({
      message: 'Collaboration request created successfully',
      request: data
    })
  };
}

// PUT handlers
async function handlePut(supabase, user, pathParts, body) {
  if (pathParts.length === 3 && pathParts[2] === 'respond') {
    // PUT /collaboration-requests/:id/respond - respond to collaboration request
    const requestId = pathParts[1];
    return await respondToCollaborationRequest(supabase, user.id, requestId, body);
  }

  return {
    statusCode: 404,
    headers,
    body: JSON.stringify({ error: 'Endpoint not found' })
  };
}

// Respond to collaboration request
async function respondToCollaborationRequest(supabase, userId, requestId, body) {
  const { response, message } = body;

  if (!response || !['interested', 'accepted', 'declined'].includes(response)) {
    return {
      statusCode: 400,
      headers,
      body: JSON.stringify({
        error: 'response must be interested, accepted, or declined'
      })
    };
  }

  // Get the collaboration request
  const { data: request, error: fetchError } = await supabase
    .from('collaboration_requests')
    .select('*')
    .eq('id', requestId)
    .single();

  if (fetchError || !request) {
    return {
      statusCode: 404,
      headers,
      body: JSON.stringify({ error: 'Collaboration request not found' })
    };
  }

  // Check if user can respond to this request
  const canRespond =
    request.target_user_id === userId ||
    request.target_audience === 'public' ||
    (request.target_audience === 'network' && await isAlly(supabase, userId, request.requester_id));

  if (!canRespond) {
    return {
      statusCode: 403,
      headers,
      body: JSON.stringify({ error: 'Not authorized to respond to this request' })
    };
  }

  // Update request status based on response
  let newStatus;
  switch (response) {
    case 'interested':
      newStatus = 'in_review';
      break;
    case 'accepted':
      newStatus = 'accepted';
      break;
    case 'declined':
      newStatus = 'declined';
      break;
  }

  const { data, error } = await supabase
    .from('collaboration_requests')
    .update({
      status: newStatus,
      updated_at: new Date().toISOString()
    })
    .eq('id', requestId)
    .select()
    .single();

  if (error) {
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ error: 'Failed to update collaboration request', details: error.message })
    };
  }

  // TODO: Send notification to requester
  // TODO: If accepted, create project/venture connection

  return {
    statusCode: 200,
    headers,
    body: JSON.stringify({
      message: `Collaboration request ${response} successfully`,
      request: data
    })
  };
}
