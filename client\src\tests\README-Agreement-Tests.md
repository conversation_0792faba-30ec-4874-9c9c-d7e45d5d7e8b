# Comprehensive Agreement System Tests

This directory contains comprehensive unit tests for the alliance, venture, and contributor agreement generation systems in the Royaltea project. The tests validate all aspects of the dynamic contributor management system and ensure generated agreements match the lawyer-approved template format.

## Test Suite Overview

### 🎯 Test Coverage

The test suite covers all confirmed use cases from previous discussions:

1. **Alliance Creation and Management**
   - Multiple alliance configurations
   - Different industries and collaboration types
   - Legal framework validation

2. **Venture Setup and Configuration**
   - Dynamic contributor management scenarios
   - Unified pool revenue distribution (default)
   - Tranche-based revenue sharing for software ventures
   - Variable team composition scenarios
   - Adding contributors over time without renegotiating existing agreements

3. **Agreement Generation and Validation**
   - Complete document comparison against lawyer-approved template
   - Format and structure verification
   - Variable replacement validation
   - Integration between venture creation flow and agreement generation

4. **Specific Scenarios**
   - CoG (City of Gamers) alliance testing
   - VOTA (Village of The Ages) venture testing
   - Dynamic contributor addition scenarios
   - Late joiner contributor management

## Test Files

### Core Test Files

1. **`cog-vota-scenario.test.js`**
   - Specific testing for City of Gamers alliance and Village of The Ages venture
   - Tests all dynamic contributor management features
   - Validates tranche-based revenue sharing
   - Tests late joiner scenarios

2. **`agreement-document-comparison.test.js`**
   - Compares generated agreements against lawyer-approved template
   - Validates document structure and format
   - Checks for proper variable replacement
   - Generates detailed comparison reports

3. **`comprehensive-agreement-validation.test.js`**
   - Full system validation across all components
   - Tests integration between alliance, venture, and agreement systems
   - Validates unified pool revenue distribution
   - Tests variable team composition scenarios

### Test Runners

4. **`run-all-agreement-tests.js`**
   - Master test runner that executes all test suites
   - Provides comprehensive reporting
   - Handles test execution coordination

5. **`run-comprehensive-agreement-tests.js`**
   - Specific runner for comprehensive validation tests
   - Detailed logging and error reporting

## Running the Tests

### Prerequisites

Ensure you have the following dependencies installed:
```bash
npm install
```

### Quick Start - Run All Tests

To run the complete test suite:

```bash
# From the client directory
cd client
node src/tests/run-all-agreement-tests.js
```

### Individual Test Suites

Run specific test suites individually:

```bash
# CoG/VOTA scenario tests
node src/tests/cog-vota-scenario.test.js

# Document comparison tests
node src/tests/agreement-document-comparison.test.js

# Comprehensive validation tests
node src/tests/comprehensive-agreement-validation.test.js
```

### Test Output

All tests generate detailed output files in the `src/tests/output/` directory:

- **Agreement Files**: Generated agreements for each test scenario
- **Validation Reports**: Detailed comparison reports against the lawyer template
- **Summary Reports**: Overall test results and recommendations
- **Execution Logs**: Complete test execution logs

## Test Scenarios

### CoG/VOTA Scenario

The primary test scenario focuses on:

- **Alliance**: City of Gamers Inc.
- **Venture**: Village of The Ages (village simulation game)
- **Contributors**: 
  - Lead Developer (Technical Lead)
  - Game Designer (Creative Director)
  - Late Joiner Developer (Backend Developer)
  - UI/UX Designer (added mid-project)

### Dynamic Contributor Management Features

1. **Unified Pool Revenue Distribution**
   - All contributors earn from the same revenue pool
   - No separate core team vs gigwork pools
   - Revenue distributed based on contribution points

2. **Tranche-Based Revenue Sharing**
   - Contributors earn only from releases they actively contributed to
   - Release-based tranches (v1.0, v1.1, v1.2)
   - Minimum contribution thresholds

3. **Variable Team Composition**
   - Support for adding contributors over time
   - No need to renegotiate existing agreements
   - Late joiners only participate in current/future tranches

## Validation Criteria

### Agreement Validation

Generated agreements are validated against the lawyer-approved template for:

1. **Structural Elements**
   - All required sections present
   - Proper heading structure
   - Complete legal framework

2. **Content Accuracy**
   - Correct company information (for user's ventures, not City of Gamers Inc)
   - Proper contributor details
   - Accurate project information

3. **Format Compliance**
   - No unreplaced template variables
   - No unfilled placeholders
   - Proper legal language and formatting

4. **Business Logic**
   - Revenue sharing calculations
   - Tranche participation rules
   - IP ownership models

### Success Criteria

For production readiness, agreements must achieve:

- **>95% structural compliance** with lawyer template
- **100% variable replacement** (no unreplaced {{variables}})
- **Complete legal sections** (all required sections present)
- **Accurate business terms** (revenue sharing, IP rights, etc.)

## Output Analysis

### Generated Files

Each test run generates:

1. **Individual Agreements** (`.md` files)
   - One agreement per contributor per scenario
   - Includes metadata headers with test information
   - Ready for legal review

2. **Validation Reports** (`.md` files)
   - Detailed comparison against lawyer template
   - Section-by-section analysis
   - Recommendations for improvements

3. **Summary Reports** (`.md` files)
   - Overall test results
   - Success/failure statistics
   - System readiness assessment

### Key Metrics

The tests track:

- **Agreement Generation Success Rate**
- **Template Compliance Score**
- **Variable Replacement Accuracy**
- **Integration Flow Success**
- **Dynamic Contributor Management Validation**

## Troubleshooting

### Common Issues

1. **Template Not Found**
   - Ensure `client/public/example-cog-contributor-agreement.md` exists
   - Check file permissions

2. **Agreement Generation Failures**
   - Verify NewAgreementGenerator is properly imported
   - Check project data structure
   - Review error logs in output directory

3. **Validation Failures**
   - Review comparison reports for specific issues
   - Check for missing template sections
   - Verify variable replacement logic

### Debug Mode

For detailed debugging, set verbose mode in test configuration:

```javascript
const config = {
  verbose: true,
  saveResults: true,
  performValidation: true
};
```

## Integration with CI/CD

These tests can be integrated into continuous integration pipelines:

```bash
# Add to package.json scripts
"test:agreements": "node src/tests/run-all-agreement-tests.js",
"test:cog-vota": "node src/tests/cog-vota-scenario.test.js"
```

## Contributing

When adding new test scenarios:

1. Follow the existing test structure
2. Include proper validation against the lawyer template
3. Generate comprehensive output files
4. Update this README with new scenarios

## Legal Compliance

⚠️ **Important**: These tests generate agreements for users' own projects and companies, not for City of Gamers Inc. The lawyer-approved template serves as a format model for what other people's agreements should look like.

Generated agreements are:
- **Contributor agreements** between venture owners and their contributors
- **Customized for each user's specific venture/alliance**
- **Based on the lawyer-approved template format**
- **Ready for legal review and customization**

## Support

For issues with the test suite:

1. Check the output logs in `src/tests/output/`
2. Review validation reports for specific failures
3. Ensure all dependencies are properly installed
4. Verify the lawyer template file is accessible

The test suite provides comprehensive validation of the agreement generation system and ensures all dynamic contributor management features work correctly according to the specified requirements.
