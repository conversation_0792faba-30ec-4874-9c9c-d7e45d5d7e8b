// Social Features API endpoints for messaging, connections, and activity feeds
const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_KEY
);

// CORS headers
const headers = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Content-Type': 'application/json'
};

// Helper function to get user from JWT token
const getUserFromToken = async (authHeader) => {
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    throw new Error('No valid authorization header');
  }

  const token = authHeader.replace('Bearer ', '');
  const { data: { user }, error } = await supabase.auth.getUser(token);
  
  if (error || !user) {
    throw new Error('Invalid or expired token');
  }
  
  return user;
};

// Send friend request (using notifications table for now)
const sendFriendRequest = async (event) => {
  try {
    const user = await getUserFromToken(event.headers.authorization);
    const { target_user_id, message } = JSON.parse(event.body);

    if (!target_user_id) {
      throw new Error('target_user_id is required');
    }

    // Check if request already exists
    const { data: existingRequest, error: checkError } = await supabase
      .from('notifications')
      .select('*')
      .eq('user_id', target_user_id)
      .eq('type', 'friend_request')
      .eq('metadata->from_user_id', user.id)
      .eq('is_read', false)
      .single();

    if (existingRequest) {
      throw new Error('Friend request already sent');
    }

    // Create friend request notification
    const { data: notification, error } = await supabase
      .from('notifications')
      .insert({
        user_id: target_user_id,
        type: 'friend_request',
        title: 'New Connection Request',
        message: message || 'Someone wants to connect with you',
        metadata: {
          from_user_id: user.id,
          from_user_name: user.user_metadata?.display_name || user.email,
          request_message: message
        }
      })
      .select()
      .single();

    if (error) throw error;

    return {
      statusCode: 200,
      headers,
      body: JSON.stringify({
        success: true,
        request_id: notification.id
      })
    };
  } catch (error) {
    console.error('Error sending friend request:', error);
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({
        success: false,
        error: error.message
      })
    };
  }
};

// Respond to friend request
const respondToFriendRequest = async (event) => {
  try {
    const user = await getUserFromToken(event.headers.authorization);
    const { request_id, response } = JSON.parse(event.body);

    if (!request_id || !response) {
      throw new Error('request_id and response are required');
    }

    // Get the friend request
    const { data: request, error: fetchError } = await supabase
      .from('notifications')
      .select('*')
      .eq('id', request_id)
      .eq('user_id', user.id)
      .eq('type', 'friend_request')
      .single();

    if (fetchError || !request) {
      throw new Error('Friend request not found');
    }

    // Mark request as read
    const { error: updateError } = await supabase
      .from('notifications')
      .update({ is_read: true })
      .eq('id', request_id);

    if (updateError) throw updateError;

    // Store connection in user_preferences for now
    if (response === 'accept') {
      const fromUserId = request.metadata.from_user_id;
      
      // Update both users' preferences to include the connection
      const { data: userPrefs, error: prefsError } = await supabase
        .from('user_preferences')
        .select('*')
        .eq('user_id', user.id)
        .single();

      if (!prefsError && userPrefs) {
        const connections = userPrefs.feature_flags?.connections || [];
        const updatedConnections = [...connections, {
          user_id: fromUserId,
          connected_at: new Date().toISOString(),
          status: 'accepted'
        }];

        await supabase
          .from('user_preferences')
          .update({
            feature_flags: {
              ...userPrefs.feature_flags,
              connections: updatedConnections
            }
          })
          .eq('user_id', user.id);
      }

      // Create response notification
      await supabase
        .from('notifications')
        .insert({
          user_id: fromUserId,
          type: 'friend_request_response',
          title: 'Connection Accepted',
          message: 'Your connection request was accepted!',
          metadata: {
            response_user_id: user.id,
            response: 'accepted'
          }
        });
    }

    return {
      statusCode: 200,
      headers,
      body: JSON.stringify({
        success: true,
        response
      })
    };
  } catch (error) {
    console.error('Error responding to friend request:', error);
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({
        success: false,
        error: error.message
      })
    };
  }
};

// Send message (using notifications for now)
const sendMessage = async (event) => {
  try {
    const user = await getUserFromToken(event.headers.authorization);
    const { recipient_id, content, message_type = 'text' } = JSON.parse(event.body);

    if (!recipient_id || !content) {
      throw new Error('recipient_id and content are required');
    }

    // Create message notification
    const { data: message, error } = await supabase
      .from('notifications')
      .insert({
        user_id: recipient_id,
        type: 'message',
        title: 'New Message',
        message: content.substring(0, 100),
        metadata: {
          from_user_id: user.id,
          from_user_name: user.user_metadata?.display_name || user.email,
          full_content: content,
          message_type,
          sent_at: new Date().toISOString()
        },
        category: 'message'
      })
      .select()
      .single();

    if (error) throw error;

    return {
      statusCode: 200,
      headers,
      body: JSON.stringify({
        success: true,
        message_id: message.id
      })
    };
  } catch (error) {
    console.error('Error sending message:', error);
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({
        success: false,
        error: error.message
      })
    };
  }
};

// Get conversations
const getConversations = async (event) => {
  try {
    const user = await getUserFromToken(event.headers.authorization);

    // Get message notifications for this user
    const { data: messages, error } = await supabase
      .from('notifications')
      .select('*')
      .eq('user_id', user.id)
      .eq('type', 'message')
      .order('created_at', { ascending: false })
      .limit(50);

    if (error) throw error;

    // Group messages by sender
    const conversations = {};
    messages.forEach(msg => {
      const fromUserId = msg.metadata?.from_user_id;
      if (fromUserId) {
        if (!conversations[fromUserId]) {
          conversations[fromUserId] = {
            user_id: fromUserId,
            user_name: msg.metadata?.from_user_name,
            messages: [],
            last_message_at: msg.created_at,
            unread_count: 0
          };
        }
        conversations[fromUserId].messages.push({
          id: msg.id,
          content: msg.metadata?.full_content || msg.message,
          sent_at: msg.metadata?.sent_at || msg.created_at,
          is_read: msg.is_read
        });
        if (!msg.is_read) {
          conversations[fromUserId].unread_count++;
        }
      }
    });

    return {
      statusCode: 200,
      headers,
      body: JSON.stringify({
        success: true,
        conversations: Object.values(conversations)
      })
    };
  } catch (error) {
    console.error('Error getting conversations:', error);
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({
        success: false,
        error: error.message
      })
    };
  }
};

// Create activity feed entry
const createActivity = async (event) => {
  try {
    const user = await getUserFromToken(event.headers.authorization);
    const { activity_type, title, description, visibility = 'public' } = JSON.parse(event.body);

    if (!activity_type || !title) {
      throw new Error('activity_type and title are required');
    }

    // Store activity in user_preferences for now
    const { data: userPrefs, error: prefsError } = await supabase
      .from('user_preferences')
      .select('*')
      .eq('user_id', user.id)
      .single();

    const activities = userPrefs?.feature_flags?.activities || [];
    const newActivity = {
      id: `activity_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      activity_type,
      title,
      description,
      visibility,
      created_at: new Date().toISOString(),
      likes_count: 0,
      comments_count: 0
    };

    const updatedActivities = [newActivity, ...activities].slice(0, 100); // Keep last 100

    const { error: updateError } = await supabase
      .from('user_preferences')
      .update({
        feature_flags: {
          ...userPrefs?.feature_flags,
          activities: updatedActivities
        }
      })
      .eq('user_id', user.id);

    if (updateError) throw updateError;

    return {
      statusCode: 200,
      headers,
      body: JSON.stringify({
        success: true,
        activity_id: newActivity.id
      })
    };
  } catch (error) {
    console.error('Error creating activity:', error);
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({
        success: false,
        error: error.message
      })
    };
  }
};

// Get activity feed
const getActivityFeed = async (event) => {
  try {
    const user = await getUserFromToken(event.headers.authorization);

    // Get activities from multiple users (simplified for now)
    const { data: allPrefs, error } = await supabase
      .from('user_preferences')
      .select('user_id, feature_flags')
      .not('feature_flags->activities', 'is', null)
      .limit(20);

    if (error) throw error;

    const activities = [];
    allPrefs.forEach(prefs => {
      const userActivities = prefs.feature_flags?.activities || [];
      userActivities.forEach(activity => {
        if (activity.visibility === 'public' || activity.user_id === user.id) {
          activities.push({
            ...activity,
            user_id: prefs.user_id
          });
        }
      });
    });

    // Sort by creation date
    activities.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));

    return {
      statusCode: 200,
      headers,
      body: JSON.stringify({
        success: true,
        activities: activities.slice(0, 20)
      })
    };
  } catch (error) {
    console.error('Error getting activity feed:', error);
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({
        success: false,
        error: error.message
      })
    };
  }
};

// Main handler
exports.handler = async (event, context) => {
  // Handle CORS preflight
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers,
      body: ''
    };
  }

  const path = event.path.replace('/.netlify/functions/social', '') || '/';
  const method = event.httpMethod;

  try {
    switch (`${method} ${path}`) {
      case 'POST /friend-request':
        return await sendFriendRequest(event);
      case 'PUT /friend-request/respond':
        return await respondToFriendRequest(event);
      case 'POST /message':
        return await sendMessage(event);
      case 'GET /conversations':
        return await getConversations(event);
      case 'POST /activity':
        return await createActivity(event);
      case 'GET /activity-feed':
        return await getActivityFeed(event);
      default:
        return {
          statusCode: 404,
          headers,
          body: JSON.stringify({
            success: false,
            error: 'Endpoint not found'
          })
        };
    }
  } catch (error) {
    console.error('Unhandled error in social handler:', error);
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({
        success: false,
        error: 'Internal server error'
      })
    };
  }
};
