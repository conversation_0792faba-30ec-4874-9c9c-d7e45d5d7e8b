// Simple test function using ES modules
export default async (req, context) => {
  return new Response(
    JSON.stringify({
      message: "This is a simple test function deployed with Netlify CLI",
      timestamp: new Date().toISOString(),
      environment: {
        node_version: process.version,
        netlify_dev: process.env.NETLIFY_DEV || false
      }
    }),
    {
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      }
    }
  );
};

// Configure the function path
export const config = {
  path: "/api/simple-test"
};
