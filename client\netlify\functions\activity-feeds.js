// Activity Feeds & Notifications API
// Backend Specialist: Real-time activity tracking and notification system
const { createClient } = require('@supabase/supabase-js');

// Supabase client
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_KEY
);

// Helper function to get user from request
const getUserFromRequest = (event) => {
  const authHeader = event.headers.authorization;
  if (!authHeader) return null;
  
  try {
    const token = authHeader.replace('Bearer ', '');
    const payload = JSON.parse(Buffer.from(token.split('.')[1], 'base64').toString());
    return payload.sub;
  } catch {
    return null;
  }
};

// Get Activity Feed
const getActivityFeed = async (event) => {
  try {
    const userId = getUserFromRequest(event);
    if (!userId) {
      return {
        statusCode: 401,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    const queryParams = new URLSearchParams(event.queryStringParameters || {});
    const feedType = queryParams.get('type') || 'all'; // 'personal', 'alliance', 'project', 'all'
    const activityType = queryParams.get('activity_type');
    const limit = parseInt(queryParams.get('limit') || '20');
    const before = queryParams.get('before'); // For pagination

    let query = supabase
      .from('activity_feeds')
      .select(`
        id,
        activity_type,
        activity_title,
        activity_description,
        view_count,
        reaction_count,
        created_at,
        metadata,
        actor:users!activity_feeds_actor_id_fkey(
          id,
          display_name,
          avatar_url
        ),
        target_user:users!activity_feeds_target_user_id_fkey(
          id,
          display_name,
          avatar_url
        ),
        project:projects(
          id,
          name,
          title
        ),
        alliance:teams(
          id,
          name
        ),
        conversation:conversations(
          id,
          title,
          conversation_type
        ),
        reactions:activity_reactions(
          id,
          reaction_type,
          user:users(
            id,
            display_name
          )
        )
      `)
      .order('created_at', { ascending: false })
      .limit(limit);

    // Apply feed type filters
    if (feedType === 'personal') {
      query = query.or(`actor_id.eq.${userId},target_user_id.eq.${userId}`);
    } else if (feedType === 'alliance') {
      // Get user's alliances
      const { data: userAlliances } = await supabase
        .from('team_members')
        .select('team_id')
        .eq('user_id', userId);

      if (userAlliances && userAlliances.length > 0) {
        const allianceIds = userAlliances.map(m => m.team_id);
        query = query.in('alliance_id', allianceIds);
      } else {
        // No alliances, return empty result
        return {
          statusCode: 200,
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ activities: [], total: 0 })
        };
      }
    } else if (feedType === 'project') {
      // Get user's projects
      const { data: userProjects } = await supabase
        .from('project_contributors')
        .select('project_id')
        .eq('user_id', userId);

      if (userProjects && userProjects.length > 0) {
        const projectIds = userProjects.map(p => p.project_id);
        query = query.in('project_id', projectIds);
      } else {
        // No projects, return empty result
        return {
          statusCode: 200,
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ activities: [], total: 0 })
        };
      }
    } else {
      // All activities visible to user
      const { data: userAlliances } = await supabase
        .from('team_members')
        .select('team_id')
        .eq('user_id', userId);

      const allianceIds = userAlliances?.map(m => m.team_id) || [];

      query = query.or(`
        visibility.eq.public,
        actor_id.eq.${userId},
        target_user_id.eq.${userId},
        alliance_id.in.(${allianceIds.join(',') || 'null'})
      `);
    }

    // Apply activity type filter
    if (activityType) {
      query = query.eq('activity_type', activityType);
    }

    // Apply pagination
    if (before) {
      query = query.lt('created_at', before);
    }

    const { data: activities, error: activitiesError } = await query;

    if (activitiesError) {
      throw new Error(`Failed to fetch activities: ${activitiesError.message}`);
    }

    // Mark activities as viewed
    if (activities && activities.length > 0) {
      const activityIds = activities.map(a => a.id);
      await supabase
        .from('activity_feeds')
        .update({ view_count: supabase.raw('view_count + 1') })
        .in('id', activityIds);
    }

    return {
      statusCode: 200,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        activities: activities,
        total: activities.length,
        has_more: activities.length === limit
      })
    };

  } catch (error) {
    console.error('Get activity feed error:', error);
    return {
      statusCode: 500,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ error: 'Failed to fetch activity feed' })
    };
  }
};

// Create Activity
const createActivity = async (event) => {
  try {
    const userId = getUserFromRequest(event);
    if (!userId) {
      return {
        statusCode: 401,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    const data = JSON.parse(event.body);
    
    if (!data.activity_type || !data.activity_title) {
      return {
        statusCode: 400,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          error: 'activity_type and activity_title are required' 
        })
      };
    }

    // Validate activity type
    const validActivityTypes = [
      'message_sent', 'file_shared', 'project_created', 'project_updated',
      'task_completed', 'alliance_joined', 'alliance_created', 'skill_endorsed',
      'friend_request_sent', 'friend_request_accepted', 'collaboration_started'
    ];

    if (!validActivityTypes.includes(data.activity_type)) {
      return {
        statusCode: 400,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Invalid activity type' })
      };
    }

    // Create activity
    const activityData = {
      activity_type: data.activity_type,
      activity_title: data.activity_title,
      activity_description: data.activity_description || null,
      actor_id: userId,
      target_user_id: data.target_user_id || null,
      project_id: data.project_id || null,
      alliance_id: data.alliance_id || null,
      conversation_id: data.conversation_id || null,
      message_id: data.message_id || null,
      file_id: data.file_id || null,
      metadata: data.metadata || {},
      visibility: data.visibility || 'alliance'
    };

    const { data: activity, error: activityError } = await supabase
      .from('activity_feeds')
      .insert([activityData])
      .select(`
        id,
        activity_type,
        activity_title,
        activity_description,
        created_at,
        actor:users!activity_feeds_actor_id_fkey(
          id,
          display_name,
          avatar_url
        )
      `)
      .single();

    if (activityError) {
      throw new Error(`Failed to create activity: ${activityError.message}`);
    }

    // Create notifications for relevant users
    await createActivityNotifications(activity, data);

    return {
      statusCode: 201,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ activity })
    };

  } catch (error) {
    console.error('Create activity error:', error);
    return {
      statusCode: 500,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ error: 'Failed to create activity' })
    };
  }
};

// React to Activity
const reactToActivity = async (event) => {
  try {
    const userId = getUserFromRequest(event);
    if (!userId) {
      return {
        statusCode: 401,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    const activityId = event.path.split('/')[1];
    const data = JSON.parse(event.body);
    
    if (!data.reaction_type) {
      return {
        statusCode: 400,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'reaction_type is required' })
      };
    }

    // Validate reaction type
    const validReactions = ['like', 'love', 'celebrate', 'support', 'insightful'];
    if (!validReactions.includes(data.reaction_type)) {
      return {
        statusCode: 400,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Invalid reaction type' })
      };
    }

    // Check if activity exists and user has access
    const { data: activity, error: activityError } = await supabase
      .from('activity_feeds')
      .select('id, actor_id, visibility, alliance_id')
      .eq('id', activityId)
      .single();

    if (activityError) {
      return {
        statusCode: 404,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Activity not found' })
      };
    }

    // Check if user can react to this activity
    const canReact = await checkActivityAccess(activity, userId);
    if (!canReact) {
      return {
        statusCode: 403,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Cannot react to this activity' })
      };
    }

    // Create or update reaction
    const { data: reaction, error: reactionError } = await supabase
      .from('activity_reactions')
      .upsert([{
        activity_id: activityId,
        user_id: userId,
        reaction_type: data.reaction_type
      }], { onConflict: 'activity_id,user_id,reaction_type' })
      .select(`
        id,
        reaction_type,
        created_at,
        user:users(
          id,
          display_name,
          avatar_url
        )
      `)
      .single();

    if (reactionError) {
      throw new Error(`Failed to create reaction: ${reactionError.message}`);
    }

    // Update activity reaction count
    await supabase
      .from('activity_feeds')
      .update({ reaction_count: supabase.raw('reaction_count + 1') })
      .eq('id', activityId);

    return {
      statusCode: 201,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ reaction })
    };

  } catch (error) {
    console.error('React to activity error:', error);
    return {
      statusCode: 500,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ error: 'Failed to react to activity' })
    };
  }
};

// Get Notifications
const getNotifications = async (event) => {
  try {
    const userId = getUserFromRequest(event);
    if (!userId) {
      return {
        statusCode: 401,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    const queryParams = new URLSearchParams(event.queryStringParameters || {});
    const unreadOnly = queryParams.get('unread_only') === 'true';
    const limit = parseInt(queryParams.get('limit') || '20');

    let query = supabase
      .from('notifications')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(limit);

    if (unreadOnly) {
      query = query.eq('is_read', false);
    }

    const { data: notifications, error: notificationsError } = await query;

    if (notificationsError) {
      throw new Error(`Failed to fetch notifications: ${notificationsError.message}`);
    }

    return {
      statusCode: 200,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        notifications: notifications,
        total: notifications.length
      })
    };

  } catch (error) {
    console.error('Get notifications error:', error);
    return {
      statusCode: 500,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ error: 'Failed to fetch notifications' })
    };
  }
};

// Mark Notification as Read
const markNotificationRead = async (event) => {
  try {
    const userId = getUserFromRequest(event);
    if (!userId) {
      return {
        statusCode: 401,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    const notificationId = event.path.split('/').pop();

    const { error: updateError } = await supabase
      .from('notifications')
      .update({ is_read: true })
      .eq('id', notificationId)
      .eq('user_id', userId);

    if (updateError) {
      throw new Error(`Failed to mark notification as read: ${updateError.message}`);
    }

    return {
      statusCode: 200,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ message: 'Notification marked as read' })
    };

  } catch (error) {
    console.error('Mark notification read error:', error);
    return {
      statusCode: 500,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ error: 'Failed to mark notification as read' })
    };
  }
};

// Helper function to check activity access
const checkActivityAccess = async (activity, userId) => {
  try {
    if (activity.visibility === 'public') {
      return true;
    }

    if (activity.actor_id === userId) {
      return true;
    }

    if (activity.visibility === 'alliance' && activity.alliance_id) {
      const { data: membership } = await supabase
        .from('team_members')
        .select('id')
        .eq('team_id', activity.alliance_id)
        .eq('user_id', userId)
        .single();

      return !!membership;
    }

    return false;
  } catch (error) {
    console.error('Check activity access error:', error);
    return false;
  }
};

// Helper function to create activity notifications
const createActivityNotifications = async (activity, data) => {
  try {
    const notifications = [];

    // Notify target user if specified
    if (data.target_user_id && data.target_user_id !== activity.actor.id) {
      notifications.push({
        user_id: data.target_user_id,
        type: activity.activity_type,
        title: activity.activity_title,
        message: activity.activity_description,
        related_id: activity.id,
        metadata: { activity_id: activity.id }
      });
    }

    // Notify alliance members for alliance activities
    if (data.alliance_id && activity.visibility === 'alliance') {
      const { data: members } = await supabase
        .from('team_members')
        .select('user_id')
        .eq('team_id', data.alliance_id)
        .neq('user_id', activity.actor.id);

      members?.forEach(member => {
        notifications.push({
          user_id: member.user_id,
          type: activity.activity_type,
          title: activity.activity_title,
          message: activity.activity_description,
          related_id: activity.id,
          metadata: { activity_id: activity.id, alliance_id: data.alliance_id }
        });
      });
    }

    if (notifications.length > 0) {
      await supabase
        .from('notifications')
        .insert(notifications);
    }

  } catch (error) {
    console.error('Create activity notifications error:', error);
  }
};

// Route requests to appropriate handlers
exports.handler = async (event, context) => {
  // Add CORS headers
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS'
  };

  // Handle preflight requests
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers,
      body: ''
    };
  }

  const path = event.path.replace('/.netlify/functions/activity-feeds', '');

  try {
    let response;

    if (event.httpMethod === 'GET') {
      if (path === '' || path === '/') {
        response = await getActivityFeed(event);
      } else if (path === '/notifications') {
        response = await getNotifications(event);
      } else {
        response = {
          statusCode: 404,
          body: JSON.stringify({ error: 'Endpoint not found' })
        };
      }
    } else if (event.httpMethod === 'POST') {
      if (path === '' || path === '/') {
        response = await createActivity(event);
      } else if (path.includes('/react')) {
        response = await reactToActivity(event);
      } else {
        response = {
          statusCode: 404,
          body: JSON.stringify({ error: 'Endpoint not found' })
        };
      }
    } else if (event.httpMethod === 'PUT') {
      if (path.includes('/notifications/') && path.includes('/read')) {
        response = await markNotificationRead(event);
      } else {
        response = {
          statusCode: 404,
          body: JSON.stringify({ error: 'Endpoint not found' })
        };
      }
    } else {
      response = {
        statusCode: 405,
        body: JSON.stringify({ error: 'Method not allowed' })
      };
    }

    // Add CORS headers to response
    response.headers = { ...response.headers, ...headers };
    return response;

  } catch (error) {
    console.error('Activity Feeds API error:', error);
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ error: 'Internal server error' })
    };
  }
};
