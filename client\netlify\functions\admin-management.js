// Admin Management API
// Authentication & Security Agent: Comprehensive admin management endpoints
// Created: January 16, 2025

const { createClient } = require('@supabase/supabase-js');
const { authMiddleware, logSecurityEvent, defaultHeaders } = require('./auth-middleware');

// Initialize Supabase client
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_KEY
);

// Helper function to log admin actions
async function logAdminAction(adminId, actionType, targetType, targetId, targetIdentifier, reason, details = {}, previousState = {}, newState = {}) {
  try {
    const { data, error } = await supabase
      .from('admin_actions')
      .insert([{
        admin_id: adminId,
        action_type: actionType,
        target_type: targetType,
        target_id: targetId,
        target_identifier: targetIdentifier,
        reason: reason,
        details: details,
        previous_state: previousState,
        new_state: newState
      }]);
    
    if (error) {
      console.error('Error logging admin action:', error);
    }
    
    return data;
  } catch (error) {
    console.error('Error in logAdminAction:', error);
  }
}

// Get all users with admin filtering
async function getUsers(event) {
  try {
    const { page = 1, limit = 50, search, status, role } = event.queryStringParameters || {};
    
    let query = supabase
      .from('users')
      .select(`
        id,
        email,
        display_name,
        avatar_url,
        date_created,
        last_login_at,
        login_count,
        status,
        is_admin,
        admin_role,
        suspension_reason,
        suspended_until
      `)
      .order('date_created', { ascending: false });
    
    // Apply filters
    if (search) {
      query = query.or(`email.ilike.%${search}%,display_name.ilike.%${search}%`);
    }
    
    if (status) {
      query = query.eq('status', status);
    }
    
    if (role) {
      if (role === 'admin') {
        query = query.eq('is_admin', true);
      } else if (role === 'user') {
        query = query.eq('is_admin', false);
      }
    }
    
    // Apply pagination
    const offset = (page - 1) * limit;
    query = query.range(offset, offset + limit - 1);
    
    const { data: users, error, count } = await query;
    
    if (error) throw error;
    
    return {
      statusCode: 200,
      headers: defaultHeaders,
      body: JSON.stringify({
        users,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: count,
          pages: Math.ceil(count / limit)
        }
      })
    };
  } catch (error) {
    console.error('Error fetching users:', error);
    return {
      statusCode: 500,
      headers: defaultHeaders,
      body: JSON.stringify({
        error: 'Internal server error',
        message: 'Failed to fetch users'
      })
    };
  }
}

// Update user status (suspend, ban, activate)
async function updateUserStatus(event) {
  try {
    const { userId } = event.pathParameters || {};
    const { status, reason, duration } = JSON.parse(event.body || '{}');
    
    if (!userId || !status) {
      return {
        statusCode: 400,
        headers: defaultHeaders,
        body: JSON.stringify({
          error: 'Bad request',
          message: 'User ID and status are required'
        })
      };
    }
    
    // Get current user data
    const { data: currentUser, error: fetchError } = await supabase
      .from('users')
      .select('*')
      .eq('id', userId)
      .single();
    
    if (fetchError || !currentUser) {
      return {
        statusCode: 404,
        headers: defaultHeaders,
        body: JSON.stringify({
          error: 'Not found',
          message: 'User not found'
        })
      };
    }
    
    // Prepare update data
    const updateData = {
      status: status,
      suspension_reason: reason || null,
      suspended_by: event.user.id
    };
    
    // Set suspension end time if duration is provided
    if (duration && status === 'suspended') {
      const suspendedUntil = new Date();
      suspendedUntil.setHours(suspendedUntil.getHours() + parseInt(duration));
      updateData.suspended_until = suspendedUntil.toISOString();
    }
    
    // Update user
    const { data: updatedUser, error: updateError } = await supabase
      .from('users')
      .update(updateData)
      .eq('id', userId)
      .select()
      .single();
    
    if (updateError) throw updateError;
    
    // Log admin action
    await logAdminAction(
      event.user.id,
      'user_status_change',
      'user',
      userId,
      currentUser.email,
      reason || `Status changed to ${status}`,
      { duration: duration },
      { status: currentUser.status },
      { status: status }
    );
    
    // Log security event
    await logSecurityEvent(
      'user_status_changed',
      status === 'banned' ? 'error' : 'warning',
      userId,
      `User status changed from ${currentUser.status} to ${status}`,
      {
        admin_id: event.user.id,
        reason: reason,
        duration: duration
      },
      status === 'banned' ? 80 : 50
    );
    
    return {
      statusCode: 200,
      headers: defaultHeaders,
      body: JSON.stringify({
        message: 'User status updated successfully',
        user: updatedUser
      })
    };
  } catch (error) {
    console.error('Error updating user status:', error);
    return {
      statusCode: 500,
      headers: defaultHeaders,
      body: JSON.stringify({
        error: 'Internal server error',
        message: 'Failed to update user status'
      })
    };
  }
}

// Update user admin role
async function updateUserRole(event) {
  try {
    const { userId } = event.pathParameters || {};
    const { isAdmin, adminRole, reason } = JSON.parse(event.body || '{}');
    
    if (!userId) {
      return {
        statusCode: 400,
        headers: defaultHeaders,
        body: JSON.stringify({
          error: 'Bad request',
          message: 'User ID is required'
        })
      };
    }
    
    // Only super admins can change admin roles
    if (event.user.admin_role !== 'super_admin') {
      return {
        statusCode: 403,
        headers: defaultHeaders,
        body: JSON.stringify({
          error: 'Forbidden',
          message: 'Only super admins can change admin roles'
        })
      };
    }
    
    // Get current user data
    const { data: currentUser, error: fetchError } = await supabase
      .from('users')
      .select('*')
      .eq('id', userId)
      .single();
    
    if (fetchError || !currentUser) {
      return {
        statusCode: 404,
        headers: defaultHeaders,
        body: JSON.stringify({
          error: 'Not found',
          message: 'User not found'
        })
      };
    }
    
    // Update user role
    const { data: updatedUser, error: updateError } = await supabase
      .from('users')
      .update({
        is_admin: isAdmin,
        admin_role: adminRole
      })
      .eq('id', userId)
      .select()
      .single();
    
    if (updateError) throw updateError;
    
    // Log admin action
    await logAdminAction(
      event.user.id,
      'user_role_change',
      'user',
      userId,
      currentUser.email,
      reason || `Admin role changed`,
      {},
      { is_admin: currentUser.is_admin, admin_role: currentUser.admin_role },
      { is_admin: isAdmin, admin_role: adminRole }
    );
    
    return {
      statusCode: 200,
      headers: defaultHeaders,
      body: JSON.stringify({
        message: 'User role updated successfully',
        user: updatedUser
      })
    };
  } catch (error) {
    console.error('Error updating user role:', error);
    return {
      statusCode: 500,
      headers: defaultHeaders,
      body: JSON.stringify({
        error: 'Internal server error',
        message: 'Failed to update user role'
      })
    };
  }
}

// Get admin actions log
async function getAdminActions(event) {
  try {
    const { page = 1, limit = 50, adminId, actionType, targetType } = event.queryStringParameters || {};
    
    let query = supabase
      .from('admin_actions')
      .select(`
        *,
        admin:admin_id (
          email,
          display_name
        )
      `)
      .order('created_at', { ascending: false });
    
    // Apply filters
    if (adminId) {
      query = query.eq('admin_id', adminId);
    }
    
    if (actionType) {
      query = query.eq('action_type', actionType);
    }
    
    if (targetType) {
      query = query.eq('target_type', targetType);
    }
    
    // Apply pagination
    const offset = (page - 1) * limit;
    query = query.range(offset, offset + limit - 1);
    
    const { data: actions, error, count } = await query;
    
    if (error) throw error;
    
    return {
      statusCode: 200,
      headers: defaultHeaders,
      body: JSON.stringify({
        actions,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: count,
          pages: Math.ceil(count / limit)
        }
      })
    };
  } catch (error) {
    console.error('Error fetching admin actions:', error);
    return {
      statusCode: 500,
      headers: defaultHeaders,
      body: JSON.stringify({
        error: 'Internal server error',
        message: 'Failed to fetch admin actions'
      })
    };
  }
}

// Main handler with middleware
exports.handler = async (event, context) => {
  // Apply authentication middleware
  const authResult = await authMiddleware({
    requireAuth: true,
    requireAdmin: true,
    adminRole: 'platform_admin'
  })(event, context);
  
  if (authResult) {
    return authResult; // Return error response from middleware
  }
  
  try {
    const path = event.path.replace('/.netlify/functions/admin-management', '');
    const method = event.httpMethod;
    
    // Route requests
    if (method === 'GET' && path === '/users') {
      return await getUsers(event);
    } else if (method === 'PUT' && path.match(/^\/users\/[^\/]+\/status$/)) {
      return await updateUserStatus(event);
    } else if (method === 'PUT' && path.match(/^\/users\/[^\/]+\/role$/)) {
      return await updateUserRole(event);
    } else if (method === 'GET' && path === '/actions') {
      return await getAdminActions(event);
    } else {
      return {
        statusCode: 404,
        headers: defaultHeaders,
        body: JSON.stringify({
          error: 'Not found',
          message: 'Endpoint not found'
        })
      };
    }
  } catch (error) {
    console.error('Error in admin management handler:', error);
    return {
      statusCode: 500,
      headers: defaultHeaders,
      body: JSON.stringify({
        error: 'Internal server error',
        message: 'An unexpected error occurred'
      })
    };
  }
};
