// Venture Management API
// Backend Specialist: Comprehensive venture system with revenue models and milestone tracking
const { createClient } = require('@supabase/supabase-js');

// Supabase client
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_KEY
);

// Helper function to get user from request
const getUserFromRequest = (event) => {
  const authHeader = event.headers.authorization;
  if (!authHeader) return null;
  
  try {
    const token = authHeader.replace('Bearer ', '');
    const payload = JSON.parse(Buffer.from(token.split('.')[1], 'base64').toString());
    return payload.sub;
  } catch {
    return null;
  }
};

// Helper function to check venture permissions
const checkVenturePermission = async (userId, ventureId, requiredRoles = ['founder', 'owner', 'admin']) => {
  const { data: venture } = await supabase
    .from('projects')
    .select('alliance_id, created_by')
    .eq('id', ventureId)
    .single();

  if (!venture) return false;

  // Check if user is venture creator
  if (venture.created_by === userId) return true;

  // Check alliance membership and role
  const { data: member } = await supabase
    .from('team_members')
    .select('role, status')
    .eq('team_id', venture.alliance_id)
    .eq('user_id', userId)
    .eq('status', 'active')
    .single();
  
  return member && requiredRoles.includes(member.role);
};

// Helper function to validate revenue model
const validateRevenueModel = (revenueModel) => {
  const validationErrors = [];
  
  if (!revenueModel || typeof revenueModel !== 'object') {
    return ['Revenue model must be a valid object'];
  }
  
  // Validate revenue type
  const validTypes = ['subscription', 'one_time', 'commission', 'royalty', 'hybrid'];
  if (revenueModel.type && !validTypes.includes(revenueModel.type)) {
    validationErrors.push('Invalid revenue type');
  }
  
  // Validate pricing
  if (revenueModel.pricing) {
    const { base_price, currency, billing_cycle } = revenueModel.pricing;
    
    if (base_price !== undefined) {
      const price = parseFloat(base_price);
      if (isNaN(price) || price < 0) {
        validationErrors.push('Base price must be a positive number');
      }
    }
    
    if (currency && !/^[A-Z]{3}$/.test(currency)) {
      validationErrors.push('Currency must be a valid 3-letter code');
    }
    
    if (billing_cycle && !['monthly', 'quarterly', 'yearly', 'one_time'].includes(billing_cycle)) {
      validationErrors.push('Invalid billing cycle');
    }
  }
  
  // Validate distribution
  if (revenueModel.distribution) {
    const { alliance_share, platform_share, contributor_share } = revenueModel.distribution;
    const total = (alliance_share || 0) + (platform_share || 0) + (contributor_share || 0);
    
    if (Math.abs(total - 100) > 0.01) {
      validationErrors.push('Revenue distribution must total 100%');
    }
  }
  
  return validationErrors;
};

// Create Venture
const createVenture = async (event) => {
  try {
    const userId = getUserFromRequest(event);
    if (!userId) {
      return {
        statusCode: 401,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    const data = JSON.parse(event.body);
    
    // Validate required fields
    if (!data.name || !data.description || !data.alliance_id) {
      return {
        statusCode: 400,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          error: 'Name, description, and alliance_id are required' 
        })
      };
    }

    // Check if user has permission to create ventures in the alliance
    const { data: membership } = await supabase
      .from('team_members')
      .select('role, status')
      .eq('team_id', data.alliance_id)
      .eq('user_id', userId)
      .eq('status', 'active')
      .single();

    if (!membership || !['founder', 'owner', 'admin', 'member'].includes(membership.role)) {
      return {
        statusCode: 403,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Insufficient permissions to create ventures' })
      };
    }

    // Validate venture type
    const validVentureTypes = ['software', 'game', 'film', 'music', 'art', 'business', 'research', 'other'];
    if (data.venture_type && !validVentureTypes.includes(data.venture_type)) {
      return {
        statusCode: 400,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Invalid venture type' })
      };
    }

    // Validate revenue model if provided
    if (data.revenue_model) {
      const revenueErrors = validateRevenueModel(data.revenue_model);
      if (revenueErrors.length > 0) {
        return {
          statusCode: 400,
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ error: 'Revenue model validation failed', details: revenueErrors })
        };
      }
    }

    // Create venture
    const ventureData = {
      name: data.name,
      title: data.title || data.name,
      description: data.description,
      alliance_id: data.alliance_id,
      venture_type: data.venture_type || 'software',
      revenue_model: data.revenue_model || {},
      milestone_config: data.milestone_config || {},
      status: 'planning',
      is_active: true,
      created_by: userId,
      start_date: data.start_date || new Date().toISOString(),
      target_completion_date: data.target_completion_date || null
    };

    const { data: venture, error: ventureError } = await supabase
      .from('projects')
      .insert([ventureData])
      .select(`
        id,
        name,
        title,
        description,
        venture_type,
        revenue_model,
        milestone_config,
        status,
        created_at,
        start_date,
        target_completion_date,
        alliance:teams!projects_alliance_id_fkey(
          id,
          name,
          alliance_type
        )
      `)
      .single();

    if (ventureError) {
      throw new Error(`Failed to create venture: ${ventureError.message}`);
    }

    // Add creator as project contributor
    const contributorData = {
      project_id: venture.id,
      user_id: userId,
      role: 'lead',
      status: 'active',
      joined_at: new Date().toISOString()
    };

    const { error: contributorError } = await supabase
      .from('project_contributors')
      .insert([contributorData]);

    if (contributorError) {
      console.warn('Failed to add creator as contributor:', contributorError);
    }

    return {
      statusCode: 201,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ venture })
    };

  } catch (error) {
    console.error('Create venture error:', error);
    return {
      statusCode: 500,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ error: 'Failed to create venture' })
    };
  }
};

// Get Venture Details
const getVenture = async (event) => {
  try {
    const userId = getUserFromRequest(event);
    if (!userId) {
      return {
        statusCode: 401,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    const ventureId = event.path.split('/').pop();

    // Get venture with alliance and contributor details
    const { data: venture, error: ventureError } = await supabase
      .from('projects')
      .select(`
        id,
        name,
        title,
        description,
        venture_type,
        revenue_model,
        milestone_config,
        status,
        is_active,
        created_at,
        updated_at,
        start_date,
        target_completion_date,
        created_by,
        alliance:teams!projects_alliance_id_fkey(
          id,
          name,
          description,
          alliance_type,
          industry
        ),
        contributors:project_contributors(
          id,
          user_id,
          role,
          status,
          joined_at,
          users(
            id,
            display_name,
            email,
            avatar_url
          )
        )
      `)
      .eq('id', ventureId)
      .single();

    if (ventureError) {
      return {
        statusCode: 404,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Venture not found' })
      };
    }

    // Check if user has access to this venture
    const userContributor = venture.contributors.find(c => c.user_id === userId);
    const { data: allianceMember } = await supabase
      .from('team_members')
      .select('role, status')
      .eq('team_id', venture.alliance.id)
      .eq('user_id', userId)
      .eq('status', 'active')
      .single();

    if (!userContributor && !allianceMember) {
      return {
        statusCode: 403,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Access denied' })
      };
    }

    // Get venture milestones
    const { data: milestones } = await supabase
      .from('milestones')
      .select('*')
      .eq('project_id', ventureId)
      .order('target_date', { ascending: true });

    // Get venture tasks/missions
    const { data: tasks } = await supabase
      .from('tasks')
      .select(`
        id,
        title,
        description,
        task_category,
        priority,
        status,
        deadline,
        bounty_amount,
        bounty_currency,
        assigned_to,
        created_by
      `)
      .eq('project_id', ventureId)
      .order('created_at', { ascending: false })
      .limit(10);

    // Calculate venture statistics
    const stats = {
      total_contributors: venture.contributors.length,
      active_contributors: venture.contributors.filter(c => c.status === 'active').length,
      total_milestones: milestones?.length || 0,
      completed_milestones: milestones?.filter(m => m.status === 'completed').length || 0,
      total_tasks: tasks?.length || 0,
      completed_tasks: tasks?.filter(t => t.status === 'completed').length || 0,
      total_bounty_value: tasks?.reduce((sum, t) => sum + (t.bounty_amount || 0), 0) || 0
    };

    const response = {
      ...venture,
      milestones: milestones || [],
      recent_tasks: tasks || [],
      statistics: stats,
      user_role: userContributor?.role || 'viewer',
      user_alliance_role: allianceMember?.role || null
    };

    return {
      statusCode: 200,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(response)
    };

  } catch (error) {
    console.error('Get venture error:', error);
    return {
      statusCode: 500,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ error: 'Failed to fetch venture' })
    };
  }
};

// Update Venture
const updateVenture = async (event) => {
  try {
    const userId = getUserFromRequest(event);
    if (!userId) {
      return {
        statusCode: 401,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    const ventureId = event.path.split('/').pop();
    const data = JSON.parse(event.body);

    // Check if user has permission to update this venture
    const hasPermission = await checkVenturePermission(userId, ventureId, ['founder', 'owner', 'admin']);
    if (!hasPermission) {
      // Check if user is venture lead
      const { data: contributor } = await supabase
        .from('project_contributors')
        .select('role')
        .eq('project_id', ventureId)
        .eq('user_id', userId)
        .eq('role', 'lead')
        .single();

      if (!contributor) {
        return {
          statusCode: 403,
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ error: 'Insufficient permissions to update venture' })
        };
      }
    }

    // Validate revenue model if provided
    if (data.revenue_model) {
      const revenueErrors = validateRevenueModel(data.revenue_model);
      if (revenueErrors.length > 0) {
        return {
          statusCode: 400,
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ error: 'Revenue model validation failed', details: revenueErrors })
        };
      }
    }

    // Validate venture type if provided
    if (data.venture_type) {
      const validVentureTypes = ['software', 'game', 'film', 'music', 'art', 'business', 'research', 'other'];
      if (!validVentureTypes.includes(data.venture_type)) {
        return {
          statusCode: 400,
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ error: 'Invalid venture type' })
        };
      }
    }

    // Validate status if provided
    if (data.status) {
      const validStatuses = ['planning', 'active', 'on_hold', 'completed', 'cancelled', 'archived'];
      if (!validStatuses.includes(data.status)) {
        return {
          statusCode: 400,
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ error: 'Invalid status' })
        };
      }
    }

    // Update venture
    const updateData = {};
    if (data.name) updateData.name = data.name;
    if (data.title) updateData.title = data.title;
    if (data.description) updateData.description = data.description;
    if (data.venture_type) updateData.venture_type = data.venture_type;
    if (data.revenue_model) updateData.revenue_model = data.revenue_model;
    if (data.milestone_config) updateData.milestone_config = data.milestone_config;
    if (data.status) updateData.status = data.status;
    if (data.is_active !== undefined) updateData.is_active = data.is_active;
    if (data.target_completion_date !== undefined) updateData.target_completion_date = data.target_completion_date;
    
    updateData.updated_at = new Date().toISOString();

    const { data: venture, error: updateError } = await supabase
      .from('projects')
      .update(updateData)
      .eq('id', ventureId)
      .select(`
        id,
        name,
        title,
        description,
        venture_type,
        revenue_model,
        milestone_config,
        status,
        is_active,
        updated_at,
        target_completion_date
      `)
      .single();

    if (updateError) {
      throw new Error(`Failed to update venture: ${updateError.message}`);
    }

    return {
      statusCode: 200,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ venture })
    };

  } catch (error) {
    console.error('Update venture error:', error);
    return {
      statusCode: 500,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ error: 'Failed to update venture' })
    };
  }
};

// Route requests to appropriate handlers
exports.handler = async (event, context) => {
  // Add CORS headers
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS'
  };

  // Handle preflight requests
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers,
      body: ''
    };
  }

  const path = event.path.replace('/.netlify/functions/venture-management', '');

  try {
    let response;

    if (event.httpMethod === 'GET') {
      if (path && path !== '/') {
        response = await getVenture(event);
      } else {
        response = {
          statusCode: 404,
          body: JSON.stringify({ error: 'Endpoint not found' })
        };
      }
    } else if (event.httpMethod === 'POST') {
      if (path === '' || path === '/') {
        response = await createVenture(event);
      } else {
        response = {
          statusCode: 404,
          body: JSON.stringify({ error: 'Endpoint not found' })
        };
      }
    } else if (event.httpMethod === 'PUT') {
      if (path && path !== '/') {
        response = await updateVenture(event);
      } else {
        response = {
          statusCode: 404,
          body: JSON.stringify({ error: 'Endpoint not found' })
        };
      }
    } else {
      response = {
        statusCode: 405,
        body: JSON.stringify({ error: 'Method not allowed' })
      };
    }

    // Add CORS headers to response
    response.headers = { ...response.headers, ...headers };
    return response;

  } catch (error) {
    console.error('Venture Management API error:', error);
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ error: 'Internal server error' })
    };
  }
};
