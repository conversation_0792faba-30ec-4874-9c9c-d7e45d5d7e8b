/**
 * Comprehensive Unit Tests for Unified Pool System
 * 
 * Tests all aspects of the updated dynamic contributor management system:
 * - Unified pool as default configuration
 * - Revenue model presets
 * - Contribution point calculations
 * - Revenue distribution (including bug fixes)
 * - Agreement generation with unified pool language
 */

import { describe, test, expect, beforeEach } from 'vitest';
import { dynamicContributorManagement, REVENUE_MODEL_PRESETS, REVENUE_CALCULATION_METHODS } from '../utils/agreement/dynamicContributorManagement.js';

describe('Unified Pool System Tests', () => {
  
  beforeEach(() => {
    // Clear any existing data before each test
    dynamicContributorManagement.ventures.clear();
    dynamicContributorManagement.tranches.clear();
    dynamicContributorManagement.revenueDistributions.clear();
  });

  // ============================================================================
  // REVENUE MODEL PRESETS TESTS
  // ============================================================================

  describe('Revenue Model Presets', () => {
    test('Should provide three revenue model presets', () => {
      const presets = dynamicContributorManagement.getRevenueModelPresets();
      
      expect(Object.keys(presets)).toHaveLength(3);
      expect(presets).toHaveProperty('UNIFIED_POOL');
      expect(presets).toHaveProperty('HYBRID_SAFETY_NET');
      expect(presets).toHaveProperty('SEPARATE_POOLS');
    });

    test('Unified Pool preset should have correct default values', () => {
      const presets = dynamicContributorManagement.getRevenueModelPresets();
      const unifiedPool = presets.UNIFIED_POOL;
      
      expect(unifiedPool.calculationMethod).toBe(REVENUE_CALCULATION_METHODS.CONTRIBUTION_POINTS);
      expect(unifiedPool.coreTeamReservedPercentage).toBe(0);
      expect(unifiedPool.gigworkPoolPercentage).toBe(90);
      expect(unifiedPool.platformFeePercentage).toBe(10);
      expect(unifiedPool.contributionPointsWeight).toBe(1.0);
      expect(unifiedPool.timeParticipationWeight).toBe(0.0);
    });

    test('Should apply revenue model presets correctly', () => {
      const ventureDefinition = {
        name: 'Test Venture',
        description: 'Test description'
      };

      const updatedDefinition = dynamicContributorManagement.applyRevenueModelPreset(
        ventureDefinition, 
        'UNIFIED_POOL'
      );

      expect(updatedDefinition.revenueModel.calculationMethod).toBe(REVENUE_CALCULATION_METHODS.CONTRIBUTION_POINTS);
      expect(updatedDefinition.revenueModel.gigworkPoolPercentage).toBe(90);
    });
  });

  // ============================================================================
  // VENTURE INITIALIZATION TESTS
  // ============================================================================

  describe('Venture Initialization with Unified Pool', () => {
    test('Should initialize venture with unified pool defaults', () => {
      const venture = dynamicContributorManagement.initializeScalableVenture({
        name: 'Test Unified Venture',
        description: 'Testing unified pool defaults',
        coreTeam: [
          {
            email: '<EMAIL>',
            role: 'Founder',
            responsibilities: ['Leadership']
          }
        ]
      });

      // Check default unified pool configuration
      expect(venture.revenueModel.calculationMethod).toBe(REVENUE_CALCULATION_METHODS.CONTRIBUTION_POINTS);
      expect(venture.revenueModel.coreTeamReservedPercentage).toBe(0);
      expect(venture.revenueModel.gigworkPoolPercentage).toBe(90);
      expect(venture.revenueModel.platformFeePercentage).toBe(10);
      expect(venture.revenueModel.contributionPointsWeight).toBe(1.0);

      // Check core team member configuration for unified pool
      const founder = venture.contributorPools.coreTeam[0];
      expect(founder.fixedRevenueShare).toBe(0); // No fixed share in unified pool
      expect(founder.guaranteedMinimumShare).toBe(0);
    });

    test('Should handle core team validation for unified pool', () => {
      // Should not throw error for unified pool even with high "revenue shares"
      expect(() => {
        dynamicContributorManagement.initializeScalableVenture({
          name: 'Test Venture',
          coreTeam: [
            {
              email: '<EMAIL>',
              role: 'Founder 1',
              revenueShare: 50 // This would normally be problematic, but unified pool ignores it
            },
            {
              email: '<EMAIL>',
              role: 'Founder 2',
              revenueShare: 50
            }
          ]
        });
      }).not.toThrow();
    });

    test('Should validate separate pools configuration', () => {
      // Should throw error for separate pools with excessive shares
      expect(() => {
        dynamicContributorManagement.initializeScalableVenture({
          name: 'Test Venture',
          revenueModel: REVENUE_MODEL_PRESETS.SEPARATE_POOLS,
          coreTeam: [
            {
              email: '<EMAIL>',
              role: 'Founder 1',
              revenueShare: 30
            },
            {
              email: '<EMAIL>',
              role: 'Founder 2',
              revenueShare: 30 // Total 60% > 50% reserved
            }
          ]
        });
      }).toThrow();
    });
  });

  // ============================================================================
  // CONTRIBUTION TRACKING TESTS
  // ============================================================================

  describe('Contribution Point Calculations', () => {
    let venture, tranche;

    beforeEach(() => {
      venture = dynamicContributorManagement.initializeScalableVenture({
        name: 'Test Venture',
        coreTeam: [
          {
            email: '<EMAIL>',
            role: 'Founder'
          }
        ],
        autoStartFirstTranche: true
      });

      tranche = Array.from(dynamicContributorManagement.tranches.values())[0];
    });

    test('Should calculate contribution points correctly', () => {
      const founder = venture.contributorPools.coreTeam[0];
      
      const contribution = dynamicContributorManagement.recordContribution(
        tranche.id,
        founder.id,
        {
          type: 'code',
          description: 'Core development work',
          hoursWorked: 40,
          difficultyLevel: 'expert',
          qualityRating: 5
        }
      );

      // Base points: 10 (code) * 40 (hours) = 400
      // Difficulty multiplier: 1.6 (expert)
      // Quality multiplier: 1.3 (rating 5)
      // Expected: 400 * 1.6 * 1.3 = 832
      expect(Math.round(contribution.finalPoints)).toBe(832);
    });

    test('Should track contribution points in tranche', () => {
      const founder = venture.contributorPools.coreTeam[0];
      
      dynamicContributorManagement.recordContribution(tranche.id, founder.id, {
        type: 'code',
        hoursWorked: 20,
        difficultyLevel: 'medium',
        qualityRating: 4
      });

      const points = tranche.contributionPoints.get(founder.id);
      expect(points).toBeGreaterThan(0);
      
      // Verify participation tracking
      const participation = tranche.activeContributors.find(p => p.contributorId === founder.id);
      expect(participation.contributionPoints).toBe(points);
      expect(participation.hoursWorked).toBe(20);
      expect(participation.tasksCompleted).toBe(1);
    });
  });

  // ============================================================================
  // UNIFIED POOL REVENUE DISTRIBUTION TESTS
  // ============================================================================

  describe('Unified Pool Revenue Distribution', () => {
    let venture, tranche;

    beforeEach(() => {
      venture = dynamicContributorManagement.initializeScalableVenture({
        name: 'Revenue Test Venture',
        coreTeam: [
          {
            email: '<EMAIL>',
            role: 'Technical Lead'
          },
          {
            email: '<EMAIL>',
            role: 'Product Lead'
          }
        ],
        autoStartFirstTranche: true
      });

      // Add gigwork contributor
      dynamicContributorManagement.addGigworkContributor(venture.id, {
        email: '<EMAIL>',
        role: 'Developer',
        experienceLevel: 'senior'
      });

      tranche = Array.from(dynamicContributorManagement.tranches.values())[0];
    });

    test('Should distribute revenue in unified pool correctly', () => {
      const alice = venture.contributorPools.coreTeam[0];
      const bob = venture.contributorPools.coreTeam[1];
      const carol = venture.contributorPools.gigwork[0];

      // Record contributions
      dynamicContributorManagement.recordContribution(tranche.id, alice.id, {
        type: 'code',
        hoursWorked: 40,
        difficultyLevel: 'expert',
        qualityRating: 5
      }); // 832 points

      dynamicContributorManagement.recordContribution(tranche.id, bob.id, {
        type: 'planning',
        hoursWorked: 30,
        difficultyLevel: 'hard',
        qualityRating: 5
      }); // 292.5 points

      dynamicContributorManagement.recordContribution(tranche.id, carol.id, {
        type: 'code',
        hoursWorked: 35,
        difficultyLevel: 'hard',
        qualityRating: 4
      }); // 598 points

      // Calculate revenue distribution
      const totalRevenue = 100000;
      const distribution = dynamicContributorManagement.calculateTrancheRevenueDistribution(
        tranche.id,
        totalRevenue
      );

      // Verify basic calculations
      expect(distribution.totalRevenue).toBe(totalRevenue);
      expect(distribution.platformFee).toBe(10000); // 10%
      expect(distribution.gigworkPool).toBe(90000); // 90% (unified pool)
      expect(distribution.coreTeamPool).toBe(0); // 0% in unified pool

      // Verify all contributors are in gigworkDistribution (unified pool)
      expect(Object.keys(distribution.gigworkDistribution)).toHaveLength(3);
      expect(Object.keys(distribution.coreTeamDistribution)).toHaveLength(0);

      // Verify revenue proportional to contribution points
      const aliceRevenue = distribution.gigworkDistribution[alice.id];
      const bobRevenue = distribution.gigworkDistribution[bob.id];
      const carolRevenue = distribution.gigworkDistribution[carol.id];

      expect(aliceRevenue).toBeGreaterThan(bobRevenue); // Alice contributed more
      expect(aliceRevenue).toBeGreaterThan(carolRevenue); // Alice contributed more
      expect(carolRevenue).toBeGreaterThan(bobRevenue); // Carol contributed more than Bob

      // Verify total distribution equals unified pool
      const totalDistributed = aliceRevenue + bobRevenue + carolRevenue;
      expect(Math.round(totalDistributed)).toBe(90000);
    });

    test('Should handle zero contribution points gracefully', () => {
      const distribution = dynamicContributorManagement.calculateTrancheRevenueDistribution(
        tranche.id,
        100000
      );

      expect(distribution.totalContributionPoints).toBe(0);
      expect(Object.keys(distribution.gigworkDistribution)).toHaveLength(0);
    });
  });

  // ============================================================================
  // AGREEMENT GENERATION TESTS
  // ============================================================================

  describe('Agreement Generation for Unified Pool', () => {
    let venture;

    beforeEach(() => {
      venture = dynamicContributorManagement.initializeScalableVenture({
        name: 'Agreement Test Venture',
        coreTeam: [
          {
            email: '<EMAIL>',
            role: 'Founder'
          }
        ]
      });

      dynamicContributorManagement.addGigworkContributor(venture.id, {
        email: '<EMAIL>',
        role: 'Developer'
      });
    });

    test('Should generate agreement with unified pool language', () => {
      const contributor = venture.contributorPools.gigwork[0];
      const agreement = dynamicContributorManagement.generateGigworkAgreement(
        venture.id,
        contributor.id
      );

      expect(agreement.terms.revenueSharing.method).toBe(REVENUE_CALCULATION_METHODS.CONTRIBUTION_POINTS);
      expect(agreement.terms.revenueSharing.isUnifiedPool).toBe(true);
      expect(agreement.terms.revenueSharing.contributorPoolPercentage).toBe(90);

      // Check content includes unified pool language
      expect(agreement.content).toContain('Unified Pool - Pure Contribution Points');
      expect(agreement.content).toContain('Unified Contributor Pool: 90% of total revenue shared among ALL contributors');
      expect(agreement.content).toContain('Equal treatment: All contributors (including founders) earn based on contribution');
      expect(agreement.content).toContain('UNIFIED POOL BENEFITS: Maximum fairness, simplicity, and equal opportunity');
    });

    test('Should generate different agreement for separate pools', () => {
      // Create venture with separate pools
      const separatePoolVenture = dynamicContributorManagement.initializeScalableVenture({
        name: 'Separate Pool Venture',
        revenueModel: REVENUE_MODEL_PRESETS.SEPARATE_POOLS,
        coreTeam: [
          {
            email: '<EMAIL>',
            role: 'Founder',
            revenueShare: 25
          }
        ]
      });

      dynamicContributorManagement.addGigworkContributor(separatePoolVenture.id, {
        email: '<EMAIL>',
        role: 'Developer'
      });

      const contributor = separatePoolVenture.contributorPools.gigwork[0];
      const agreement = dynamicContributorManagement.generateGigworkAgreement(
        separatePoolVenture.id,
        contributor.id
      );

      expect(agreement.terms.revenueSharing.isUnifiedPool).toBe(false);
      expect(agreement.content).toContain('Contribution Points Based');
      expect(agreement.content).toContain('Gigwork Pool: 40% of total revenue (separate from core team pool)');
      expect(agreement.content).not.toContain('UNIFIED POOL BENEFITS');
    });
  });

  // ============================================================================
  // INTEGRATION TESTS
  // ============================================================================

  describe('End-to-End Unified Pool Workflow', () => {
    test('Should complete full workflow with unified pool', () => {
      // 1. Create venture with unified pool (default)
      const venture = dynamicContributorManagement.initializeScalableVenture({
        name: 'E2E Test Venture',
        description: 'End-to-end testing venture',
        coreTeam: [
          {
            email: '<EMAIL>',
            role: 'Founder',
            responsibilities: ['Leadership', 'Strategy']
          }
        ],
        autoStartFirstTranche: true
      });

      expect(venture.revenueModel.calculationMethod).toBe(REVENUE_CALCULATION_METHODS.CONTRIBUTION_POINTS);

      // 2. Add gigwork contributors
      const dev1 = dynamicContributorManagement.addGigworkContributor(venture.id, {
        email: '<EMAIL>',
        role: 'Senior Developer',
        experienceLevel: 'senior'
      });

      const dev2 = dynamicContributorManagement.addGigworkContributor(venture.id, {
        email: '<EMAIL>',
        role: 'Junior Developer',
        experienceLevel: 'junior'
      });

      expect(venture.contributorPools.gigwork).toHaveLength(2);

      // 3. Record contributions
      const tranche = Array.from(dynamicContributorManagement.tranches.values())[0];
      const founder = venture.contributorPools.coreTeam[0];

      dynamicContributorManagement.recordContribution(tranche.id, founder.id, {
        type: 'code',
        hoursWorked: 50,
        difficultyLevel: 'expert',
        qualityRating: 5
      });

      dynamicContributorManagement.recordContribution(tranche.id, dev1.id, {
        type: 'code',
        hoursWorked: 40,
        difficultyLevel: 'hard',
        qualityRating: 4
      });

      dynamicContributorManagement.recordContribution(tranche.id, dev2.id, {
        type: 'code',
        hoursWorked: 30,
        difficultyLevel: 'easy',
        qualityRating: 3
      });

      // 4. Calculate revenue distribution
      const distribution = dynamicContributorManagement.calculateTrancheRevenueDistribution(
        tranche.id,
        150000
      );

      // 5. Verify unified pool distribution
      expect(distribution.totalRevenue).toBe(150000);
      expect(distribution.platformFee).toBe(15000);
      expect(distribution.gigworkPool).toBe(135000);
      expect(Object.keys(distribution.gigworkDistribution)).toHaveLength(3); // All contributors

      // 6. Generate agreements
      const dev1Agreement = dynamicContributorManagement.generateGigworkAgreement(venture.id, dev1.id);
      const dev2Agreement = dynamicContributorManagement.generateGigworkAgreement(venture.id, dev2.id);

      expect(dev1Agreement.terms.revenueSharing.isUnifiedPool).toBe(true);
      expect(dev2Agreement.terms.revenueSharing.isUnifiedPool).toBe(true);

      // 7. Verify founder earns based on contribution (not fixed share)
      const founderRevenue = distribution.gigworkDistribution[founder.id];
      const dev1Revenue = distribution.gigworkDistribution[dev1.id];
      
      expect(founderRevenue).toBeGreaterThan(dev1Revenue); // Founder contributed more
      expect(founderRevenue).toBeGreaterThan(0); // Founder earns based on contribution
    });
  });
});
