// Secure API Error Middleware
// Authentication & Security Agent: Secure error handling for API endpoints
// Created: January 16, 2025

/**
 * Secure API Error Middleware
 * 
 * Provides secure error handling for Netlify functions that prevents
 * information disclosure while maintaining useful error responses
 */

// Error categories and safe messages
const ERROR_CATEGORIES = {
  AUTHENTICATION: 'authentication',
  AUTHORIZATION: 'authorization',
  VALIDATION: 'validation',
  NOT_FOUND: 'not_found',
  RATE_LIMIT: 'rate_limit',
  SERVER_ERROR: 'server_error',
  DATABASE_ERROR: 'database_error',
  SECURITY: 'security'
};

const SAFE_ERROR_RESPONSES = {
  [ERROR_CATEGORIES.AUTHENTICATION]: {
    statusCode: 401,
    message: 'Authentication required. Please provide valid credentials.',
    code: 'AUTHENTICATION_REQUIRED'
  },
  [ERROR_CATEGORIES.AUTHORIZATION]: {
    statusCode: 403,
    message: 'Access denied. You do not have permission to perform this action.',
    code: 'ACCESS_DENIED'
  },
  [ERROR_CATEGORIES.VALIDATION]: {
    statusCode: 400,
    message: 'Invalid request data. Please check your input and try again.',
    code: 'VALIDATION_ERROR'
  },
  [ERROR_CATEGORIES.NOT_FOUND]: {
    statusCode: 404,
    message: 'The requested resource was not found.',
    code: 'RESOURCE_NOT_FOUND'
  },
  [ERROR_CATEGORIES.RATE_LIMIT]: {
    statusCode: 429,
    message: 'Too many requests. Please wait before trying again.',
    code: 'RATE_LIMIT_EXCEEDED'
  },
  [ERROR_CATEGORIES.SERVER_ERROR]: {
    statusCode: 500,
    message: 'An internal server error occurred. Please try again later.',
    code: 'INTERNAL_SERVER_ERROR'
  },
  [ERROR_CATEGORIES.DATABASE_ERROR]: {
    statusCode: 503,
    message: 'Database service is temporarily unavailable. Please try again later.',
    code: 'DATABASE_UNAVAILABLE'
  },
  [ERROR_CATEGORIES.SECURITY]: {
    statusCode: 403,
    message: 'Security violation detected. This incident has been logged.',
    code: 'SECURITY_VIOLATION'
  }
};

// Sensitive information patterns to redact from logs
const SENSITIVE_PATTERNS = [
  /password/gi,
  /token/gi,
  /secret/gi,
  /key/gi,
  /authorization/gi,
  /bearer/gi,
  /cookie/gi,
  /session/gi,
  /credential/gi,
  /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g, // Email addresses
  /\b\d{4}[- ]?\d{4}[- ]?\d{4}[- ]?\d{4}\b/g, // Credit card numbers
  /\b\d{3}-\d{2}-\d{4}\b/g // SSN format
];

/**
 * Categorize error based on status code and message
 */
function categorizeError(error) {
  const status = error.statusCode || error.status || 500;
  const message = (error.message || '').toLowerCase();

  if (status === 401 || message.includes('unauthorized') || message.includes('authentication')) {
    return ERROR_CATEGORIES.AUTHENTICATION;
  }
  
  if (status === 403 || message.includes('forbidden') || message.includes('permission')) {
    return ERROR_CATEGORIES.AUTHORIZATION;
  }
  
  if (status === 400 || message.includes('validation') || message.includes('invalid')) {
    return ERROR_CATEGORIES.VALIDATION;
  }
  
  if (status === 404 || message.includes('not found')) {
    return ERROR_CATEGORIES.NOT_FOUND;
  }
  
  if (status === 429 || message.includes('rate limit') || message.includes('too many')) {
    return ERROR_CATEGORIES.RATE_LIMIT;
  }
  
  if (message.includes('database') || message.includes('connection') || message.includes('timeout')) {
    return ERROR_CATEGORIES.DATABASE_ERROR;
  }
  
  if (message.includes('security') || message.includes('attack') || message.includes('malicious')) {
    return ERROR_CATEGORIES.SECURITY;
  }
  
  return ERROR_CATEGORIES.SERVER_ERROR;
}

/**
 * Redact sensitive information from text
 */
function redactSensitiveInfo(text) {
  if (!text || typeof text !== 'string') {
    return text;
  }

  let redactedText = text;
  
  SENSITIVE_PATTERNS.forEach(pattern => {
    redactedText = redactedText.replace(pattern, '[REDACTED]');
  });
  
  // Redact file paths
  redactedText = redactedText.replace(/\/[^\s]+/g, '[PATH]');
  
  // Redact URLs
  redactedText = redactedText.replace(/https?:\/\/[^\s]+/g, '[URL]');
  
  return redactedText;
}

/**
 * Generate secure error response
 */
function generateSecureErrorResponse(error, context = {}) {
  const category = categorizeError(error);
  const safeResponse = SAFE_ERROR_RESPONSES[category];
  const isDevelopment = process.env.NODE_ENV === 'development';
  
  const errorResponse = {
    success: false,
    error: {
      message: safeResponse.message,
      code: safeResponse.code,
      category: category,
      timestamp: new Date().toISOString(),
      requestId: generateRequestId()
    }
  };

  // Add validation details for validation errors
  if (category === ERROR_CATEGORIES.VALIDATION && error.validationErrors) {
    errorResponse.error.details = error.validationErrors;
  }

  // Add retry information for rate limiting
  if (category === ERROR_CATEGORIES.RATE_LIMIT) {
    errorResponse.error.retryAfter = error.retryAfter || 60;
  }

  // Include additional details in development
  if (isDevelopment) {
    errorResponse.debug = {
      originalMessage: redactSensitiveInfo(error.message),
      stack: error.stack,
      context: redactSensitiveInfo(JSON.stringify(context))
    };
  }

  return {
    statusCode: safeResponse.statusCode,
    headers: {
      'Content-Type': 'application/json',
      'X-Request-ID': errorResponse.error.requestId
    },
    body: JSON.stringify(errorResponse)
  };
}

/**
 * Log error securely
 */
function logErrorSecurely(error, context = {}) {
  const category = categorizeError(error);
  const isDevelopment = process.env.NODE_ENV === 'development';
  
  const logData = {
    timestamp: new Date().toISOString(),
    category: category,
    message: redactSensitiveInfo(error.message || 'Unknown error'),
    statusCode: error.statusCode || error.status || 500,
    method: context.httpMethod,
    path: context.path,
    userAgent: context.headers?.['user-agent'],
    ip: context.headers?.['x-forwarded-for'] || context.headers?.['x-real-ip'],
    requestId: context.requestId
  };

  // Include stack trace in development
  if (isDevelopment && error.stack) {
    logData.stack = error.stack;
  }

  // Log to console (in production, this would go to a logging service)
  console.error('API Error:', JSON.stringify(logData, null, 2));

  // Log security events for security-related errors
  if (category === ERROR_CATEGORIES.SECURITY) {
    console.warn('SECURITY EVENT:', JSON.stringify({
      ...logData,
      severity: 'high',
      securityEvent: true
    }, null, 2));
  }

  return logData;
}

/**
 * Generate unique request ID
 */
function generateRequestId() {
  return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Secure error handling middleware
 */
function secureErrorMiddleware(handler) {
  return async (event, context) => {
    const requestId = generateRequestId();
    const requestContext = {
      ...context,
      requestId,
      httpMethod: event.httpMethod,
      path: event.path,
      headers: event.headers,
      timestamp: new Date().toISOString()
    };

    try {
      // Add request ID to context for tracking
      context.requestId = requestId;
      
      // Call the original handler
      const response = await handler(event, requestContext);
      
      // Add request ID to successful responses
      if (response && response.headers) {
        response.headers['X-Request-ID'] = requestId;
      }
      
      return response;
    } catch (error) {
      // Log error securely
      logErrorSecurely(error, requestContext);
      
      // Generate secure error response
      return generateSecureErrorResponse(error, requestContext);
    }
  };
}

/**
 * Create validation error
 */
function createValidationError(message, validationErrors = []) {
  const error = new Error(message);
  error.statusCode = 400;
  error.validationErrors = validationErrors;
  return error;
}

/**
 * Create authentication error
 */
function createAuthenticationError(message = 'Authentication required') {
  const error = new Error(message);
  error.statusCode = 401;
  return error;
}

/**
 * Create authorization error
 */
function createAuthorizationError(message = 'Access denied') {
  const error = new Error(message);
  error.statusCode = 403;
  return error;
}

/**
 * Create not found error
 */
function createNotFoundError(message = 'Resource not found') {
  const error = new Error(message);
  error.statusCode = 404;
  return error;
}

/**
 * Create rate limit error
 */
function createRateLimitError(message = 'Rate limit exceeded', retryAfter = 60) {
  const error = new Error(message);
  error.statusCode = 429;
  error.retryAfter = retryAfter;
  return error;
}

/**
 * Create security error
 */
function createSecurityError(message = 'Security violation detected') {
  const error = new Error(message);
  error.statusCode = 403;
  return error;
}

/**
 * Validate request data
 */
function validateRequest(data, schema) {
  const errors = [];
  
  for (const [field, rules] of Object.entries(schema)) {
    const value = data[field];
    
    if (rules.required && (value === undefined || value === null || value === '')) {
      errors.push({
        field,
        message: `${field} is required`,
        code: 'REQUIRED_FIELD'
      });
      continue;
    }
    
    if (value !== undefined && value !== null) {
      if (rules.type && typeof value !== rules.type) {
        errors.push({
          field,
          message: `${field} must be of type ${rules.type}`,
          code: 'INVALID_TYPE'
        });
      }
      
      if (rules.minLength && value.length < rules.minLength) {
        errors.push({
          field,
          message: `${field} must be at least ${rules.minLength} characters`,
          code: 'MIN_LENGTH'
        });
      }
      
      if (rules.maxLength && value.length > rules.maxLength) {
        errors.push({
          field,
          message: `${field} must be no more than ${rules.maxLength} characters`,
          code: 'MAX_LENGTH'
        });
      }
      
      if (rules.pattern && !rules.pattern.test(value)) {
        errors.push({
          field,
          message: `${field} format is invalid`,
          code: 'INVALID_FORMAT'
        });
      }
    }
  }
  
  if (errors.length > 0) {
    throw createValidationError('Validation failed', errors);
  }
  
  return true;
}

module.exports = {
  secureErrorMiddleware,
  createValidationError,
  createAuthenticationError,
  createAuthorizationError,
  createNotFoundError,
  createRateLimitError,
  createSecurityError,
  validateRequest,
  ERROR_CATEGORIES,
  generateRequestId,
  redactSensitiveInfo
};
