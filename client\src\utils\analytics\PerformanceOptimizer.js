/**
 * Performance Optimizer for Analytics Data Service
 * 
 * Provides caching, request batching, and performance optimization
 * for analytics data fetching and processing.
 */

class PerformanceOptimizer {
  constructor() {
    this.cache = new Map();
    this.batchRequests = new Map();
    this.cacheTimeout = 5 * 60 * 1000; // 5 minutes
    this.maxCacheSize = 100;
    this.requestQueue = new Map();
  }

  /**
   * Get cached data if available and not expired
   */
  getCache(key) {
    if (this.cache.has(key)) {
      const cached = this.cache.get(key);
      if (Date.now() - cached.timestamp < this.cacheTimeout) {
        return cached.data;
      } else {
        this.cache.delete(key);
      }
    }
    return null;
  }

  /**
   * Set cache with automatic cleanup
   */
  setCache(key, data) {
    // Clean up old cache entries if we're at max size
    if (this.cache.size >= this.maxCacheSize) {
      const oldestKey = this.cache.keys().next().value;
      this.cache.delete(oldestKey);
    }

    this.cache.set(key, {
      data,
      timestamp: Date.now()
    });
  }

  /**
   * Batch similar requests to avoid duplicate API calls
   */
  async batchRequest(key, requestFn) {
    // If request is already in progress, wait for it
    if (this.batchRequests.has(key)) {
      return await this.batchRequests.get(key);
    }

    // Start new request and cache the promise
    const requestPromise = requestFn();
    this.batchRequests.set(key, requestPromise);

    try {
      const result = await requestPromise;
      return result;
    } finally {
      // Clean up the batch request
      this.batchRequests.delete(key);
    }
  }

  /**
   * Debounce function calls to reduce API load
   */
  debounce(func, delay = 300) {
    let timeoutId;
    return (...args) => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => func.apply(this, args), delay);
    };
  }

  /**
   * Throttle function calls to limit frequency
   */
  throttle(func, limit = 1000) {
    let inThrottle;
    return (...args) => {
      if (!inThrottle) {
        func.apply(this, args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, limit);
      }
    };
  }

  /**
   * Queue requests to prevent overwhelming the API
   */
  async queueRequest(key, requestFn, priority = 'normal') {
    return new Promise((resolve, reject) => {
      const request = {
        key,
        requestFn,
        priority,
        resolve,
        reject,
        timestamp: Date.now()
      };

      if (!this.requestQueue.has(priority)) {
        this.requestQueue.set(priority, []);
      }

      this.requestQueue.get(priority).push(request);
      this.processQueue();
    });
  }

  /**
   * Process queued requests with priority handling
   */
  async processQueue() {
    const priorities = ['high', 'normal', 'low'];
    
    for (const priority of priorities) {
      const queue = this.requestQueue.get(priority);
      if (queue && queue.length > 0) {
        const request = queue.shift();
        
        try {
          const result = await request.requestFn();
          request.resolve(result);
        } catch (error) {
          request.reject(error);
        }
        
        // Small delay between requests to prevent overwhelming
        await new Promise(resolve => setTimeout(resolve, 50));
        break;
      }
    }

    // Continue processing if there are more requests
    const hasMoreRequests = priorities.some(priority => {
      const queue = this.requestQueue.get(priority);
      return queue && queue.length > 0;
    });

    if (hasMoreRequests) {
      setTimeout(() => this.processQueue(), 100);
    }
  }

  /**
   * Preload data for better user experience
   */
  async preloadData(keys, requestFns) {
    const preloadPromises = keys.map((key, index) => {
      if (!this.getCache(key)) {
        return this.queueRequest(key, requestFns[index], 'low');
      }
      return Promise.resolve();
    });

    await Promise.allSettled(preloadPromises);
  }

  /**
   * Clear expired cache entries
   */
  cleanupCache() {
    const now = Date.now();
    for (const [key, cached] of this.cache.entries()) {
      if (now - cached.timestamp > this.cacheTimeout) {
        this.cache.delete(key);
      }
    }
  }

  /**
   * Get cache statistics
   */
  getCacheStats() {
    return {
      size: this.cache.size,
      maxSize: this.maxCacheSize,
      hitRate: this.cacheHits / (this.cacheHits + this.cacheMisses) || 0,
      queueSize: Array.from(this.requestQueue.values()).reduce((total, queue) => total + queue.length, 0)
    };
  }

  /**
   * Clear all cache and queues
   */
  clear() {
    this.cache.clear();
    this.batchRequests.clear();
    this.requestQueue.clear();
  }
}

// Export singleton instance
export default new PerformanceOptimizer();
