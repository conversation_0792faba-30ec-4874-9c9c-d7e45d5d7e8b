// Group Communication API
// Backend Specialist: Alliance and project group messaging system
const { createClient } = require('@supabase/supabase-js');

// Supabase client
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_KEY
);

// Helper function to get user from request
const getUserFromRequest = (event) => {
  const authHeader = event.headers.authorization;
  if (!authHeader) return null;
  
  try {
    const token = authHeader.replace('Bearer ', '');
    const payload = JSON.parse(Buffer.from(token.split('.')[1], 'base64').toString());
    return payload.sub;
  } catch {
    return null;
  }
};

// Create Group Conversation
const createGroupConversation = async (event) => {
  try {
    const userId = getUserFromRequest(event);
    if (!userId) {
      return {
        statusCode: 401,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    const data = JSON.parse(event.body);
    
    if (!data.title || !data.conversation_type) {
      return {
        statusCode: 400,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          error: 'title and conversation_type are required' 
        })
      };
    }

    // Validate conversation type
    if (!['group', 'alliance', 'project'].includes(data.conversation_type)) {
      return {
        statusCode: 400,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          error: 'conversation_type must be group, alliance, or project' 
        })
      };
    }

    // Verify permissions based on conversation type
    if (data.conversation_type === 'alliance' && data.alliance_id) {
      const { data: membership, error: membershipError } = await supabase
        .from('team_members')
        .select('role')
        .eq('team_id', data.alliance_id)
        .eq('user_id', userId)
        .single();

      if (membershipError || !['founder', 'owner', 'admin'].includes(membership.role)) {
        return {
          statusCode: 403,
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ error: 'Insufficient permissions to create alliance conversation' })
        };
      }
    }

    if (data.conversation_type === 'project' && data.project_id) {
      const { data: project, error: projectError } = await supabase
        .from('projects')
        .select('created_by, team_id')
        .eq('id', data.project_id)
        .single();

      if (projectError) {
        return {
          statusCode: 404,
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ error: 'Project not found' })
        };
      }

      // Check if user is project creator or alliance admin
      const isProjectCreator = project.created_by === userId;
      let isAllianceAdmin = false;

      if (project.team_id) {
        const { data: membership } = await supabase
          .from('team_members')
          .select('role')
          .eq('team_id', project.team_id)
          .eq('user_id', userId)
          .single();

        isAllianceAdmin = membership && ['founder', 'owner', 'admin'].includes(membership.role);
      }

      if (!isProjectCreator && !isAllianceAdmin) {
        return {
          statusCode: 403,
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ error: 'Insufficient permissions to create project conversation' })
        };
      }
    }

    // Create conversation
    const conversationData = {
      conversation_type: data.conversation_type,
      title: data.title,
      description: data.description || null,
      alliance_id: data.alliance_id || null,
      project_id: data.project_id || null,
      created_by: userId
    };

    const { data: conversation, error: conversationError } = await supabase
      .from('conversations')
      .insert([conversationData])
      .select()
      .single();

    if (conversationError) {
      throw new Error(`Failed to create conversation: ${conversationError.message}`);
    }

    // Add creator as admin
    await supabase
      .from('conversation_participants')
      .insert([{
        conversation_id: conversation.id,
        user_id: userId,
        role: 'admin',
        can_add_members: true,
        can_remove_members: true,
        can_edit_conversation: true
      }]);

    // Auto-add members based on conversation type
    if (data.conversation_type === 'alliance' && data.alliance_id) {
      await addAllianceMembers(conversation.id, data.alliance_id);
    } else if (data.conversation_type === 'project' && data.project_id) {
      await addProjectMembers(conversation.id, data.project_id);
    }

    // Add specified participants for group conversations
    if (data.conversation_type === 'group' && data.participant_ids) {
      await addGroupParticipants(conversation.id, data.participant_ids, userId);
    }

    return {
      statusCode: 201,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        conversation: {
          id: conversation.id,
          type: conversation.conversation_type,
          title: conversation.title,
          description: conversation.description,
          alliance_id: conversation.alliance_id,
          project_id: conversation.project_id,
          created_at: conversation.created_at
        }
      })
    };

  } catch (error) {
    console.error('Create group conversation error:', error);
    return {
      statusCode: 500,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ error: 'Failed to create group conversation' })
    };
  }
};

// Add Alliance Members to Conversation
const addAllianceMembers = async (conversationId, allianceId) => {
  try {
    const { data: members } = await supabase
      .from('team_members')
      .select('user_id, role')
      .eq('team_id', allianceId);

    if (members && members.length > 0) {
      const participants = members.map(member => ({
        conversation_id: conversationId,
        user_id: member.user_id,
        role: ['founder', 'owner', 'admin'].includes(member.role) ? 'moderator' : 'member',
        can_add_members: ['founder', 'owner', 'admin'].includes(member.role)
      }));

      await supabase
        .from('conversation_participants')
        .upsert(participants, { onConflict: 'conversation_id,user_id' });
    }
  } catch (error) {
    console.error('Add alliance members error:', error);
  }
};

// Add Project Members to Conversation
const addProjectMembers = async (conversationId, projectId) => {
  try {
    const { data: contributors } = await supabase
      .from('project_contributors')
      .select('user_id, role')
      .eq('project_id', projectId);

    if (contributors && contributors.length > 0) {
      const participants = contributors.map(contributor => ({
        conversation_id: conversationId,
        user_id: contributor.user_id,
        role: contributor.role === 'lead' ? 'moderator' : 'member',
        can_add_members: contributor.role === 'lead'
      }));

      await supabase
        .from('conversation_participants')
        .upsert(participants, { onConflict: 'conversation_id,user_id' });
    }
  } catch (error) {
    console.error('Add project members error:', error);
  }
};

// Add Group Participants
const addGroupParticipants = async (conversationId, participantIds, creatorId) => {
  try {
    // Verify all participants are allies of the creator
    const { data: allies } = await supabase
      .from('user_allies')
      .select('ally_id, user_id')
      .or(`user_id.eq.${creatorId},ally_id.eq.${creatorId}`)
      .eq('status', 'accepted');

    const allyIds = new Set();
    allies?.forEach(ally => {
      allyIds.add(ally.user_id === creatorId ? ally.ally_id : ally.user_id);
    });

    // Filter participants to only include allies
    const validParticipants = participantIds.filter(id => allyIds.has(id));

    if (validParticipants.length > 0) {
      const participants = validParticipants.map(userId => ({
        conversation_id: conversationId,
        user_id: userId,
        role: 'member'
      }));

      await supabase
        .from('conversation_participants')
        .insert(participants);
    }
  } catch (error) {
    console.error('Add group participants error:', error);
  }
};

// Add Participant to Conversation
const addParticipant = async (event) => {
  try {
    const userId = getUserFromRequest(event);
    if (!userId) {
      return {
        statusCode: 401,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    const conversationId = event.path.split('/')[1];
    const data = JSON.parse(event.body);
    
    if (!data.user_id) {
      return {
        statusCode: 400,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'user_id is required' })
      };
    }

    // Verify user has permission to add members
    const { data: participant, error: participantError } = await supabase
      .from('conversation_participants')
      .select('role, can_add_members')
      .eq('conversation_id', conversationId)
      .eq('user_id', userId)
      .is('left_at', null)
      .single();

    if (participantError || !participant.can_add_members) {
      return {
        statusCode: 403,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Insufficient permissions to add members' })
      };
    }

    // Check if user is already a participant
    const { data: existingParticipant } = await supabase
      .from('conversation_participants')
      .select('id')
      .eq('conversation_id', conversationId)
      .eq('user_id', data.user_id)
      .is('left_at', null)
      .single();

    if (existingParticipant) {
      return {
        statusCode: 400,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'User is already a participant' })
      };
    }

    // Add participant
    const { data: newParticipant, error: addError } = await supabase
      .from('conversation_participants')
      .insert([{
        conversation_id: conversationId,
        user_id: data.user_id,
        role: data.role || 'member'
      }])
      .select(`
        id,
        role,
        joined_at,
        user:users(
          id,
          display_name,
          avatar_url
        )
      `)
      .single();

    if (addError) {
      throw new Error(`Failed to add participant: ${addError.message}`);
    }

    return {
      statusCode: 201,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        participant: newParticipant
      })
    };

  } catch (error) {
    console.error('Add participant error:', error);
    return {
      statusCode: 500,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ error: 'Failed to add participant' })
    };
  }
};

// Remove Participant from Conversation
const removeParticipant = async (event) => {
  try {
    const userId = getUserFromRequest(event);
    if (!userId) {
      return {
        statusCode: 401,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    const conversationId = event.path.split('/')[1];
    const participantUserId = event.path.split('/').pop();

    // Verify user has permission to remove members or is removing themselves
    const { data: participant, error: participantError } = await supabase
      .from('conversation_participants')
      .select('role, can_remove_members')
      .eq('conversation_id', conversationId)
      .eq('user_id', userId)
      .is('left_at', null)
      .single();

    const canRemove = participant?.can_remove_members || participantUserId === userId;

    if (participantError || !canRemove) {
      return {
        statusCode: 403,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Insufficient permissions to remove participant' })
      };
    }

    // Remove participant (mark as left)
    const { error: removeError } = await supabase
      .from('conversation_participants')
      .update({ left_at: new Date().toISOString() })
      .eq('conversation_id', conversationId)
      .eq('user_id', participantUserId);

    if (removeError) {
      throw new Error(`Failed to remove participant: ${removeError.message}`);
    }

    return {
      statusCode: 200,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        message: 'Participant removed successfully',
        removed_user_id: participantUserId
      })
    };

  } catch (error) {
    console.error('Remove participant error:', error);
    return {
      statusCode: 500,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ error: 'Failed to remove participant' })
    };
  }
};

// Update Conversation Settings
const updateConversation = async (event) => {
  try {
    const userId = getUserFromRequest(event);
    if (!userId) {
      return {
        statusCode: 401,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    const conversationId = event.path.split('/').pop();
    const data = JSON.parse(event.body);

    // Verify user has permission to edit conversation
    const { data: participant, error: participantError } = await supabase
      .from('conversation_participants')
      .select('role, can_edit_conversation')
      .eq('conversation_id', conversationId)
      .eq('user_id', userId)
      .is('left_at', null)
      .single();

    if (participantError || !participant.can_edit_conversation) {
      return {
        statusCode: 403,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Insufficient permissions to edit conversation' })
      };
    }

    // Update conversation
    const updateData = {};
    if (data.title) updateData.title = data.title;
    if (data.description !== undefined) updateData.description = data.description;
    updateData.updated_at = new Date().toISOString();

    const { data: updatedConversation, error: updateError } = await supabase
      .from('conversations')
      .update(updateData)
      .eq('id', conversationId)
      .select()
      .single();

    if (updateError) {
      throw new Error(`Failed to update conversation: ${updateError.message}`);
    }

    return {
      statusCode: 200,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        conversation: {
          id: updatedConversation.id,
          title: updatedConversation.title,
          description: updatedConversation.description,
          updated_at: updatedConversation.updated_at
        }
      })
    };

  } catch (error) {
    console.error('Update conversation error:', error);
    return {
      statusCode: 500,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ error: 'Failed to update conversation' })
    };
  }
};

// Get Conversation Participants
const getConversationParticipants = async (event) => {
  try {
    const userId = getUserFromRequest(event);
    if (!userId) {
      return {
        statusCode: 401,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    const conversationId = event.path.split('/')[1];

    // Verify user has access to conversation
    const { data: userParticipant, error: accessError } = await supabase
      .from('conversation_participants')
      .select('id')
      .eq('conversation_id', conversationId)
      .eq('user_id', userId)
      .is('left_at', null)
      .single();

    if (accessError) {
      return {
        statusCode: 403,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Access denied to this conversation' })
      };
    }

    // Get all participants
    const { data: participants, error: participantsError } = await supabase
      .from('conversation_participants')
      .select(`
        id,
        role,
        joined_at,
        is_muted,
        can_add_members,
        can_remove_members,
        can_edit_conversation,
        user:users(
          id,
          display_name,
          avatar_url,
          is_premium
        )
      `)
      .eq('conversation_id', conversationId)
      .is('left_at', null)
      .order('joined_at', { ascending: true });

    if (participantsError) {
      throw new Error(`Failed to fetch participants: ${participantsError.message}`);
    }

    return {
      statusCode: 200,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        participants: participants,
        total: participants.length
      })
    };

  } catch (error) {
    console.error('Get conversation participants error:', error);
    return {
      statusCode: 500,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ error: 'Failed to fetch participants' })
    };
  }
};

// Route requests to appropriate handlers
exports.handler = async (event, context) => {
  // Add CORS headers
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS'
  };

  // Handle preflight requests
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers,
      body: ''
    };
  }

  const path = event.path.replace('/.netlify/functions/group-communication', '');

  try {
    let response;

    if (event.httpMethod === 'POST') {
      if (path === '' || path === '/') {
        response = await createGroupConversation(event);
      } else if (path.includes('/participants')) {
        response = await addParticipant(event);
      } else {
        response = {
          statusCode: 404,
          body: JSON.stringify({ error: 'Endpoint not found' })
        };
      }
    } else if (event.httpMethod === 'GET') {
      if (path.includes('/participants')) {
        response = await getConversationParticipants(event);
      } else {
        response = {
          statusCode: 404,
          body: JSON.stringify({ error: 'Endpoint not found' })
        };
      }
    } else if (event.httpMethod === 'PUT') {
      response = await updateConversation(event);
    } else if (event.httpMethod === 'DELETE') {
      if (path.includes('/participants/')) {
        response = await removeParticipant(event);
      } else {
        response = {
          statusCode: 404,
          body: JSON.stringify({ error: 'Endpoint not found' })
        };
      }
    } else {
      response = {
        statusCode: 405,
        body: JSON.stringify({ error: 'Method not allowed' })
      };
    }

    // Add CORS headers to response
    response.headers = { ...response.headers, ...headers };
    return response;

  } catch (error) {
    console.error('Group Communication API error:', error);
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ error: 'Internal server error' })
    };
  }
};
