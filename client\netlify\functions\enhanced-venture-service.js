// Enhanced Venture Service API
// Integration & Services Agent: Comprehensive venture/project management system

const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_ANON_KEY
);

// CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS'
};

// Authenticate user from JWT token
const authenticateUser = async (authHeader) => {
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    throw new Error('Missing or invalid authorization header');
  }

  const token = authHeader.substring(7);
  
  try {
    const { data: { user }, error } = await supabase.auth.getUser(token);
    
    if (error || !user) {
      throw new Error('Invalid authentication token');
    }
    
    return user;
  } catch (error) {
    console.error('Authentication error:', error);
    throw new Error('Authentication failed');
  }
};

// Check venture access and permissions
const checkVentureAccess = async (userId, ventureId, requiredPermission = null) => {
  try {
    const { data: member, error } = await supabase
      .from('venture_members')
      .select('role, permissions, status')
      .eq('venture_id', ventureId)
      .eq('user_id', userId)
      .eq('status', 'active')
      .single();

    if (error || !member) {
      throw new Error('Access denied: Not a member of this venture');
    }

    if (requiredPermission && !member.permissions[requiredPermission]) {
      throw new Error(`Access denied: ${requiredPermission} permission required`);
    }

    return member;
  } catch (error) {
    console.error('Check venture access error:', error);
    throw error;
  }
};

// Log venture activity
const logActivity = async (ventureId, userId, activityType, title, description = null, metadata = {}) => {
  try {
    await supabase.rpc('log_venture_activity', {
      venture_id_param: ventureId,
      user_id_param: userId,
      activity_type_param: activityType,
      title_param: title,
      description_param: description,
      metadata_param: metadata
    });
  } catch (error) {
    console.error('Failed to log activity:', error);
  }
};

// Get venture templates
const getVentureTemplates = async (user, queryParams) => {
  try {
    let query = supabase
      .from('venture_templates')
      .select('*')
      .eq('is_active', true)
      .order('is_featured', { ascending: false })
      .order('usage_count', { ascending: false });

    const templateType = queryParams.get('type');
    if (templateType) {
      query = query.eq('template_type', templateType);
    }

    const industry = queryParams.get('industry');
    if (industry) {
      query = query.eq('industry', industry);
    }

    const complexity = queryParams.get('complexity');
    if (complexity) {
      query = query.eq('complexity_level', complexity);
    }

    const { data: templates, error } = await query;

    if (error) {
      throw new Error(`Failed to fetch templates: ${error.message}`);
    }

    return templates || [];

  } catch (error) {
    console.error('Get venture templates error:', error);
    throw error;
  }
};

// Create venture from template
const createVentureFromTemplate = async (user, ventureData) => {
  try {
    const {
      template_id,
      name,
      description,
      alliance_id,
      start_date,
      custom_settings = {}
    } = ventureData;

    if (!template_id || !name) {
      throw new Error('template_id and name are required');
    }

    // Get template details
    const { data: template, error: templateError } = await supabase
      .from('venture_templates')
      .select('*')
      .eq('id', template_id)
      .eq('is_active', true)
      .single();

    if (templateError || !template) {
      throw new Error('Template not found or inactive');
    }

    // Create the venture (project)
    const { data: venture, error: ventureError } = await supabase
      .from('projects')
      .insert({
        name,
        title: name,
        description: description || template.description,
        project_type: template.template_type,
        team_id: alliance_id,
        created_by: user.id,
        is_active: true,
        estimated_duration: template.estimated_duration_weeks * 7, // Convert to days
        start_date: start_date || new Date().toISOString().split('T')[0],
        wizard_progress: { 
          current_step: 1, 
          completed_steps: [],
          template_id: template_id,
          template_applied: true
        }
      })
      .select()
      .single();

    if (ventureError) {
      throw new Error(`Failed to create venture: ${ventureError.message}`);
    }

    // Add creator as venture lead
    await supabase
      .from('venture_members')
      .insert({
        venture_id: venture.id,
        user_id: user.id,
        role: 'lead',
        permissions: {
          can_edit_venture: true,
          can_manage_team: true,
          can_create_milestones: true,
          can_approve_milestones: true,
          can_manage_budget: true,
          can_view_analytics: true,
          can_invite_members: true
        },
        revenue_share: custom_settings.lead_revenue_share || 30.00,
        status: 'active'
      });

    // Create milestones from template
    if (template.milestone_templates && template.milestone_templates.length > 0) {
      const milestones = template.milestone_templates.map((milestone, index) => ({
        venture_id: venture.id,
        title: milestone.title,
        description: milestone.description || '',
        milestone_type: milestone.type || 'deliverable',
        estimated_hours: milestone.estimated_hours || 0,
        created_by: user.id,
        due_date: start_date ? 
          new Date(new Date(start_date).getTime() + (index + 1) * 14 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] :
          null
      }));

      await supabase
        .from('venture_milestones')
        .insert(milestones);
    }

    // Update template usage count
    await supabase
      .from('venture_templates')
      .update({ usage_count: template.usage_count + 1 })
      .eq('id', template_id);

    // Log activity
    await logActivity(
      venture.id,
      user.id,
      'venture_created',
      `Venture created from ${template.name} template`,
      `New venture "${name}" created using the ${template.name} template`,
      { template_id, template_name: template.name }
    );

    return {
      success: true,
      venture_id: venture.id,
      venture: venture,
      template_applied: template.name,
      message: 'Venture created successfully from template'
    };

  } catch (error) {
    console.error('Create venture from template error:', error);
    throw error;
  }
};

// Get venture details with enhanced information
const getVentureDetails = async (user, ventureId) => {
  try {
    // Check access
    await checkVentureAccess(user.id, ventureId);

    // Get venture basic info
    const { data: venture, error: ventureError } = await supabase
      .from('projects')
      .select(`
        *,
        teams (
          id,
          name,
          alliance_type
        )
      `)
      .eq('id', ventureId)
      .single();

    if (ventureError) {
      throw new Error(`Failed to fetch venture: ${ventureError.message}`);
    }

    // Get venture members
    const { data: members, error: membersError } = await supabase
      .from('venture_members')
      .select(`
        *,
        users (
          id,
          display_name,
          email,
          avatar_url
        )
      `)
      .eq('venture_id', ventureId)
      .order('role', { ascending: true });

    if (membersError) {
      console.error('Failed to fetch members:', membersError);
    }

    // Get milestones
    const { data: milestones, error: milestonesError } = await supabase
      .from('venture_milestones')
      .select(`
        *,
        assigned_user:assigned_to (
          id,
          display_name,
          avatar_url
        ),
        created_user:created_by (
          id,
          display_name
        )
      `)
      .eq('venture_id', ventureId)
      .order('due_date', { ascending: true });

    if (milestonesError) {
      console.error('Failed to fetch milestones:', milestonesError);
    }

    // Get recent activities
    const { data: activities, error: activitiesError } = await supabase
      .from('venture_activities')
      .select(`
        *,
        user:user_id (
          id,
          display_name,
          avatar_url
        )
      `)
      .eq('venture_id', ventureId)
      .order('created_at', { ascending: false })
      .limit(20);

    if (activitiesError) {
      console.error('Failed to fetch activities:', activitiesError);
    }

    // Get budget summary
    const { data: budgetItems, error: budgetError } = await supabase
      .from('venture_budget_items')
      .select('*')
      .eq('venture_id', ventureId);

    if (budgetError) {
      console.error('Failed to fetch budget:', budgetError);
    }

    // Calculate progress
    const { data: progressData, error: progressError } = await supabase
      .rpc('calculate_venture_progress', { venture_id_param: ventureId });

    if (progressError) {
      console.error('Failed to calculate progress:', progressError);
    }

    // Calculate budget summary
    const budgetSummary = budgetItems ? budgetItems.reduce((acc, item) => ({
      planned_total: acc.planned_total + (item.planned_amount || 0),
      actual_total: acc.actual_total + (item.actual_amount || 0),
      remaining: acc.remaining + ((item.planned_amount || 0) - (item.actual_amount || 0))
    }), { planned_total: 0, actual_total: 0, remaining: 0 }) : { planned_total: 0, actual_total: 0, remaining: 0 };

    return {
      venture,
      members: members || [],
      milestones: milestones || [],
      activities: activities || [],
      budget_items: budgetItems || [],
      budget_summary: budgetSummary,
      progress_percentage: progressData || 0,
      user_role: members?.find(m => m.user_id === user.id)?.role || 'observer'
    };

  } catch (error) {
    console.error('Get venture details error:', error);
    throw error;
  }
};

// Manage venture milestones
const manageMilestone = async (user, action, milestoneData) => {
  try {
    switch (action) {
      case 'create':
        const {
          venture_id,
          title,
          description,
          milestone_type = 'deliverable',
          priority = 'medium',
          due_date,
          estimated_hours,
          budget_allocation,
          assigned_to,
          revenue_trigger = false,
          revenue_amount,
          dependencies = [],
          acceptance_criteria = []
        } = milestoneData;

        if (!venture_id || !title) {
          throw new Error('venture_id and title are required');
        }

        // Check permissions
        await checkVentureAccess(user.id, venture_id, 'can_create_milestones');

        const { data: milestone, error: createError } = await supabase
          .from('venture_milestones')
          .insert({
            venture_id,
            title,
            description,
            milestone_type,
            priority,
            due_date,
            estimated_hours,
            budget_allocation,
            assigned_to,
            created_by: user.id,
            revenue_trigger,
            revenue_amount,
            dependencies,
            acceptance_criteria
          })
          .select()
          .single();

        if (createError) {
          throw new Error(`Failed to create milestone: ${createError.message}`);
        }

        // Log activity
        await logActivity(
          venture_id,
          user.id,
          'milestone_created',
          `Milestone created: ${title}`,
          `New ${milestone_type} milestone "${title}" created`,
          { milestone_id: milestone.id, milestone_type, priority }
        );

        return { success: true, milestone };

      case 'update':
        const { milestone_id, ...updateData } = milestoneData;

        if (!milestone_id) {
          throw new Error('milestone_id is required');
        }

        // Get milestone to check venture access
        const { data: existingMilestone, error: fetchError } = await supabase
          .from('venture_milestones')
          .select('venture_id, title')
          .eq('id', milestone_id)
          .single();

        if (fetchError || !existingMilestone) {
          throw new Error('Milestone not found');
        }

        // Check permissions
        await checkVentureAccess(user.id, existingMilestone.venture_id, 'can_create_milestones');

        const { data: updatedMilestone, error: updateError } = await supabase
          .from('venture_milestones')
          .update(updateData)
          .eq('id', milestone_id)
          .select()
          .single();

        if (updateError) {
          throw new Error(`Failed to update milestone: ${updateError.message}`);
        }

        // Log activity
        await logActivity(
          existingMilestone.venture_id,
          user.id,
          'milestone_updated',
          `Milestone updated: ${existingMilestone.title}`,
          `Milestone "${existingMilestone.title}" was updated`,
          { milestone_id, changes: Object.keys(updateData) }
        );

        return { success: true, milestone: updatedMilestone };

      case 'complete':
        const { milestone_id: completeId, completion_evidence = [], review_notes } = milestoneData;

        if (!completeId) {
          throw new Error('milestone_id is required');
        }

        // Get milestone details
        const { data: milestoneToComplete, error: milestoneError } = await supabase
          .from('venture_milestones')
          .select('*')
          .eq('id', completeId)
          .single();

        if (milestoneError || !milestoneToComplete) {
          throw new Error('Milestone not found');
        }

        // Check access
        await checkVentureAccess(user.id, milestoneToComplete.venture_id);

        // Update milestone status
        const { data: completedMilestone, error: completeError } = await supabase
          .from('venture_milestones')
          .update({
            status: 'completed',
            completion_percentage: 100,
            completed_at: new Date().toISOString(),
            completion_evidence,
            review_notes
          })
          .eq('id', completeId)
          .select()
          .single();

        if (completeError) {
          throw new Error(`Failed to complete milestone: ${completeError.message}`);
        }

        // Log activity
        await logActivity(
          milestoneToComplete.venture_id,
          user.id,
          'milestone_completed',
          `Milestone completed: ${milestoneToComplete.title}`,
          `Milestone "${milestoneToComplete.title}" has been marked as completed`,
          { milestone_id: completeId, revenue_trigger: milestoneToComplete.revenue_trigger }
        );

        // Handle revenue trigger if applicable
        if (milestoneToComplete.revenue_trigger && milestoneToComplete.revenue_amount) {
          // This would trigger revenue distribution logic
          await logActivity(
            milestoneToComplete.venture_id,
            user.id,
            'revenue_triggered',
            `Revenue distribution triggered: $${milestoneToComplete.revenue_amount}`,
            `Milestone completion triggered revenue distribution of $${milestoneToComplete.revenue_amount}`,
            { milestone_id: completeId, amount: milestoneToComplete.revenue_amount }
          );
        }

        return { success: true, milestone: completedMilestone };

      default:
        throw new Error('Invalid action');
    }

  } catch (error) {
    console.error('Manage milestone error:', error);
    throw error;
  }
};

// Manage venture team members
const manageTeamMember = async (user, action, memberData) => {
  try {
    switch (action) {
      case 'invite':
        const {
          venture_id,
          email,
          role = 'member',
          revenue_share = 0,
          permissions = {}
        } = memberData;

        if (!venture_id || !email) {
          throw new Error('venture_id and email are required');
        }

        // Check permissions
        await checkVentureAccess(user.id, venture_id, 'can_manage_team');

        // Check if user exists
        const { data: inviteeUser, error: userError } = await supabase
          .from('users')
          .select('id, display_name')
          .eq('email', email)
          .single();

        if (userError || !inviteeUser) {
          throw new Error('User not found with this email');
        }

        // Check if already a member
        const { data: existingMember, error: memberError } = await supabase
          .from('venture_members')
          .select('id')
          .eq('venture_id', venture_id)
          .eq('user_id', inviteeUser.id)
          .single();

        if (existingMember) {
          throw new Error('User is already a member of this venture');
        }

        // Add member
        const { data: newMember, error: addError } = await supabase
          .from('venture_members')
          .insert({
            venture_id,
            user_id: inviteeUser.id,
            role,
            revenue_share,
            permissions: {
              can_edit_venture: false,
              can_manage_team: false,
              can_create_milestones: role === 'lead',
              can_approve_milestones: role === 'lead',
              can_manage_budget: role === 'lead',
              can_view_analytics: true,
              can_invite_members: role === 'lead',
              ...permissions
            },
            status: 'active'
          })
          .select()
          .single();

        if (addError) {
          throw new Error(`Failed to add team member: ${addError.message}`);
        }

        // Log activity
        await logActivity(
          venture_id,
          user.id,
          'member_added',
          `Team member added: ${inviteeUser.display_name}`,
          `${inviteeUser.display_name} was added to the venture as ${role}`,
          { member_id: newMember.id, role, revenue_share }
        );

        return { success: true, member: newMember };

      case 'update':
        const { member_id, ...updateData } = memberData;

        if (!member_id) {
          throw new Error('member_id is required');
        }

        // Get member to check venture access
        const { data: memberToUpdate, error: fetchError } = await supabase
          .from('venture_members')
          .select('venture_id, user_id')
          .eq('id', member_id)
          .single();

        if (fetchError || !memberToUpdate) {
          throw new Error('Team member not found');
        }

        // Check permissions
        await checkVentureAccess(user.id, memberToUpdate.venture_id, 'can_manage_team');

        const { data: updatedMember, error: updateError } = await supabase
          .from('venture_members')
          .update(updateData)
          .eq('id', member_id)
          .select()
          .single();

        if (updateError) {
          throw new Error(`Failed to update team member: ${updateError.message}`);
        }

        // Log activity
        await logActivity(
          memberToUpdate.venture_id,
          user.id,
          'member_updated',
          'Team member updated',
          'Team member role or permissions were updated',
          { member_id, changes: Object.keys(updateData) }
        );

        return { success: true, member: updatedMember };

      case 'remove':
        const { member_id: removeId } = memberData;

        if (!removeId) {
          throw new Error('member_id is required');
        }

        // Get member details
        const { data: memberToRemove, error: removeError } = await supabase
          .from('venture_members')
          .select('venture_id, user_id, users(display_name)')
          .eq('id', removeId)
          .single();

        if (removeError || !memberToRemove) {
          throw new Error('Team member not found');
        }

        // Check permissions
        await checkVentureAccess(user.id, memberToRemove.venture_id, 'can_manage_team');

        // Update status to removed instead of deleting
        const { error: updateRemoveError } = await supabase
          .from('venture_members')
          .update({ status: 'removed' })
          .eq('id', removeId);

        if (updateRemoveError) {
          throw new Error(`Failed to remove team member: ${updateRemoveError.message}`);
        }

        // Log activity
        await logActivity(
          memberToRemove.venture_id,
          user.id,
          'member_removed',
          `Team member removed: ${memberToRemove.users.display_name}`,
          `${memberToRemove.users.display_name} was removed from the venture`,
          { member_id: removeId }
        );

        return { success: true, message: 'Team member removed successfully' };

      default:
        throw new Error('Invalid action');
    }

  } catch (error) {
    console.error('Manage team member error:', error);
    throw error;
  }
};

// Get venture analytics
const getVentureAnalytics = async (user, ventureId, timeRange = '30d') => {
  try {
    // Check access
    await checkVentureAccess(user.id, ventureId, 'can_view_analytics');

    const daysBack = timeRange === '7d' ? 7 : timeRange === '30d' ? 30 : timeRange === '90d' ? 90 : 30;
    const startDate = new Date(Date.now() - daysBack * 24 * 60 * 60 * 1000).toISOString().split('T')[0];

    // Get milestone completion trends
    const { data: milestoneData, error: milestoneError } = await supabase
      .from('venture_milestones')
      .select('status, completed_at, estimated_hours, actual_hours')
      .eq('venture_id', ventureId)
      .gte('completed_at', startDate);

    if (milestoneError) {
      console.error('Failed to fetch milestone analytics:', milestoneError);
    }

    // Get budget analytics
    const { data: budgetData, error: budgetError } = await supabase
      .from('venture_budget_items')
      .select('category, planned_amount, actual_amount, status')
      .eq('venture_id', ventureId);

    if (budgetError) {
      console.error('Failed to fetch budget analytics:', budgetError);
    }

    // Get team activity
    const { data: activityData, error: activityError } = await supabase
      .from('venture_activities')
      .select('activity_type, created_at, user_id')
      .eq('venture_id', ventureId)
      .gte('created_at', startDate);

    if (activityError) {
      console.error('Failed to fetch activity analytics:', activityError);
    }

    // Calculate analytics
    const analytics = {
      milestone_completion_rate: milestoneData ?
        (milestoneData.filter(m => m.status === 'completed').length / Math.max(milestoneData.length, 1)) * 100 : 0,
      budget_utilization: budgetData ?
        budgetData.reduce((acc, item) => acc + (item.actual_amount || 0), 0) /
        Math.max(budgetData.reduce((acc, item) => acc + (item.planned_amount || 0), 0), 1) * 100 : 0,
      team_activity_count: activityData ? activityData.length : 0,
      time_efficiency: milestoneData ?
        milestoneData.filter(m => m.actual_hours && m.estimated_hours)
          .reduce((acc, m) => acc + (m.estimated_hours / Math.max(m.actual_hours, 1)), 0) /
        Math.max(milestoneData.filter(m => m.actual_hours && m.estimated_hours).length, 1) * 100 : 100
    };

    return {
      analytics,
      milestone_data: milestoneData || [],
      budget_data: budgetData || [],
      activity_data: activityData || [],
      time_range: timeRange
    };

  } catch (error) {
    console.error('Get venture analytics error:', error);
    throw error;
  }
};

// Main handler function
exports.handler = async (event, context) => {
  // Handle CORS preflight
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers: corsHeaders,
      body: ''
    };
  }

  try {
    // Authenticate user
    const user = await authenticateUser(event.headers.authorization);
    
    // Parse request path and method
    const pathParts = event.path.split('/').filter(Boolean);
    const action = pathParts[pathParts.length - 1];
    const httpMethod = event.httpMethod;
    const body = event.body ? JSON.parse(event.body) : {};
    const queryParams = new URLSearchParams(event.queryStringParameters || {});

    let result;

    switch (action) {
      case 'templates':
        if (httpMethod !== 'GET') {
          throw new Error('Method not allowed');
        }
        result = await getVentureTemplates(user, queryParams);
        break;

      case 'create-from-template':
        if (httpMethod !== 'POST') {
          throw new Error('Method not allowed');
        }
        result = await createVentureFromTemplate(user, body);
        break;

      case 'details':
        if (httpMethod !== 'GET') {
          throw new Error('Method not allowed');
        }
        const ventureId = queryParams.get('venture_id');
        if (!ventureId) {
          throw new Error('venture_id is required');
        }
        result = await getVentureDetails(user, ventureId);
        break;

      case 'milestone':
        if (httpMethod !== 'POST') {
          throw new Error('Method not allowed');
        }
        const milestoneAction = body.action || 'create';
        result = await manageMilestone(user, milestoneAction, body);
        break;

      case 'team':
        if (httpMethod !== 'POST') {
          throw new Error('Method not allowed');
        }
        const teamAction = body.action || 'invite';
        result = await manageTeamMember(user, teamAction, body);
        break;

      case 'analytics':
        if (httpMethod !== 'GET') {
          throw new Error('Method not allowed');
        }
        const analyticsVentureId = queryParams.get('venture_id');
        const timeRange = queryParams.get('time_range') || '30d';
        if (!analyticsVentureId) {
          throw new Error('venture_id is required');
        }
        result = await getVentureAnalytics(user, analyticsVentureId, timeRange);
        break;

      default:
        throw new Error('Invalid action');
    }

    return {
      statusCode: 200,
      headers: corsHeaders,
      body: JSON.stringify({
        success: true,
        data: result,
        timestamp: new Date().toISOString()
      })
    };

  } catch (error) {
    console.error('Enhanced Venture Service API error:', error);
    
    return {
      statusCode: error.message.includes('Authentication') || error.message.includes('Access denied') ? 401 : 
                  error.message.includes('not allowed') ? 405 : 400,
      headers: corsHeaders,
      body: JSON.stringify({
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      })
    };
  }
};
