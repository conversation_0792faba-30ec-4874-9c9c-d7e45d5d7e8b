#!/usr/bin/env node

/**
 * Comprehensive Legal Agreement Validation Test Runner
 * 
 * This script executes the comprehensive legal agreement validation test suite
 * and provides detailed reporting on the results.
 * 
 * Usage:
 *   node run-comprehensive-legal-tests.js
 *   npm run test:legal-comprehensive
 */

import { ComprehensiveLegalTestRunner } from './comprehensive-legal-agreement-validation.test.js';

/**
 * Main execution function
 */
async function main() {
  console.log('🚀 Starting Comprehensive Legal Agreement Validation Tests...\n');
  
  const testRunner = new ComprehensiveLegalTestRunner();
  
  try {
    // Initialize the test runner
    await testRunner.initialize();
    
    // Run all tests
    const results = await testRunner.runAllTests();
    
    // Display final results
    console.log('\n🎯 TEST EXECUTION COMPLETE');
    console.log('='.repeat(30));
    
    if (results.allTestsPassed) {
      console.log('🎉 ALL TESTS PASSED - SYSTEM READY FOR PRODUCTION!');
      console.log(`✅ ${results.productionReadyTests}/${results.totalTests} scenarios meet production requirements`);
      console.log(`📊 Average accuracy: ${results.averageAccuracy.toFixed(1)}%`);
    } else {
      console.log('⚠️  SOME TESTS REQUIRE ATTENTION');
      console.log(`📊 Production ready: ${results.productionReadyTests}/${results.totalTests} scenarios`);
      console.log(`📊 Average accuracy: ${results.averageAccuracy.toFixed(1)}%`);
      console.log(`🚨 Total critical errors: ${results.totalCriticalErrors}`);
      console.log('\n🔧 Please review the validation reports and fix issues before production deployment.');
    }
    
    // Exit with appropriate code
    process.exit(results.allTestsPassed ? 0 : 1);
    
  } catch (error) {
    console.error('\n❌ TEST EXECUTION FAILED');
    console.error('='.repeat(25));
    console.error(`Error: ${error.message}`);
    
    if (process.env.DEBUG) {
      console.error('\nStack trace:');
      console.error(error.stack);
    }
    
    console.error('\n💡 Troubleshooting tips:');
    console.error('   - Ensure all dependencies are installed: npm install');
    console.error('   - Check that the lawyer template exists at client/public/example-cog-contributor-agreement.md');
    console.error('   - Verify the agreement generator is properly configured');
    console.error('   - Run with DEBUG=true for detailed error information');
    
    process.exit(1);
  }
}

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Unhandled Promise Rejection:', reason);
  process.exit(1);
});

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error('❌ Uncaught Exception:', error.message);
  process.exit(1);
});

// Run the main function
main();
