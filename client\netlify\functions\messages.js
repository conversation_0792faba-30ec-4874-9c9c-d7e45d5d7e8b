// Messaging API
// Handles direct messaging between allies
// Based on docs/design-system/systems/social-system.md

const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_ANON_KEY;

const headers = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Content-Type': 'application/json'
};

exports.handler = async (event, context) => {
  // Handle CORS preflight
  if (event.httpMethod === 'OPTIONS') {
    return { statusCode: 200, headers, body: '' };
  }

  try {
    const supabase = createClient(supabaseUrl, supabaseKey);
    
    // Get user from authorization header
    const authHeader = event.headers.authorization;
    if (!authHeader) {
      return {
        statusCode: 401,
        headers,
        body: JSON.stringify({ error: 'Authorization header required' })
      };
    }

    const token = authHeader.replace('Bearer ', '');
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    
    if (authError || !user) {
      return {
        statusCode: 401,
        headers,
        body: JSON.stringify({ error: 'Invalid authentication token' })
      };
    }

    const { httpMethod, path } = event;
    const pathParts = path.split('/').filter(part => part);
    
    switch (httpMethod) {
      case 'GET':
        return await handleGet(supabase, user, pathParts, event.queryStringParameters);
      case 'POST':
        return await handlePost(supabase, user, pathParts, JSON.parse(event.body || '{}'));
      case 'PUT':
        return await handlePut(supabase, user, pathParts, JSON.parse(event.body || '{}'));
      default:
        return {
          statusCode: 405,
          headers,
          body: JSON.stringify({ error: 'Method not allowed' })
        };
    }
  } catch (error) {
    console.error('API Error:', error);
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ error: 'Internal server error', details: error.message })
    };
  }
};

// GET handlers
async function handleGet(supabase, user, pathParts, queryParams) {
  if (pathParts.length === 1) {
    // GET /messages - get conversation threads
    return await getConversationThreads(supabase, user.id, queryParams);
  } else if (pathParts.length === 2) {
    // GET /messages/:threadId - get conversation history
    const threadId = pathParts[1];
    return await getConversationHistory(supabase, user.id, threadId, queryParams);
  }
  
  return {
    statusCode: 404,
    headers,
    body: JSON.stringify({ error: 'Endpoint not found' })
  };
}

// Get conversation threads
async function getConversationThreads(supabase, userId, queryParams) {
  const { limit = 20, offset = 0 } = queryParams || {};
  
  // Get latest message for each conversation
  const { data, error } = await supabase
    .from('messages')
    .select(`
      id,
      from_user_id,
      to_user_id,
      content,
      message_type,
      read_at,
      created_at,
      from_user:from_user_id(id, display_name, email),
      to_user:to_user_id(id, display_name, email)
    `)
    .or(`from_user_id.eq.${userId},to_user_id.eq.${userId}`)
    .eq('is_deleted', false)
    .order('created_at', { ascending: false })
    .range(offset, offset + limit - 1);

  if (error) {
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ error: 'Failed to fetch conversations', details: error.message })
    };
  }

  // Group messages by conversation partner
  const conversationMap = new Map();
  
  data.forEach(message => {
    const partnerId = message.from_user_id === userId ? message.to_user_id : message.from_user_id;
    const partner = message.from_user_id === userId ? message.to_user : message.from_user;
    
    if (!conversationMap.has(partnerId)) {
      conversationMap.set(partnerId, {
        partner,
        latest_message: message,
        unread_count: 0
      });
    }
    
    // Count unread messages
    if (message.to_user_id === userId && !message.read_at) {
      conversationMap.get(partnerId).unread_count++;
    }
  });

  const conversations = Array.from(conversationMap.values())
    .sort((a, b) => new Date(b.latest_message.created_at) - new Date(a.latest_message.created_at));

  return {
    statusCode: 200,
    headers,
    body: JSON.stringify({
      conversations,
      total: conversations.length,
      hasMore: conversations.length === limit
    })
  };
}

// Get conversation history
async function getConversationHistory(supabase, userId, partnerId, queryParams) {
  const { limit = 50, offset = 0, before } = queryParams || {};
  
  // Verify ally relationship
  const { data: allyCheck } = await supabase
    .from('user_allies')
    .select('id')
    .or(`and(user_id.eq.${userId},ally_id.eq.${partnerId}),and(user_id.eq.${partnerId},ally_id.eq.${userId})`)
    .eq('status', 'accepted')
    .single();

  if (!allyCheck) {
    return {
      statusCode: 403,
      headers,
      body: JSON.stringify({ error: 'Can only message allies' })
    };
  }

  let query = supabase
    .from('messages')
    .select(`
      id,
      from_user_id,
      to_user_id,
      content,
      message_type,
      reply_to_id,
      read_at,
      created_at,
      attachment_url,
      attachment_type,
      from_user:from_user_id(id, display_name),
      to_user:to_user_id(id, display_name)
    `)
    .or(`and(from_user_id.eq.${userId},to_user_id.eq.${partnerId}),and(from_user_id.eq.${partnerId},to_user_id.eq.${userId})`)
    .eq('is_deleted', false)
    .order('created_at', { ascending: false })
    .range(offset, offset + limit - 1);

  if (before) {
    query = query.lt('created_at', before);
  }

  const { data, error } = await query;

  if (error) {
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ error: 'Failed to fetch messages', details: error.message })
    };
  }

  return {
    statusCode: 200,
    headers,
    body: JSON.stringify({
      messages: data.reverse(), // Return in chronological order
      total: data.length,
      hasMore: data.length === limit
    })
  };
}

// POST handlers
async function handlePost(supabase, user, pathParts, body) {
  if (pathParts.length === 1) {
    // POST /messages - send new message
    return await sendMessage(supabase, user.id, body);
  }
  
  return {
    statusCode: 404,
    headers,
    body: JSON.stringify({ error: 'Endpoint not found' })
  };
}

// Send message
async function sendMessage(supabase, userId, body) {
  const { to_user_id, content, message_type = 'text', reply_to_id, attachment_url, attachment_type } = body;
  
  if (!to_user_id || !content) {
    return {
      statusCode: 400,
      headers,
      body: JSON.stringify({ error: 'to_user_id and content are required' })
    };
  }

  // Verify ally relationship
  const { data: allyCheck } = await supabase
    .from('user_allies')
    .select('id')
    .or(`and(user_id.eq.${userId},ally_id.eq.${to_user_id}),and(user_id.eq.${to_user_id},ally_id.eq.${userId})`)
    .eq('status', 'accepted')
    .single();

  if (!allyCheck) {
    return {
      statusCode: 403,
      headers,
      body: JSON.stringify({ error: 'Can only message allies' })
    };
  }

  const { data, error } = await supabase
    .from('messages')
    .insert({
      from_user_id: userId,
      to_user_id,
      content,
      message_type,
      reply_to_id,
      attachment_url,
      attachment_type
    })
    .select(`
      id,
      from_user_id,
      to_user_id,
      content,
      message_type,
      reply_to_id,
      read_at,
      created_at,
      attachment_url,
      attachment_type,
      from_user:from_user_id(id, display_name),
      to_user:to_user_id(id, display_name)
    `)
    .single();

  if (error) {
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ error: 'Failed to send message', details: error.message })
    };
  }

  return {
    statusCode: 201,
    headers,
    body: JSON.stringify({ message: 'Message sent successfully', data })
  };
}

// PUT handlers
async function handlePut(supabase, user, pathParts, body) {
  if (pathParts.length === 3 && pathParts[2] === 'read') {
    // PUT /messages/:id/read - mark message as read
    const messageId = pathParts[1];
    return await markMessageAsRead(supabase, user.id, messageId);
  }
  
  return {
    statusCode: 404,
    headers,
    body: JSON.stringify({ error: 'Endpoint not found' })
  };
}

// Mark message as read
async function markMessageAsRead(supabase, userId, messageId) {
  const { data, error } = await supabase
    .from('messages')
    .update({ read_at: new Date().toISOString() })
    .eq('id', messageId)
    .eq('to_user_id', userId) // Only recipient can mark as read
    .select()
    .single();

  if (error) {
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ error: 'Failed to mark message as read', details: error.message })
    };
  }

  if (!data) {
    return {
      statusCode: 404,
      headers,
      body: JSON.stringify({ error: 'Message not found or not authorized' })
    };
  }

  return {
    statusCode: 200,
    headers,
    body: JSON.stringify({ message: 'Message marked as read', data })
  };
}
