/**
 * Agreement Template System
 * 
 * Dynamic template system supporting industry-specific agreement generation
 * with variable replacement, content customization, and template management
 */

import { INDUSTRY_REVENUE_MODELS } from './revenueModelStructures.js';
import { INDUSTRY_IP_MODELS } from './ipRightsStructures.js';

// ============================================================================
// TEMPLATE VARIABLE SYSTEM
// ============================================================================

export const TEMPLATE_VARIABLES = {
  // Company/Alliance Variables
  COMPANY_NAME: '{{COMPANY_NAME}}',
  COMPANY_LEGAL_NAME: '{{COMPANY_LEGAL_NAME}}',
  COMPANY_ADDRESS: '{{COMPANY_ADDRESS}}',
  COMPANY_STATE: '{{COMPANY_STATE}}',
  COMPANY_COUNTRY: '{{COMPANY_COUNTRY}}',
  COMPANY_EMAIL: '{{COMPANY_EMAIL}}',
  COMPANY_PHONE: '{{COMPANY_PHONE}}',
  
  // Signatory Variables
  COMPANY_SIGNER_NAME: '{{COMPANY_SIGNER_NAME}}',
  COMPANY_SIGNER_TITLE: '{{COMPANY_SIGNER_TITLE}}',
  CONTRIBUTOR_NAME: '{{CONTRIBUTOR_NAME}}',
  CONTRIBUTOR_EMAIL: '{{CONTRIBUTOR_EMAIL}}',
  CONTRIBUTOR_ADDRESS: '{{CONTRIBUTOR_ADDRESS}}',
  
  // Project/Venture Variables
  PROJECT_NAME: '{{PROJECT_NAME}}',
  PROJECT_DESCRIPTION: '{{PROJECT_DESCRIPTION}}',
  PROJECT_TYPE: '{{PROJECT_TYPE}}',
  PROJECT_INDUSTRY: '{{PROJECT_INDUSTRY}}',
  
  // Date Variables
  CURRENT_DATE: '{{CURRENT_DATE}}',
  EFFECTIVE_DATE: '{{EFFECTIVE_DATE}}',
  EXPIRATION_DATE: '{{EXPIRATION_DATE}}',
  
  // Financial Variables
  REVENUE_SHARE_PERCENTAGE: '{{REVENUE_SHARE_PERCENTAGE}}',
  MINIMUM_PAYOUT: '{{MINIMUM_PAYOUT}}',
  MAXIMUM_PAYOUT: '{{MAXIMUM_PAYOUT}}',
  PAYMENT_FREQUENCY: '{{PAYMENT_FREQUENCY}}',
  
  // Legal Variables
  GOVERNING_LAW_STATE: '{{GOVERNING_LAW_STATE}}',
  JURISDICTION: '{{JURISDICTION}}',
  
  // Dynamic Content Sections
  SCHEDULE_A_CONTENT: '{{SCHEDULE_A_CONTENT}}',
  SCHEDULE_B_CONTENT: '{{SCHEDULE_B_CONTENT}}',
  EXHIBIT_I_CONTENT: '{{EXHIBIT_I_CONTENT}}',
  EXHIBIT_II_CONTENT: '{{EXHIBIT_II_CONTENT}}'
};

// ============================================================================
// BASE AGREEMENT TEMPLATE
// ============================================================================

export const BASE_AGREEMENT_TEMPLATE = `
# {{COMPANY_NAME}} CONTRIBUTOR AGREEMENT

**EFFECTIVE DATE:** {{EFFECTIVE_DATE}}

This Contributor Agreement ("Agreement") is entered into on {{CURRENT_DATE}}, by and between {{COMPANY_LEGAL_NAME}}, a {{COMPANY_STATE}} corporation with its principal place of business at {{COMPANY_ADDRESS}} (the "Company"), and {{CONTRIBUTOR_NAME}}, an individual with an address at {{CONTRIBUTOR_ADDRESS}} (the "Contributor").

## RECITALS

WHEREAS, the Company is engaged in {{PROJECT_INDUSTRY}} and is developing {{PROJECT_NAME}}, {{PROJECT_DESCRIPTION}} (the "Project");

WHEREAS, the Contributor possesses skills, knowledge, and expertise that would be valuable to the development and success of the Project;

WHEREAS, the Company desires to engage the Contributor to provide services in connection with the Project; and

WHEREAS, the Contributor is willing to provide such services subject to the terms and conditions set forth herein;

NOW, THEREFORE, in consideration of the mutual covenants and agreements contained herein, and for other good and valuable consideration, the receipt and sufficiency of which are hereby acknowledged, the parties agree as follows:

## 1. SERVICES

The Contributor agrees to provide services to the Company in connection with the Project as more particularly described in **Schedule A** attached hereto and incorporated herein by reference (the "Services").

## 2. COMPENSATION

In consideration for the Services, the Company shall provide compensation to the Contributor as set forth in **Schedule B** attached hereto and incorporated herein by reference.

## 3. INTELLECTUAL PROPERTY

{{IP_RIGHTS_SECTION}}

## 4. CONFIDENTIALITY

The Contributor acknowledges that during the performance of the Services, the Contributor may have access to confidential and proprietary information of the Company. The Contributor agrees to maintain the confidentiality of such information and not to disclose it to any third party without the prior written consent of the Company.

## 5. TERM AND TERMINATION

This Agreement shall commence on the Effective Date and shall continue until terminated by either party upon thirty (30) days' written notice to the other party.

## 6. GOVERNING LAW

This Agreement shall be governed by and construed in accordance with the laws of the State of {{GOVERNING_LAW_STATE}}, without regard to its conflict of laws principles.

## 7. DISPUTE RESOLUTION

Any disputes arising under this Agreement shall be resolved through binding arbitration in {{JURISDICTION}}.

## 8. ENTIRE AGREEMENT

This Agreement, including all schedules and exhibits attached hereto, constitutes the entire agreement between the parties and supersedes all prior negotiations, representations, or agreements relating to the subject matter hereof.

## SIGNATURES

**COMPANY:**

{{COMPANY_LEGAL_NAME}}

By: ______________________
Name: {{COMPANY_SIGNER_NAME}}
Title: {{COMPANY_SIGNER_TITLE}}
Date: _______________

**CONTRIBUTOR:**

______________________
{{CONTRIBUTOR_NAME}}
Date: _______________

---

{{SCHEDULE_A_CONTENT}}

---

{{SCHEDULE_B_CONTENT}}

---

{{EXHIBIT_I_CONTENT}}

---

{{EXHIBIT_II_CONTENT}}
`;

// ============================================================================
// INDUSTRY-SPECIFIC TEMPLATE VARIATIONS
// ============================================================================

export const INDUSTRY_TEMPLATES = {
  technology: {
    software: {
      name: 'Software Development Agreement',
      template: BASE_AGREEMENT_TEMPLATE,
      customizations: {
        PROJECT_TYPE: 'software application',
        IP_RIGHTS_SECTION: `
### 3.1 Work Product Ownership
All software code, documentation, designs, and related materials created by the Contributor in connection with the Project ("Work Product") shall be owned by the Company as works made for hire.

### 3.2 Pre-Existing IP
The Contributor retains ownership of any pre-existing intellectual property, provided that the Company receives a perpetual, royalty-free license to use such pre-existing IP in connection with the Project.

### 3.3 Open Source Compliance
The Contributor agrees to comply with all open source license requirements and to disclose any open source components used in the Work Product.
        `
      }
    },
    
    saas: {
      name: 'SaaS Platform Development Agreement',
      template: BASE_AGREEMENT_TEMPLATE,
      customizations: {
        PROJECT_TYPE: 'software-as-a-service platform',
        IP_RIGHTS_SECTION: `
### 3.1 Platform Ownership
All platform code, algorithms, user interfaces, and related technology developed by the Contributor shall be owned by the Company.

### 3.2 Data Rights
The Company shall own all rights to user data, analytics, and platform-generated insights, subject to applicable privacy laws.

### 3.3 API and Integration Rights
The Contributor grants the Company exclusive rights to all APIs, integrations, and third-party connections developed for the platform.
        `
      }
    }
  },
  
  creative: {
    music: {
      name: 'Music Collaboration Agreement',
      template: BASE_AGREEMENT_TEMPLATE,
      customizations: {
        PROJECT_TYPE: 'musical composition and recording',
        IP_RIGHTS_SECTION: `
### 3.1 Songwriting Credits
Songwriting credits and publishing rights shall be allocated based on creative contribution as documented in the project records.

### 3.2 Performance Rights
Both parties shall share performance royalties through their respective performing rights organizations (ASCAP, BMI, SESAC).

### 3.3 Master Recording Rights
Master recording ownership shall be determined based on financial contribution to recording costs and creative input.

### 3.4 Sync Licensing
All synchronization licensing opportunities require mutual consent of both parties.
        `
      }
    },
    
    film: {
      name: 'Film Production Agreement',
      template: BASE_AGREEMENT_TEMPLATE,
      customizations: {
        PROJECT_TYPE: 'film or video production',
        IP_RIGHTS_SECTION: `
### 3.1 Copyright Ownership
The Company shall own the copyright in the completed film, including all footage, audio, and related materials.

### 3.2 Credit Rights
The Contributor shall receive appropriate screen credit as agreed upon in the project specifications.

### 3.3 Residual Rights
The Contributor may be entitled to residual payments based on distribution revenue as specified in Schedule B.

### 3.4 Moral Rights
The Contributor waives moral rights to the extent permitted by law, except for the right to proper attribution.
        `
      }
    }
  },
  
  service: {
    consulting: {
      name: 'Consulting Services Agreement',
      template: BASE_AGREEMENT_TEMPLATE,
      customizations: {
        PROJECT_TYPE: 'consulting and advisory services',
        IP_RIGHTS_SECTION: `
### 3.1 Work Product
All reports, recommendations, and deliverables created specifically for the Company shall be owned by the Company.

### 3.2 General Methodologies
The Contributor retains ownership of general methodologies, frameworks, and know-how developed independently.

### 3.3 Confidential Information
The Contributor may use general knowledge and experience gained but shall not disclose confidential client information.

### 3.4 Non-Compete
The Contributor agrees not to provide similar services to direct competitors during the term of this Agreement.
        `
      }
    },
    
    agency: {
      name: 'Agency Partnership Agreement',
      template: BASE_AGREEMENT_TEMPLATE,
      customizations: {
        PROJECT_TYPE: 'marketing and creative services',
        IP_RIGHTS_SECTION: `
### 3.1 Client Work Product
All work product created for clients shall be owned by the respective clients, with the Company retaining portfolio rights.

### 3.2 Agency IP
The Company shall own all internal processes, client lists, and proprietary methodologies developed for agency operations.

### 3.3 Creative Rights
The Contributor retains the right to use work samples for portfolio purposes, subject to client confidentiality requirements.

### 3.4 Client Relationships
Client relationships and contacts developed during the engagement shall belong to the Company.
        `
      }
    }
  }
};

// ============================================================================
// TEMPLATE PROCESSING ENGINE
// ============================================================================

export class AgreementTemplateEngine {
  constructor() {
    this.templates = INDUSTRY_TEMPLATES;
    this.variables = TEMPLATE_VARIABLES;
    this.generatedAgreements = new Map();
  }
  
  /**
   * Get template for specific industry and collaboration type
   */
  getTemplate(industry, collaborationType) {
    const industryTemplates = this.templates[industry];
    if (!industryTemplates) {
      throw new Error(`No templates found for industry: ${industry}`);
    }
    
    const template = industryTemplates[collaborationType];
    if (!template) {
      throw new Error(`No template found for collaboration type: ${collaborationType} in industry: ${industry}`);
    }
    
    return template;
  }
  
  /**
   * Process template with variable replacement
   */
  processTemplate(templateContent, variables) {
    let processedContent = templateContent;
    
    // Replace all variables
    for (const [key, value] of Object.entries(variables)) {
      const variablePattern = new RegExp(`{{${key}}}`, 'g');
      processedContent = processedContent.replace(variablePattern, value || '');
    }
    
    // Clean up any remaining unreplaced variables
    processedContent = processedContent.replace(/{{[^}]+}}/g, '[TO BE FILLED]');
    
    return processedContent;
  }
  
  /**
   * Generate complete agreement from alliance, venture, and contributor data
   */
  generateAgreement(alliance, venture, contributor, options = {}) {
    try {
      // Get appropriate template
      const template = this.getTemplate(venture.industry || alliance.industry, venture.venture_type);
      
      // Prepare variables
      const variables = this.prepareVariables(alliance, venture, contributor, options);
      
      // Generate dynamic content sections
      const scheduleA = this.generateScheduleA(venture, alliance);
      const scheduleB = this.generateScheduleB(venture, alliance);
      const exhibitI = this.generateExhibitI(venture);
      const exhibitII = this.generateExhibitII(venture);
      
      // Add dynamic content to variables
      variables.SCHEDULE_A_CONTENT = scheduleA;
      variables.SCHEDULE_B_CONTENT = scheduleB;
      variables.EXHIBIT_I_CONTENT = exhibitI;
      variables.EXHIBIT_II_CONTENT = exhibitII;
      
      // Apply template customizations
      let templateContent = template.template;
      if (template.customizations) {
        for (const [key, value] of Object.entries(template.customizations)) {
          variables[key] = value;
        }
      }
      
      // Process template
      const agreement = this.processTemplate(templateContent, variables);
      
      return {
        success: true,
        agreement,
        templateUsed: template.name,
        variables
      };
      
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }
  
  /**
   * Prepare variables from alliance, venture, and contributor data
   */
  prepareVariables(alliance, venture, contributor, options) {
    const today = new Date();
    const currentDate = today.toLocaleDateString('en-US', {
      month: 'long',
      day: 'numeric',
      year: 'numeric'
    });
    
    // Get alliance business details
    const businessInfo = alliance.legal_entity_info || {};
    const contactInfo = alliance.contact_information || {};
    const address = alliance.business_address || {};
    
    // Get revenue model details
    const revenueModel = venture.revenue_model || alliance.default_revenue_model || {};
    
    return {
      // Company/Alliance Variables
      COMPANY_NAME: alliance.name,
      COMPANY_LEGAL_NAME: businessInfo.legalName || alliance.name,
      COMPANY_ADDRESS: this.formatAddress(address),
      COMPANY_STATE: address.state || businessInfo.incorporationState || 'Delaware',
      COMPANY_COUNTRY: address.country || 'United States',
      COMPANY_EMAIL: contactInfo.email || contactInfo.primaryContact?.email || '<EMAIL>',
      COMPANY_PHONE: contactInfo.phone || contactInfo.primaryContact?.phone || '',
      
      // Signatory Variables
      COMPANY_SIGNER_NAME: contactInfo.primaryContact?.name || 'Company Representative',
      COMPANY_SIGNER_TITLE: contactInfo.primaryContact?.title || 'Authorized Representative',
      CONTRIBUTOR_NAME: contributor.full_name || contributor.name || 'Contributor',
      CONTRIBUTOR_EMAIL: contributor.email || '',
      CONTRIBUTOR_ADDRESS: contributor.address || '[Contributor Address]',
      
      // Project/Venture Variables
      PROJECT_NAME: venture.name,
      PROJECT_DESCRIPTION: venture.description,
      PROJECT_TYPE: venture.venture_type || 'project',
      PROJECT_INDUSTRY: venture.industry || alliance.industry || 'technology',
      
      // Date Variables
      CURRENT_DATE: currentDate,
      EFFECTIVE_DATE: options.effectiveDate || currentDate,
      EXPIRATION_DATE: options.expirationDate || '',
      
      // Financial Variables
      REVENUE_SHARE_PERCENTAGE: revenueModel.revenue_share_percentage || '25',
      MINIMUM_PAYOUT: this.formatCurrency(revenueModel.minimum_payout_threshold || 100),
      MAXIMUM_PAYOUT: this.formatCurrency(revenueModel.maximum_individual_payout) || 'No limit',
      PAYMENT_FREQUENCY: revenueModel.payment_frequency || 'quarterly',
      
      // Legal Variables
      GOVERNING_LAW_STATE: address.state || businessInfo.incorporationState || 'Delaware',
      JURISDICTION: `${address.city || 'County'}, ${address.state || 'Delaware'}`
    };
  }
  
  /**
   * Format address object into string
   */
  formatAddress(address) {
    if (!address || Object.keys(address).length === 0) {
      return '[Company Address]';
    }
    
    const parts = [
      address.street,
      address.city,
      address.state,
      address.zipCode
    ].filter(Boolean);
    
    return parts.join(', ');
  }
  
  /**
   * Format currency amount
   */
  formatCurrency(amount) {
    if (!amount) return '';
    return `$${Number(amount).toLocaleString()}`;
  }
  
  /**
   * Generate Schedule A (Services Description)
   */
  generateScheduleA(venture, alliance) {
    return `
## SCHEDULE A
### Description of Services

The Contributor shall provide the following services in connection with the ${venture.name} project:

**Project Overview:**
${venture.description}

**Specific Services:**
${this.generateServicesList(venture)}

**Deliverables:**
${this.generateDeliverablesList(venture)}

**Performance Standards:**
${this.generatePerformanceStandards(venture)}
    `;
  }
  
  /**
   * Generate Schedule B (Compensation)
   */
  generateScheduleB(venture, alliance) {
    const revenueModel = venture.revenue_model || alliance.default_revenue_model || {};
    
    return `
## SCHEDULE B
### Description of Consideration

**Compensation Structure:**
${this.generateCompensationStructure(revenueModel)}

**Payment Terms:**
${this.generatePaymentTerms(revenueModel)}

**Revenue Sharing Details:**
${this.generateRevenueDetails(revenueModel)}
    `;
  }
  
  /**
   * Generate Exhibit I (Project Specifications)
   */
  generateExhibitI(venture) {
    const specs = venture.specifications || {};
    const techStack = venture.tech_stack || {};
    
    return `
## EXHIBIT I
### Project Specifications

**${venture.name} - Technical Specifications**

**Project Type:** ${venture.venture_type}
**Industry:** ${venture.industry}

**Technical Requirements:**
${this.generateTechnicalSpecs(techStack, venture.venture_type)}

**Functional Requirements:**
${this.generateFunctionalSpecs(specs)}

**Quality Standards:**
${this.generateQualityStandards(venture)}
    `;
  }
  
  /**
   * Generate Exhibit II (Project Roadmap)
   */
  generateExhibitII(venture) {
    const roadmap = venture.roadmap || {};
    
    return `
## EXHIBIT II
### Project Roadmap and Milestones

**${venture.name} - Development Roadmap**

**Project Phases:**
${this.generateProjectPhases(roadmap)}

**Key Milestones:**
${this.generateMilestones(roadmap)}

**Timeline:**
${this.generateTimeline(roadmap)}
    `;
  }
  
  // Helper methods for content generation
  generateServicesList(venture) {
    const services = venture.specifications?.services || [];
    if (services.length === 0) {
      return `- Development and implementation services for ${venture.venture_type} project
- Code review and quality assurance
- Documentation and technical specifications
- Testing and debugging
- Collaboration with team members`;
    }
    return services.map(service => `- ${service}`).join('\n');
  }
  
  generateDeliverablesList(venture) {
    const deliverables = venture.specifications?.deliverables || [];
    if (deliverables.length === 0) {
      return `- Completed project components as specified
- Technical documentation
- Source code and related files
- Test results and quality reports`;
    }
    return deliverables.map(deliverable => `- ${deliverable}`).join('\n');
  }
  
  generatePerformanceStandards(venture) {
    return `- All work must meet industry standard quality requirements
- Code must pass all automated tests and code review
- Deliverables must be completed within agreed timeframes
- Communication and collaboration standards must be maintained`;
  }
  
  generateCompensationStructure(revenueModel) {
    const type = revenueModel.model_type || 'percentage';
    const percentage = revenueModel.revenue_share_percentage || 25;
    
    return `The Contributor shall receive ${percentage}% of net revenue generated from the project, calculated and paid ${revenueModel.payment_frequency || 'quarterly'}.`;
  }
  
  generatePaymentTerms(revenueModel) {
    return `- Minimum payout threshold: ${this.formatCurrency(revenueModel.minimum_payout_threshold || 100)}
- Payment frequency: ${revenueModel.payment_frequency || 'Quarterly'}
- Payment method: Electronic transfer
- Payment timing: Within 45 days of period end`;
  }
  
  generateRevenueDetails(revenueModel) {
    return `- Revenue calculation based on net receipts after platform fees and expenses
- Detailed revenue reports provided with each payment
- Right to audit revenue calculations upon reasonable notice`;
  }
  
  generateTechnicalSpecs(techStack, ventureType) {
    if (Object.keys(techStack).length === 0) {
      return `Technical specifications to be determined based on ${ventureType} project requirements.`;
    }
    
    return Object.entries(techStack)
      .map(([key, value]) => `- ${key}: ${value}`)
      .join('\n');
  }
  
  generateFunctionalSpecs(specs) {
    if (Object.keys(specs).length === 0) {
      return `Functional specifications to be defined during project planning phase.`;
    }
    
    return Object.entries(specs)
      .map(([key, value]) => `- ${key}: ${value}`)
      .join('\n');
  }
  
  generateQualityStandards(venture) {
    return `- All deliverables must meet professional industry standards
- Code must be well-documented and maintainable
- User interfaces must be intuitive and accessible
- Performance requirements must be met as specified`;
  }
  
  generateProjectPhases(roadmap) {
    const phases = roadmap.phases || [];
    if (phases.length === 0) {
      return `Project phases to be defined during planning stage.`;
    }
    
    return phases.map((phase, index) => 
      `**Phase ${index + 1}: ${phase.name}**
Duration: ${phase.duration}
Deliverables: ${phase.deliverables?.join(', ') || 'To be defined'}`
    ).join('\n\n');
  }
  
  generateMilestones(roadmap) {
    const milestones = roadmap.milestones || [];
    if (milestones.length === 0) {
      return `Project milestones to be defined during planning stage.`;
    }
    
    return milestones.map(milestone => 
      `- ${milestone.name}: ${milestone.deadline} - ${milestone.criteria}`
    ).join('\n');
  }
  
  generateTimeline(roadmap) {
    return `Project timeline will be established based on scope and resource availability. Regular progress reviews will be conducted to ensure adherence to schedule.`;
  }
}

// ============================================================================
// TEMPLATE VALIDATION
// ============================================================================

export const validateTemplate = (template) => {
  const errors = [];
  
  if (!template.name) errors.push('Template name is required');
  if (!template.template) errors.push('Template content is required');
  
  // Check for required variables
  const requiredVars = ['COMPANY_NAME', 'CONTRIBUTOR_NAME', 'PROJECT_NAME'];
  for (const varName of requiredVars) {
    if (!template.template.includes(`{{${varName}}}`)) {
      errors.push(`Template must include {{${varName}}} variable`);
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
  }

  /**
   * Generate complete agreement from test data (for testing compatibility)
   */
  generateAgreement(agreementData) {
    const { templateType, industry, venture, alliance, variables = {}, customClauses = [] } = agreementData;

    // Generate agreement content based on template type
    const agreement = {
      id: this.generateAgreementId(),
      templateType: templateType || 'software_development',
      industry: industry || alliance?.industry || 'TECHNOLOGY',

      // Parties information
      parties: this.extractParties(venture, alliance),

      // Agreement sections
      sections: this.generateAgreementSections(templateType, venture, alliance),

      // Revenue sharing details
      revenueSharing: this.generateRevenueSection(venture, alliance),

      // IP rights details
      ipRights: this.generateIPRightsSection(venture, alliance),

      // Milestones and deliverables
      milestones: venture?.milestones || [],
      deliverables: venture?.deliverables || [],

      // Legal terms
      legalTerms: this.generateLegalTerms(alliance, variables),

      // Custom clauses
      customClauses: customClauses,

      // Generated content
      content: '',

      // Metadata
      generatedAt: new Date().toISOString(),
      status: 'generated'
    };

    // Generate the actual agreement content
    agreement.content = this.generateAgreementContent(agreement);

    // Store the generated agreement
    this.generatedAgreements.set(agreement.id, agreement);

    return agreement;
  }

  /**
   * Extract parties from venture and alliance data
   */
  extractParties(venture, alliance) {
    const parties = [];

    if (venture?.contributors) {
      venture.contributors.forEach(contributor => {
        parties.push({
          email: contributor.email,
          role: contributor.role,
          responsibilities: contributor.responsibilities,
          revenueShare: contributor.revenueShare,
          ipRights: contributor.ipRights
        });
      });
    }

    return parties;
  }

  /**
   * Generate agreement sections based on template type
   */
  generateAgreementSections(templateType, venture, alliance) {
    const baseSections = {
      introduction: 'This Agreement governs the collaborative relationship between the parties.',
      scope: venture?.scope || 'Collaborative work as defined in the project specifications.',
      responsibilities: 'Each party shall perform their designated responsibilities as outlined.',
      compensation: 'Compensation shall be distributed according to the agreed revenue sharing model.',
      intellectualProperty: 'Intellectual property rights shall be managed according to the specified ownership model.',
      confidentiality: 'All parties agree to maintain confidentiality of proprietary information.',
      termination: 'This agreement may be terminated under the conditions specified herein.',
      disputeResolution: 'Disputes shall be resolved through the agreed dispute resolution mechanism.',
      generalProvisions: 'Standard legal provisions and miscellaneous terms.'
    };

    // Customize sections based on template type
    switch (templateType) {
      case 'software_development':
        baseSections.technicalRequirements = 'Software development shall meet specified technical standards.';
        baseSections.codeOwnership = 'Code ownership and licensing terms as specified.';
        break;
      case 'music_production':
        baseSections.creativeRights = 'Creative rights and attribution requirements.';
        baseSections.masterRecordings = 'Master recording ownership and distribution rights.';
        break;
      case 'business_consulting':
        baseSections.deliverables = 'Consulting deliverables and acceptance criteria.';
        baseSections.clientConfidentiality = 'Enhanced confidentiality provisions for client information.';
        break;
    }

    return baseSections;
  }

  /**
   * Generate revenue sharing section
   */
  generateRevenueSection(venture, alliance) {
    const revenueModel = alliance?.revenueModel || 'PERCENTAGE_SPLIT';

    return {
      model: revenueModel,
      currency: alliance?.currency || 'USD',
      paymentFrequency: alliance?.paymentFrequency || 'monthly',
      shares: venture?.contributors?.reduce((acc, contributor) => {
        acc[contributor.email] = contributor.revenueShare;
        return acc;
      }, {}) || {},
      minimumPayout: alliance?.minimumPayout || 100
    };
  }

  /**
   * Generate IP rights section
   */
  generateIPRightsSection(venture, alliance) {
    const ipModel = alliance?.ipOwnershipModel || 'CO_OWNERSHIP';

    return {
      ownershipModel: ipModel,
      attributionRequired: alliance?.attributionRequired !== false,
      assignments: venture?.contributors?.reduce((acc, contributor) => {
        acc[contributor.email] = contributor.ipRights;
        return acc;
      }, {}) || {},
      masterRecordings: 'shared_ownership', // For music projects
      compositions: 'songwriter_retains' // For music projects
    };
  }

  /**
   * Generate legal terms
   */
  generateLegalTerms(alliance, variables) {
    return {
      effectiveDate: variables.effectiveDate || new Date().toISOString(),
      governingLaw: variables.governingLaw || alliance?.jurisdiction || 'Delaware',
      disputeResolution: alliance?.disputeResolution || 'arbitration',
      confidentialityPeriod: variables.confidentialityPeriod || 24,
      jurisdiction: alliance?.jurisdiction || 'US'
    };
  }

  /**
   * Generate the actual agreement content text
   */
  generateAgreementContent(agreement) {
    const { templateType, parties, sections, revenueSharing, ipRights, legalTerms } = agreement;

    let content = `
COLLABORATION AGREEMENT

This Collaboration Agreement ("Agreement") is entered into on ${new Date(legalTerms.effectiveDate).toLocaleDateString()} between the following parties:

PARTIES:
${parties.map((party, index) => `${index + 1}. ${party.email} - ${party.role}`).join('\n')}

1. SCOPE OF WORK
${sections.scope}

2. RESPONSIBILITIES
${parties.map(party => `${party.role}: ${party.responsibilities}`).join('\n')}

3. REVENUE SHARING
Revenue shall be distributed according to the ${revenueSharing.model} model:
${Object.entries(revenueSharing.shares).map(([email, share]) => `- ${email}: ${share}%`).join('\n')}

4. INTELLECTUAL PROPERTY
Intellectual property shall be managed under the ${ipRights.ownershipModel} model.
${ipRights.attributionRequired ? 'Attribution is required for all contributions.' : ''}

5. CONFIDENTIALITY
All parties agree to maintain confidentiality of proprietary information for ${legalTerms.confidentialityPeriod} months.

6. TERMINATION
${sections.termination}

7. DISPUTE RESOLUTION
Disputes shall be resolved through ${legalTerms.disputeResolution} under the laws of ${legalTerms.governingLaw}.

8. GENERAL PROVISIONS
This Agreement shall be governed by the laws of ${legalTerms.governingLaw}.

IN WITNESS WHEREOF, the parties have executed this Agreement as of the date first written above.

${parties.map(party => `
_________________________
${party.email}
${party.role}
Date: ___________
`).join('\n')}
    `.trim();

    return content;
  }

  /**
   * Generate unique agreement ID
   */
  generateAgreementId() {
    return 'agreement_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }
}

// Export the template engine instance
export const templateEngine = new AgreementTemplateEngine();
export const agreementTemplateSystem = new AgreementTemplateEngine();
