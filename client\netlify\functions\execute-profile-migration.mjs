// Execute Profile Migration API function using ES modules
import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client
const initSupabase = () => {
  const supabaseUrl = Netlify.env.get("SUPABASE_URL");
  const supabaseKey = Netlify.env.get("SUPABASE_SERVICE_KEY") || Netlify.env.get("SUPABASE_ANON_KEY");
  
  if (!supabaseUrl || !supabaseKey) {
    throw new Error('Missing Supabase credentials');
  }
  
  return createClient(supabaseUrl, supabaseKey);
};

export default async (req, context) => {
  // Check if this is a POST request
  if (req.method !== 'POST') {
    return new Response(JSON.stringify({ error: 'Method not allowed' }), {
      status: 405,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }

  // Check for admin authorization
  const authHeader = req.headers.get('Authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return new Response(JSON.stringify({ error: 'Unauthorized' }), {
      status: 401,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }

  try {
    // Initialize Supabase client
    const supabase = initSupabase();
    
    // Verify the user is an admin
    const token = authHeader.replace('Bearer ', '');
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    
    if (authError || !user) {
      return new Response(JSON.stringify({ error: 'Invalid token' }), {
        status: 401,
        headers: {
          'Content-Type': 'application/json'
        }
      });
    }
    
    // Check if user is admin
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('is_admin')
      .eq('id', user.id)
      .single();
    
    if (userError || !userData || !userData.is_admin) {
      return new Response(JSON.stringify({ error: 'Unauthorized - Admin access required' }), {
        status: 403,
        headers: {
          'Content-Type': 'application/json'
        }
      });
    }
    
    // Execute the migration
    const migrationResult = await executeMigration(supabase);
    
    return new Response(JSON.stringify(migrationResult), {
      status: 200,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  } catch (error) {
    console.error('Error executing migration:', error);
    
    return new Response(JSON.stringify({ 
      error: 'Internal server error', 
      details: error.message 
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }
};

// Function to execute the migration
async function executeMigration(supabase) {
  const results = {
    steps: [],
    success: true
  };
  
  try {
    // Step 1: Add new columns to the users table
    results.steps.push({ step: 'Adding new columns to users table', status: 'started' });
    
    // Add headline column
    try {
      await supabase.rpc('execute_sql', { 
        sql_query: 'ALTER TABLE public.users ADD COLUMN IF NOT EXISTS headline TEXT;' 
      });
      results.steps.push({ step: 'Added headline column', status: 'success' });
    } catch (error) {
      results.steps.push({ step: 'Adding headline column', status: 'error', details: error.message });
      results.success = false;
    }
    
    // Add location column
    try {
      await supabase.rpc('execute_sql', { 
        sql_query: 'ALTER TABLE public.users ADD COLUMN IF NOT EXISTS location TEXT;' 
      });
      results.steps.push({ step: 'Added location column', status: 'success' });
    } catch (error) {
      results.steps.push({ step: 'Adding location column', status: 'error', details: error.message });
      results.success = false;
    }
    
    // Add website column
    try {
      await supabase.rpc('execute_sql', { 
        sql_query: 'ALTER TABLE public.users ADD COLUMN IF NOT EXISTS website TEXT;' 
      });
      results.steps.push({ step: 'Added website column', status: 'success' });
    } catch (error) {
      results.steps.push({ step: 'Adding website column', status: 'error', details: error.message });
      results.success = false;
    }
    
    // Add cover_image_url column
    try {
      await supabase.rpc('execute_sql', { 
        sql_query: 'ALTER TABLE public.users ADD COLUMN IF NOT EXISTS cover_image_url TEXT;' 
      });
      results.steps.push({ step: 'Added cover_image_url column', status: 'success' });
    } catch (error) {
      results.steps.push({ step: 'Adding cover_image_url column', status: 'error', details: error.message });
      results.success = false;
    }
    
    // Add status_message column
    try {
      await supabase.rpc('execute_sql', { 
        sql_query: 'ALTER TABLE public.users ADD COLUMN IF NOT EXISTS status_message TEXT;' 
      });
      results.steps.push({ step: 'Added status_message column', status: 'success' });
    } catch (error) {
      results.steps.push({ step: 'Adding status_message column', status: 'error', details: error.message });
      results.success = false;
    }
    
    // Add availability_status column
    try {
      await supabase.rpc('execute_sql', { 
        sql_query: 'ALTER TABLE public.users ADD COLUMN IF NOT EXISTS availability_status TEXT;' 
      });
      results.steps.push({ step: 'Added availability_status column', status: 'success' });
    } catch (error) {
      results.steps.push({ step: 'Adding availability_status column', status: 'error', details: error.message });
      results.success = false;
    }
    
    // Add profile_views column
    try {
      await supabase.rpc('execute_sql', { 
        sql_query: 'ALTER TABLE public.users ADD COLUMN IF NOT EXISTS profile_views INTEGER DEFAULT 0;' 
      });
      results.steps.push({ step: 'Added profile_views column', status: 'success' });
    } catch (error) {
      results.steps.push({ step: 'Adding profile_views column', status: 'error', details: error.message });
      results.success = false;
    }
    
    // Add theme_settings column
    try {
      await supabase.rpc('execute_sql', { 
        sql_query: `ALTER TABLE public.users ADD COLUMN IF NOT EXISTS theme_settings JSONB DEFAULT '{
          "theme": "default",
          "colors": {
            "background": "#f8fafc",
            "primary": "#3b82f6",
            "secondary": "#f0f2f5",
            "text": "#1c1e21",
            "accent": "#6c5ce7",
            "links": "#3b82f6",
            "borders": "#e2e8f0"
          },
          "fonts": {
            "heading": "Inter",
            "body": "Inter"
          },
          "layout": "standard"
        }'::jsonb;` 
      });
      results.steps.push({ step: 'Added theme_settings column', status: 'success' });
    } catch (error) {
      results.steps.push({ step: 'Adding theme_settings column', status: 'error', details: error.message });
      results.success = false;
    }
    
    // Add custom_css column
    try {
      await supabase.rpc('execute_sql', { 
        sql_query: 'ALTER TABLE public.users ADD COLUMN IF NOT EXISTS custom_css TEXT;' 
      });
      results.steps.push({ step: 'Added custom_css column', status: 'success' });
    } catch (error) {
      results.steps.push({ step: 'Adding custom_css column', status: 'error', details: error.message });
      results.success = false;
    }
    
    // Add profile_song_url column
    try {
      await supabase.rpc('execute_sql', { 
        sql_query: 'ALTER TABLE public.users ADD COLUMN IF NOT EXISTS profile_song_url TEXT;' 
      });
      results.steps.push({ step: 'Added profile_song_url column', status: 'success' });
    } catch (error) {
      results.steps.push({ step: 'Adding profile_song_url column', status: 'error', details: error.message });
      results.success = false;
    }
    
    // Add privacy_settings column
    try {
      await supabase.rpc('execute_sql', { 
        sql_query: `ALTER TABLE public.users ADD COLUMN IF NOT EXISTS privacy_settings JSONB DEFAULT '{
          "profile_visibility": "public",
          "section_visibility": {
            "personal_info": true,
            "contact_details": false,
            "skills": true,
            "projects": true,
            "contribution_details": true,
            "contribution_percentages": false,
            "royalty_info": false,
            "profile_song": true,
            "top_collaborators": true,
            "comments": true,
            "profile_visitors": false
          },
          "verification_display": "level_only"
        }'::jsonb;` 
      });
      results.steps.push({ step: 'Added privacy_settings column', status: 'success' });
    } catch (error) {
      results.steps.push({ step: 'Adding privacy_settings column', status: 'error', details: error.message });
      results.success = false;
    }
    
    // Step 2: Create the increment_profile_views function
    results.steps.push({ step: 'Creating increment_profile_views function', status: 'started' });
    
    try {
      await supabase.rpc('execute_sql', { 
        sql_query: `
        CREATE OR REPLACE FUNCTION increment_profile_views(profile_id UUID)
        RETURNS void
        LANGUAGE plpgsql
        SECURITY DEFINER
        AS $$
        BEGIN
          -- Update the profile_views counter
          UPDATE public.users
          SET profile_views = COALESCE(profile_views, 0) + 1
          WHERE id = profile_id;
        END;
        $$;
        ` 
      });
      results.steps.push({ step: 'Created increment_profile_views function', status: 'success' });
    } catch (error) {
      results.steps.push({ step: 'Creating increment_profile_views function', status: 'error', details: error.message });
      results.success = false;
    }
    
    // Step 3: Create profile_comments table
    results.steps.push({ step: 'Creating profile_comments table', status: 'started' });
    
    try {
      await supabase.rpc('execute_sql', { 
        sql_query: `
        CREATE TABLE IF NOT EXISTS public.profile_comments (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          profile_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
          author_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
          content TEXT NOT NULL,
          is_approved BOOLEAN DEFAULT false,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
        );
        
        -- Create index for faster comment lookups
        CREATE INDEX IF NOT EXISTS idx_profile_comments_profile ON public.profile_comments(profile_id);
        CREATE INDEX IF NOT EXISTS idx_profile_comments_author ON public.profile_comments(author_id);
        
        -- Add RLS policies for profile_comments
        ALTER TABLE public.profile_comments ENABLE ROW LEVEL SECURITY;
        
        -- Anyone can view approved comments
        CREATE POLICY IF NOT EXISTS "Anyone can view approved comments" 
        ON public.profile_comments
        FOR SELECT
        TO authenticated
        USING (is_approved = true OR profile_id = auth.uid() OR author_id = auth.uid());
        
        -- Users can add comments to profiles
        CREATE POLICY IF NOT EXISTS "Users can add comments" 
        ON public.profile_comments
        FOR INSERT
        TO authenticated
        WITH CHECK (true);
        
        -- Users can update their own comments
        CREATE POLICY IF NOT EXISTS "Users can update their own comments" 
        ON public.profile_comments
        FOR UPDATE
        TO authenticated
        USING (author_id = auth.uid());
        
        -- Users can delete their own comments or comments on their profile
        CREATE POLICY IF NOT EXISTS "Users can delete comments" 
        ON public.profile_comments
        FOR DELETE
        TO authenticated
        USING (author_id = auth.uid() OR profile_id = auth.uid());
        ` 
      });
      results.steps.push({ step: 'Created profile_comments table', status: 'success' });
    } catch (error) {
      results.steps.push({ step: 'Creating profile_comments table', status: 'error', details: error.message });
      results.success = false;
    }
    
    // Step 4: Create profile_views table
    results.steps.push({ step: 'Creating profile_views table', status: 'started' });
    
    try {
      await supabase.rpc('execute_sql', { 
        sql_query: `
        CREATE TABLE IF NOT EXISTS public.profile_views (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          profile_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
          viewer_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
          viewed_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
          ip_address TEXT,
          user_agent TEXT
        );
        
        -- Create index for faster profile view lookups
        CREATE INDEX IF NOT EXISTS idx_profile_views_profile ON public.profile_views(profile_id);
        CREATE INDEX IF NOT EXISTS idx_profile_views_viewer ON public.profile_views(viewer_id);
        
        -- Add RLS policies for profile_views
        ALTER TABLE public.profile_views ENABLE ROW LEVEL SECURITY;
        
        -- Users can view profile views on their own profile
        CREATE POLICY IF NOT EXISTS "Users can view their profile views" 
        ON public.profile_views
        FOR SELECT
        TO authenticated
        USING (profile_id = auth.uid());
        
        -- Anyone can insert profile views
        CREATE POLICY IF NOT EXISTS "Anyone can insert profile views" 
        ON public.profile_views
        FOR INSERT
        TO authenticated
        WITH CHECK (true);
        ` 
      });
      results.steps.push({ step: 'Created profile_views table', status: 'success' });
    } catch (error) {
      results.steps.push({ step: 'Creating profile_views table', status: 'error', details: error.message });
      results.success = false;
    }
    
    // Step 5: Create top_collaborators table
    results.steps.push({ step: 'Creating top_collaborators table', status: 'started' });
    
    try {
      await supabase.rpc('execute_sql', { 
        sql_query: `
        CREATE TABLE IF NOT EXISTS public.top_collaborators (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
          collaborator_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
          display_order INTEGER NOT NULL,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
          UNIQUE(user_id, collaborator_id)
        );
        
        -- Create index for faster collaborator lookups
        CREATE INDEX IF NOT EXISTS idx_top_collaborators_user ON public.top_collaborators(user_id);
        
        -- Add RLS policies for top_collaborators
        ALTER TABLE public.top_collaborators ENABLE ROW LEVEL SECURITY;
        
        -- Anyone can view top collaborators
        CREATE POLICY IF NOT EXISTS "Anyone can view top collaborators" 
        ON public.top_collaborators
        FOR SELECT
        TO authenticated
        USING (true);
        
        -- Users can manage their own top collaborators
        CREATE POLICY IF NOT EXISTS "Users can manage their top collaborators" 
        ON public.top_collaborators
        FOR ALL
        TO authenticated
        USING (user_id = auth.uid());
        ` 
      });
      results.steps.push({ step: 'Created top_collaborators table', status: 'success' });
    } catch (error) {
      results.steps.push({ step: 'Creating top_collaborators table', status: 'error', details: error.message });
      results.success = false;
    }
    
    return results;
  } catch (error) {
    results.success = false;
    results.error = error.message;
    return results;
  }
}
