/**
 * Legal Agreement Generator
 *
 * Generates legal contributor agreements for user ventures based on our lawyer-approved
 * template structure. These agreements are between:
 * - The USER'S VENTURE/COMPANY (as "Company")
 * - CONTRIBUTORS they add to their project through Royaltea (as "Contributor")
 *
 * This is NOT an agreement between users and City of Gamers Inc.
 * Our lawyer-approved agreement serves as the structural template.
 */

/**
 * Generate a complete legal contributor agreement for a user's venture
 * @param {Object} projectData - Project data from venture mapping
 * @param {Object} answers - Original venture answers
 * @param {Object} ventureOwnerInfo - Information about the venture owner/company
 * @param {Object} contributorInfo - Information about the contributor being added to the project
 * @returns {string} Complete legal agreement document for venture owner ↔ contributor relationship
 */
export function generateLegalAgreement(projectData, answers, ventureOwnerInfo = {}, contributorInfo = {}) {
  // Ensure we have minimum required data with safe defaults
  const safeProjectData = {
    name: 'Untitled Project',
    description: 'A collaborative project',
    project_type: 'software',
    estimated_duration: 6,
    start_date: new Date(),
    target_audience: 'general',
    max_team_size: 8,
    tags: [],
    royalty_model: {
      model_type: 'contribution_based',
      base_percentage: 0,
      contribution_multiplier: 1,
      minimum_threshold: 0,
      calculation_method: 'dynamic'
    },
    revenue_tranches: [],
    milestones: [],
    contribution_tracking: {
      categories: ['Development', 'Design', 'Testing'],
      tracking_method: 'time_and_impact',
      weight_system: 'balanced'
    },
    ip_ownership: 'shared',
    dispute_resolution: 'mediation',
    agreement_type: 'business',
    notification_frequency: 'weekly',
    ...projectData
  }

  const safeAnswers = {
    ventureName: 'Untitled Venture',
    projectCategory: 'software',
    ...answers
  }

  const safeVentureOwnerInfo = {
    companyName: safeProjectData.name || 'Your Venture Name',
    ownerName: '[Your Name]',
    ownerTitle: 'Founder',
    address: '[Your Address]',
    address2: '[City, State ZIP]',
    address3: '[Country]',
    state: 'Florida', // Default to Florida like our template
    ...ventureOwnerInfo
  }

  const safeContributorInfo = {
    name: '[Contributor Name]',
    isCompany: false,
    address: '[Contributor Address]',
    address2: '[City, State ZIP]',
    address3: '[Country]',
    ...contributorInfo
  }

  const currentDate = new Date()
  const formattedDate = formatDate(currentDate)
  
  return `# ${safeVentureOwnerInfo.companyName.toUpperCase()}
# CONTRIBUTOR AGREEMENT

This Contributor Agreement (this "Agreement") is effective as of ${formattedDate}, by and between ${safeVentureOwnerInfo.companyName}, a ${safeVentureOwnerInfo.state} ${safeVentureOwnerInfo.entityType || 'company'} with its principal place of business at ${safeVentureOwnerInfo.address}, ${safeVentureOwnerInfo.address2} (the "Company") and ${safeContributorInfo.name || '[_________________________]'} (the "Contributor").

## Recitals

WHEREAS, the Company desires to procure services of the Contributor, and the Contributor is willing to provide services to the Company, specifically as provided on Schedule A to this Agreement (the "Services") for the consideration as provided on Schedule B to this Agreement (the "Consideration"), and the Company has asked the Contributor to enter into this Agreement as a part of such arrangement; 

WHEREAS, the Contributor shall provide the Services to the Company as an independent contractor on the terms set forth in this Agreement; 

WHEREAS, Contributor to the best of his or her knowledge is not legally obligated, whether by entering into an agreement or otherwise, in any way that conflicts with the terms of this Agreement, nor violates any third party's Intellectual Property Rights; and

WHEREAS, the Contributor acknowledges he or she will have access to certain confidential information which is vital to the success of the Company's business, and that the Company has a legitimate business interest in protecting such information.

NOW THEREFORE, in consideration of the foregoing, the agreements set forth below regarding the parties' desire to preserve the value inherent in the Company for their mutual benefit, and for other good and valuable consideration (the receipt of which the Contributor hereby acknowledges), the Contributor, intending to be legally bound hereby, agrees with the Company as follows:

## 1. Definitions. 

The terms below shall have the following meanings when used throughout this Agreement:

(a) **"Background IP"** means the Intellectual Property Rights owned by or licensed to a Party at the Effective Date, and any modifications or improvements thereto.

(b) **"Confidential Documents"** shall mean and include all files, letters, memoranda, reports, records, computer disks or other computer storage medium, data, models, or any photographic or other tangible materials containing Confidential Information (as hereinafter defined), whether created by the Company, its clients, the Contributor or any other party, and all copies, excerpts and summaries thereof which shall come into the custody or possession of the Contributor.

(c) **"Confidential Information"** shall mean and include all information, whether written or oral, tangible or intangible, of a private, secret, proprietary or confidential nature, of or concerning the Company, its clients and its business and operations, including without limitation, any trade secrets or know how, computer software programs in both source code and object code form (including, without limitation, Programs (as hereinafter defined)) and any rights relating thereto, information relating to any product (whether actual or proposed), development (including any improvement, advancement or modification thereto), technology, technique, process or methodology, any sales, promotional or marketing plans, programs, techniques, practices or strategies, any expansion plans (including existing and entry into new geographic and/or product markets), any operational and management guidelines, any corporate and commercial policies, any cost, pricing or other financial data or projections, client lists, the identity and background of any customers, prospects or suppliers, and any other information which is to be treated as confidential because of any duty of confidentiality owed by the Company to a third party, or any other information that the Company shall, in the ordinary course of business, possess or use and not release externally without restriction on use or disclosure.

Confidential Information shall also include "Work Product" as hereinafter defined.

Notwithstanding the foregoing, Confidential Information shall not include any information that (i) was known by the Contributor before disclosure by or on behalf of the Company, (ii) becomes available to the Contributor from a source other than the Company that is not bound by a duty of confidentiality to the Company, (iii) becomes generally available or known in the industry other than as a result of its disclosure by the Contributor, or (iv) has been independently developed by the Contributor; provided, in each case, that the Contributor shall bear the burden of demonstrating that the information falls under one of the above-described exceptions.

(d) **"Contribution"** means any original Work Product, including any modification of or addition to an existing work product that the Contributor submits to the Company; and by doing so the Contributor agrees such Contribution shall be the sole and exclusive property of the Company.

(e) **"Developed IP"** means the intellectual property rights created by or on behalf of Contributor in the course of performing the Services, including all intellectual property in and to the Work Product (which is not Background IP);

(f) **"Governmental Authority"** means any foreign, domestic, federal, territorial, state or local governmental authority, quasi-governmental authority, instrumentality, court, quasi-judicial authority, arbitrator (public or private), government or self-regulatory organization, governmental commission, tribunal or organization, or any regulatory, administrative or other agency, or any political or other subdivision, department or branch, of any of the foregoing.

(g) **"Launch"** means the date on which the Work Product will be fully and publicly available to end users;

(h) **"Milestones"** means those milestones for the Work Product and deadlines for completion of certain elements of the Work Product as set out in Exhibit II;

(i) **"Person"** means an individual, corporation, partnership, joint venture, limited liability company, Governmental Authority, unincorporated organization, trust, association or other entity.

(j) **"Programs"** shall mean certain ideas, routines, object and source codes, specifications, flowcharts, development and coding of video games, software, web sites, and any other material and documentation, together with all information data and know-how, alterations corrections, improvements and upgrades thereto.

(k) **"Specification"** means the specification of the Work Product as set out in Exhibit I;

(l) **"Termination Date"** shall mean the date the Contributor ceases to provide Services to the Company.

(m) **"Work Product"** shall mean and include any and all products, designs, works, original works, discoveries, inventions and improvements and other results of the Contributor's engagement with the Company (including, without limitation, any Programs), that may be conceived, developed, produced, prepared, created or contributed to (whether at the Company's premises or elsewhere) by the Contributor, acting alone or with others, during the period of his or her engagement by the Company (or at any time after the termination of the Contributor's engagement by the Company if derived from, based upon or relating to any Confidential Information).

(n) **"Work Product Management"** means defining what features to develop and what to change in the Work Product in order to achieve success, defining the roadmap, prioritizing what is relevant, and analyzing qualitative and quantitative data to support decision making.

(o) **"Revenue Tranche"** means a designated portion of Revenue that is allocated for distribution to Contributors based on predefined criteria, including time periods and revenue thresholds, as further detailed in Schedule B.

(p) **"Contribution Points"** means the numerical value assigned to Contributor's Contributions based on factors including time committed, task complexity, and overall impact on the Work Product, as further detailed in Schedule B.

${generateStandardLegalSections()}

${generateScheduleA(safeProjectData, safeAnswers)}

${generateScheduleB(safeProjectData, safeAnswers)}

${generateExhibitI(safeProjectData, safeAnswers)}

${generateExhibitII(safeProjectData, safeAnswers)}

IN WITNESS WHEREOF, this Agreement has been executed by the parties as of the date set forth above.

**COMPANY:**

${safeVentureOwnerInfo.companyName}

By: ______________________
Name: ${safeVentureOwnerInfo.ownerName}
Title: ${safeVentureOwnerInfo.ownerTitle}
Date: ______________________

**CONTRIBUTOR:**

${safeContributorInfo.isCompany ? generateCompanySignature(safeContributorInfo) : generateIndividualSignature(safeContributorInfo)}`
}

/**
 * Format date for legal documents
 */
function formatDate(date) {
  const months = ['January', 'February', 'March', 'April', 'May', 'June',
                  'July', 'August', 'September', 'October', 'November', 'December']
  return `${months[date.getMonth()]} ${date.getDate()}, ${date.getFullYear()}`
}

/**
 * Generate standard legal sections (2-21) from the template
 */
function generateStandardLegalSections() {
  return `
## 2. Treatment of Confidential Information.

The Contributor acknowledges that during the course of providing Services, the Contributor may have access to Confidential Information. The Contributor agrees to hold all Confidential Information in strict confidence and not to disclose such information to any third party without the prior written consent of the Company. The Contributor shall use Confidential Information solely for the purpose of performing Services under this Agreement.

## 3. Ownership of Work Product.

All Work Product shall be the sole and exclusive property of the Company. The Contributor hereby assigns to the Company all right, title, and interest in and to the Work Product, including all intellectual property rights therein. The Contributor agrees to execute any documents necessary to perfect the Company's ownership of the Work Product.

## 4. Non-Disparagement.

The Contributor agrees not to make any disparaging statements about the Company, its products, services, or personnel, whether written or oral, during the term of this Agreement and for a period of two (2) years thereafter.

## 5. Termination.

This Agreement may be terminated by either party upon thirty (30) days written notice. Upon termination, the Contributor shall immediately return all Confidential Information and Work Product to the Company.

## 6. Equitable Remedies.

The Contributor acknowledges that any breach of this Agreement would cause irreparable harm to the Company for which monetary damages would be inadequate. Therefore, the Company shall be entitled to seek equitable relief, including injunction and specific performance.

## 7. Assignment.

This Agreement may not be assigned by the Contributor without the prior written consent of the Company. The Company may assign this Agreement to any successor or affiliate.

## 8. Waivers and Amendments.

No waiver or amendment of this Agreement shall be effective unless in writing and signed by both parties. No waiver of any breach shall constitute a waiver of any subsequent breach.

## 9. Survival.

The provisions of this Agreement relating to confidentiality, ownership of Work Product, and non-disparagement shall survive termination of this Agreement.

## 10. Status as Independent Contractor.

The Contributor is an independent contractor and not an employee of the Company. The Contributor shall be responsible for all taxes and benefits related to compensation received under this Agreement.

## 11. Representations and Warranties.

The Contributor represents and warrants that: (a) the Contributor has the right and authority to enter into this Agreement; (b) the performance of Services will not violate any other agreement to which the Contributor is bound; and (c) the Work Product will be original and will not infringe any third party rights.

## 12. Indemnification.

The Contributor agrees to indemnify and hold harmless the Company from any claims, damages, or expenses arising from the Contributor's breach of this Agreement or negligent performance of Services.

## 13. Entire Agreement.

This Agreement constitutes the entire agreement between the parties and supersedes all prior negotiations, representations, or agreements relating to the subject matter hereof.

## 14. Governing Law.

This Agreement shall be governed by and construed in accordance with the laws of the State of Florida, without regard to conflict of law principles.

## 15. Consent to Jurisdiction.

The parties consent to the exclusive jurisdiction of the courts of Orange County, Florida for any disputes arising under this Agreement.

## 16. Settlement of Disputes.

Any disputes arising under this Agreement shall first be subject to good faith negotiations. If such negotiations fail, disputes shall be resolved through binding arbitration in accordance with the rules of the American Arbitration Association.

## 17. Titles and Subtitles.

The titles and subtitles of this Agreement are for convenience only and shall not affect the interpretation of any provision.

## 18. Opportunity to Consult.

The Contributor acknowledges having had the opportunity to consult with legal counsel regarding this Agreement and understands its terms and conditions.

## 19. Gender; Singular and Plural.

References to any gender shall include all genders, and references to the singular shall include the plural and vice versa.

## 20. Notice.

All notices under this Agreement shall be in writing and delivered to the addresses set forth in the signature blocks below.

## 21. Counterparts.

This Agreement may be executed in counterparts, each of which shall be deemed an original and all of which together shall constitute one and the same instrument.`
}

/**
 * Generate Schedule A - Description of Services
 */
function generateScheduleA(projectData, answers) {
  return `
## SCHEDULE A
### Description of Services

This project involves development work on "${projectData.name}," ${projectData.description.toLowerCase()}.

1. **Services**

   a. **General.** Pursuant to the terms and conditions of this Agreement and subject to Company's acceptance, Contributor shall:
      i. develop the Work Product following the requirements and technical specifications set forth in Exhibit I and in accordance with the roadmap set forth in Exhibit II and Good Industry Practice ("Developing Services"); and
      ii. provide the Support Services in accordance with Good Industry Practice.

   b. **Performance.** Contributor understands and agrees that Contributor is solely responsible for the control and supervision of the means by which the Services are provided consistent with the goal of successfully completing the Services on time. Contributor shall allocate sufficient resources to ensure that it performs its Services under this Agreement in accordance with the roadmap in Exhibit II and in accordance with Good Industry Practice.

   c. **Co-operation.**
      i. During the period from the Effective Date until Launch, unless directed otherwise by Company, Contributor shall attend at least weekly calls with Company, with frequency increasing during crunch periods as needed, and provide builds (application versions) every two (2) weeks with Company.
      ii. The Parties shall share responsibility for Work Product Management as agreed between the Parties from time to time and as outlined in Exhibit II. If the Parties are unable to reach agreement in respect of a decision in relation to Work Product Management, Company's decision shall prevail.
      iii. If there is additional need for development that is not in the agreed roadmap the Parties will negotiate in good faith the timeline for Contributor to deliver such further development. The Parties will also negotiate in good faith the costs to deliver such further development.

2. **Project Type:** ${projectData.project_type}
3. **Estimated Duration:** ${projectData.estimated_duration} months
4. **Target Audience:** ${projectData.target_audience}
5. **Team Size Limit:** ${projectData.max_team_size} contributors`
}

/**
 * Generate Schedule B - Consideration and Revenue Model
 */
function generateScheduleB(projectData, answers) {
  const royaltyModel = projectData.royalty_model
  const revenueTranches = projectData.revenue_tranches

  return `
## SCHEDULE B
### Consideration

1. **Revenue Sharing Model**

   The Contributor's compensation shall be based on a **${royaltyModel.model_type.replace('_', ' ')}** model with the following terms:

   - **Base Percentage:** ${royaltyModel.base_percentage}%
   - **Contribution Multiplier:** ${royaltyModel.contribution_multiplier}x
   - **Minimum Threshold:** $${royaltyModel.minimum_threshold}
   - **Calculation Method:** ${royaltyModel.calculation_method}

2. **Revenue Tranches**

   Revenue sharing shall be distributed according to the following tranches:

${revenueTranches.map((tranche, index) => `   **Tranche ${index + 1}: ${tranche.name}**
   - Revenue Range: $${tranche.start_amount}${tranche.end_amount ? ` - $${tranche.end_amount}` : '+'}
   - Contributor Share: ${tranche.percentage}%
   - Description: ${tranche.description}`).join('\n\n')}

3. **Contribution Point System**

   Contribution Points shall be calculated based on:

${projectData.contribution_tracking.categories.map(category => `   - **${category}:** Points awarded based on time commitment, complexity, and impact`).join('\n')}

   - **Tracking Method:** ${projectData.contribution_tracking.tracking_method}
   - **Weight System:** ${projectData.contribution_tracking.weight_system}

4. **Payment Terms**

   - **Payment Frequency:** ${projectData.notification_frequency === 'daily' ? 'Weekly' : 'Monthly'}
   - **Payment Method:** Wire transfer to Contributor's designated account
   - **Minimum Payout:** $${royaltyModel.minimum_threshold}
   - **Currency:** USD

5. **Buy Out Provision**

   Following the 3rd anniversary of this Agreement, the Company may terminate this Agreement on thirty (30) days' written notice to Contributor, subject to the payment of a Buy Out Fee calculated as follows:

   - **Buy Out Fee:** [To be calculated based on average monthly revenue over preceding 12 months × ${royaltyModel.contribution_multiplier}]`
}

/**
 * Generate Exhibit I - Technical Specifications
 */
function generateExhibitI(projectData, answers) {
  return `
## EXHIBIT I
### Technical Specifications

**Project Name:** ${projectData.name}

**Project Description:** ${projectData.description}

**Technical Requirements:**

1. **Project Type:** ${projectData.project_type}
2. **Target Platform:** ${getTargetPlatform(projectData.project_type, answers)}
3. **Technology Stack:** ${getTechnologyStack(projectData.project_type)}
4. **Performance Requirements:** ${getPerformanceRequirements(projectData.project_type)}

**Functional Requirements:**

${projectData.tags.map(tag => `- ${tag.charAt(0).toUpperCase() + tag.slice(1)} functionality`).join('\n')}

**Quality Standards:**

- Code must follow industry best practices
- All features must be thoroughly tested
- Documentation must be provided for all major components
- Security requirements must be met according to industry standards

**Deliverables:**

- Source code with appropriate version control
- Technical documentation
- User documentation (if applicable)
- Test cases and results
- Deployment instructions

**Acceptance Criteria:**

- All Milestones completed as specified in Exhibit II
- Code passes all quality assurance tests
- Performance meets specified requirements
- Security audit completed (if applicable)`
}

/**
 * Generate Exhibit II - Project Roadmap and Milestones
 */
function generateExhibitII(projectData, answers) {
  return `
## EXHIBIT II
### Project Roadmap and Milestones

**Project Duration:** ${projectData.estimated_duration} months
**Start Date:** ${projectData.start_date.toDateString()}
**Target Launch Date:** ${new Date(projectData.start_date.getTime() + projectData.estimated_duration * 30 * 24 * 60 * 60 * 1000).toDateString()}

**Milestones:**

${projectData.milestones.map((milestone, index) => `
**Milestone ${index + 1}: ${milestone.name}**
- **Target Date:** ${milestone.target_date.toDateString()}
- **Progress Target:** ${milestone.percentage}%
- **Required:** ${milestone.is_required ? 'Yes' : 'No'}
- **Description:** ${milestone.description}
- **Deliverables:** [To be specified based on milestone requirements]
- **Acceptance Criteria:** [To be defined for each milestone]`).join('\n')}

**Work Product Management Responsibilities:**

- **Company Responsibilities:**
  - Define overall product vision and strategy
  - Provide business requirements and priorities
  - Conduct milestone reviews and approvals
  - Manage stakeholder communications

- **Contributor Responsibilities:**
  - Execute technical development according to specifications
  - Provide regular progress updates
  - Participate in milestone reviews
  - Deliver quality work products on schedule

**Communication Schedule:**

- **Weekly Meetings:** Every ${projectData.notification_frequency === 'daily' ? 'Monday' : 'Friday'}
- **Milestone Reviews:** At completion of each milestone
- **Progress Reports:** ${projectData.notification_frequency === 'daily' ? 'Daily' : 'Weekly'}
- **Emergency Contact:** As needed for critical issues`
}

/**
 * Generate individual contributor signature block
 */
function generateIndividualSignature(contributorInfo) {
  return `
[If an individual]

Name: ${contributorInfo.name || '_______________________'}
Date: ${contributorInfo.date || '________________________'}
Address: ${contributorInfo.address || '_____________________'}
${contributorInfo.address2 || '______________________________'}
${contributorInfo.address3 || '______________________________'}`
}

/**
 * Generate company contributor signature block
 */
function generateCompanySignature(contributorInfo) {
  return `
[If a company]

${contributorInfo.companyName || '[Name of Company]'}

By: ${contributorInfo.signatory || '_________________________'}
Name: ${contributorInfo.signatoryName || '______________________'}
Title: ${contributorInfo.signatoryTitle || '_______________________'}
Date: ${contributorInfo.date || '_______________________'}
Address: ${contributorInfo.address || '____________________'}
${contributorInfo.address2 || '_____________________________'}
${contributorInfo.address3 || '_____________________________'}`
}

/**
 * Helper functions for technical specifications
 */
function getTargetPlatform(projectType, answers) {
  const platformMap = {
    software: answers.ventureTags?.includes('mobile') ? 'Mobile (iOS/Android)' : 'Web Application',
    creative: 'Multi-platform Distribution',
    business: 'Web-based Platform',
    physical: 'Physical Product'
  }
  return platformMap[projectType] || 'To be determined'
}

function getTechnologyStack(projectType) {
  const stackMap = {
    software: 'Modern web technologies (React, Node.js, etc.)',
    creative: 'Industry-standard creative tools and platforms',
    business: 'Business application stack',
    physical: 'Manufacturing and design tools'
  }
  return stackMap[projectType] || 'To be determined'
}

function getPerformanceRequirements(projectType) {
  const perfMap = {
    software: 'Fast loading times, responsive design, scalable architecture',
    creative: 'High-quality output, efficient workflow, professional standards',
    business: 'Reliable operation, data security, user-friendly interface',
    physical: 'Quality materials, precise manufacturing, safety standards'
  }
  return perfMap[projectType] || 'Industry standard performance'
}
