// Commission Management API
// Backend Specialist: Automated commission calculation and payment processing
const { createClient } = require('@supabase/supabase-js');

// Supabase client
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_KEY
);

// Helper function to get user from request
const getUserFromRequest = (event) => {
  const authHeader = event.headers.authorization;
  if (!authHeader) return null;
  
  try {
    const token = authHeader.replace('Bearer ', '');
    const payload = JSON.parse(Buffer.from(token.split('.')[1], 'base64').toString());
    return payload.sub;
  } catch {
    return null;
  }
};

// Helper function to check finance admin permissions
const checkFinancePermission = async (userId) => {
  const { data: user } = await supabase
    .from('users')
    .select('role')
    .eq('id', userId)
    .single();
  
  return user && ['admin', 'finance_admin'].includes(user.role);
};

// Helper function to calculate commission based on schedule
const calculateCommission = (baseAmount, schedule) => {
  const { commission_type, base_rate, fixed_amount, tier_config } = schedule;
  
  switch (commission_type) {
    case 'percentage':
      return {
        amount: (baseAmount * (base_rate / 100)),
        method: 'percentage',
        rate: base_rate,
        breakdown: { base_amount: baseAmount, rate: base_rate }
      };
      
    case 'fixed':
      return {
        amount: fixed_amount,
        method: 'fixed',
        rate: null,
        breakdown: { fixed_amount }
      };
      
    case 'tiered':
      let totalCommission = 0;
      let remainingAmount = baseAmount;
      const tierBreakdown = [];
      
      for (const tier of tier_config || []) {
        if (remainingAmount <= 0) break;
        
        const tierAmount = Math.min(remainingAmount, tier.max_amount - tier.min_amount);
        const tierCommission = tierAmount * (tier.rate / 100);
        
        totalCommission += tierCommission;
        remainingAmount -= tierAmount;
        
        tierBreakdown.push({
          tier: tier.name,
          amount: tierAmount,
          rate: tier.rate,
          commission: tierCommission
        });
      }
      
      return {
        amount: totalCommission,
        method: 'tiered',
        rate: null,
        breakdown: { tiers: tierBreakdown, total_base: baseAmount }
      };
      
    default:
      return { amount: 0, method: 'unknown', rate: null, breakdown: {} };
  }
};

// Create Commission Schedule
const createCommissionSchedule = async (event) => {
  try {
    const userId = getUserFromRequest(event);
    if (!userId) {
      return {
        statusCode: 401,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    // Check finance permissions
    const hasPermission = await checkFinancePermission(userId);
    if (!hasPermission) {
      return {
        statusCode: 403,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Insufficient permissions' })
      };
    }

    const data = JSON.parse(event.body);
    
    // Validate required fields
    if (!data.name || !data.commission_type || !data.applies_to) {
      return {
        statusCode: 400,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          error: 'Name, commission_type, and applies_to are required' 
        })
      };
    }

    // Validate commission configuration
    if (data.commission_type === 'percentage' && (!data.base_rate || data.base_rate < 0 || data.base_rate > 100)) {
      return {
        statusCode: 400,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Valid base_rate (0-100) required for percentage commission' })
      };
    }

    if (data.commission_type === 'fixed' && (!data.fixed_amount || data.fixed_amount < 0)) {
      return {
        statusCode: 400,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Valid fixed_amount required for fixed commission' })
      };
    }

    // Create commission schedule
    const scheduleData = {
      name: data.name,
      description: data.description || null,
      commission_type: data.commission_type,
      base_rate: data.base_rate || null,
      fixed_amount: data.fixed_amount || null,
      tier_config: data.tier_config || [],
      applies_to: data.applies_to,
      product_categories: data.product_categories || [],
      client_types: data.client_types || [],
      alliance_types: data.alliance_types || [],
      effective_date: data.effective_date || new Date().toISOString().split('T')[0],
      expiration_date: data.expiration_date || null,
      minimum_sale_amount: data.minimum_sale_amount || 0,
      maximum_commission_amount: data.maximum_commission_amount || null,
      payment_frequency: data.payment_frequency || 'monthly',
      payment_delay_days: data.payment_delay_days || 0,
      is_active: data.is_active !== undefined ? data.is_active : true,
      auto_calculate: data.auto_calculate !== undefined ? data.auto_calculate : true,
      requires_approval: data.requires_approval !== undefined ? data.requires_approval : false,
      created_by: userId
    };

    const { data: schedule, error: scheduleError } = await supabase
      .from('commission_schedules')
      .insert([scheduleData])
      .select()
      .single();

    if (scheduleError) {
      throw new Error(`Failed to create commission schedule: ${scheduleError.message}`);
    }

    return {
      statusCode: 201,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ schedule })
    };

  } catch (error) {
    console.error('Create commission schedule error:', error);
    return {
      statusCode: 500,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ error: 'Failed to create commission schedule' })
    };
  }
};

// Calculate Commission for Revenue
const calculateCommissionForRevenue = async (event) => {
  try {
    const userId = getUserFromRequest(event);
    if (!userId) {
      return {
        statusCode: 401,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    const data = JSON.parse(event.body);
    
    // Validate required fields
    if (!data.revenue_amount || !data.sales_rep_id) {
      return {
        statusCode: 400,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          error: 'revenue_amount and sales_rep_id are required' 
        })
      };
    }

    const revenueAmount = parseFloat(data.revenue_amount);
    if (isNaN(revenueAmount) || revenueAmount <= 0) {
      return {
        statusCode: 400,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Valid revenue_amount required' })
      };
    }

    // Get applicable commission schedules
    let query = supabase
      .from('commission_schedules')
      .select('*')
      .eq('is_active', true)
      .lte('effective_date', new Date().toISOString().split('T')[0])
      .or('expiration_date.is.null,expiration_date.gte.' + new Date().toISOString().split('T')[0])
      .lte('minimum_sale_amount', revenueAmount);

    // Apply filters based on context
    if (data.product_category) {
      query = query.or(`applies_to.eq.all_sales,product_categories.cs.{${data.product_category}}`);
    }

    if (data.client_type) {
      query = query.or(`applies_to.eq.all_sales,client_types.cs.{${data.client_type}}`);
    }

    if (data.alliance_type) {
      query = query.or(`applies_to.eq.all_sales,alliance_types.cs.{${data.alliance_type}}`);
    }

    const { data: schedules, error: schedulesError } = await query.order('effective_date', { ascending: false });

    if (schedulesError) {
      throw new Error(`Failed to fetch commission schedules: ${schedulesError.message}`);
    }

    if (!schedules || schedules.length === 0) {
      return {
        statusCode: 200,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          calculations: [],
          total_commission: 0,
          message: 'No applicable commission schedules found'
        })
      };
    }

    // Calculate commissions for each applicable schedule
    const calculations = [];
    let totalCommission = 0;

    for (const schedule of schedules) {
      const calculation = calculateCommission(revenueAmount, schedule);
      
      // Apply maximum commission limit if set
      if (schedule.maximum_commission_amount && calculation.amount > schedule.maximum_commission_amount) {
        calculation.amount = schedule.maximum_commission_amount;
        calculation.capped = true;
      }

      const calculationData = {
        commission_schedule_id: schedule.id,
        revenue_transaction_id: data.revenue_transaction_id || null,
        sales_rep_id: data.sales_rep_id,
        base_amount: revenueAmount,
        commission_rate: calculation.rate,
        calculated_amount: calculation.amount,
        calculation_method: calculation.method,
        tier_breakdown: calculation.breakdown,
        status: schedule.requires_approval ? 'calculated' : 'approved'
      };

      // Save calculation if requested
      if (data.save_calculation) {
        const { data: savedCalculation, error: calcError } = await supabase
          .from('commission_calculations')
          .insert([calculationData])
          .select()
          .single();

        if (calcError) {
          console.error('Failed to save commission calculation:', calcError);
        } else {
          calculationData.id = savedCalculation.id;
          calculationData.created_at = savedCalculation.created_at;
        }
      }

      calculations.push({
        schedule: {
          id: schedule.id,
          name: schedule.name,
          commission_type: schedule.commission_type,
          payment_frequency: schedule.payment_frequency
        },
        calculation: calculationData
      });

      totalCommission += calculation.amount;
    }

    return {
      statusCode: 200,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        calculations,
        total_commission: totalCommission,
        revenue_amount: revenueAmount,
        sales_rep_id: data.sales_rep_id
      })
    };

  } catch (error) {
    console.error('Calculate commission error:', error);
    return {
      statusCode: 500,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ error: 'Failed to calculate commission' })
    };
  }
};

// Get Commission Dashboard
const getCommissionDashboard = async (event) => {
  try {
    const userId = getUserFromRequest(event);
    if (!userId) {
      return {
        statusCode: 401,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    const queryParams = new URLSearchParams(event.queryStringParameters || {});
    const salesRepId = queryParams.get('sales_rep_id') || userId;
    const period = queryParams.get('period') || 'monthly'; // monthly, quarterly, yearly
    const status = queryParams.get('status'); // calculated, approved, paid

    // Check if user can view this data
    const isFinanceAdmin = await checkFinancePermission(userId);
    if (!isFinanceAdmin && salesRepId !== userId) {
      return {
        statusCode: 403,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Can only view your own commission data' })
      };
    }

    // Build query for commission calculations
    let query = supabase
      .from('commission_calculations')
      .select(`
        id,
        base_amount,
        commission_rate,
        calculated_amount,
        calculation_method,
        status,
        calculation_date,
        approval_date,
        payment_date,
        commission_schedule:commission_schedules(
          id,
          name,
          commission_type,
          payment_frequency
        ),
        sales_rep:users!commission_calculations_sales_rep_id_fkey(
          id,
          display_name,
          email
        )
      `)
      .eq('sales_rep_id', salesRepId)
      .order('calculation_date', { ascending: false });

    // Apply status filter
    if (status) {
      query = query.eq('status', status);
    }

    // Apply date filter based on period
    const now = new Date();
    let startDate;
    
    switch (period) {
      case 'monthly':
        startDate = new Date(now.getFullYear(), now.getMonth(), 1);
        break;
      case 'quarterly':
        const quarter = Math.floor(now.getMonth() / 3);
        startDate = new Date(now.getFullYear(), quarter * 3, 1);
        break;
      case 'yearly':
        startDate = new Date(now.getFullYear(), 0, 1);
        break;
      default:
        startDate = new Date(now.getFullYear(), now.getMonth(), 1);
    }

    query = query.gte('calculation_date', startDate.toISOString());

    const { data: calculations, error: calculationsError } = await query.limit(100);

    if (calculationsError) {
      throw new Error(`Failed to fetch commission calculations: ${calculationsError.message}`);
    }

    // Calculate summary statistics
    const summary = {
      total_calculations: calculations?.length || 0,
      total_commission_calculated: calculations?.reduce((sum, calc) => sum + (calc.calculated_amount || 0), 0) || 0,
      total_commission_approved: calculations?.filter(c => ['approved', 'paid'].includes(c.status))
        .reduce((sum, calc) => sum + (calc.calculated_amount || 0), 0) || 0,
      total_commission_paid: calculations?.filter(c => c.status === 'paid')
        .reduce((sum, calc) => sum + (calc.calculated_amount || 0), 0) || 0,
      pending_approval: calculations?.filter(c => c.status === 'calculated').length || 0,
      pending_payment: calculations?.filter(c => c.status === 'approved').length || 0
    };

    // Calculate period analysis
    const periodAnalysis = {};
    calculations?.forEach(calc => {
      const date = new Date(calc.calculation_date);
      const key = period === 'monthly' ? 
        `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}` :
        period === 'quarterly' ?
        `${date.getFullYear()}-Q${Math.floor(date.getMonth() / 3) + 1}` :
        `${date.getFullYear()}`;

      if (!periodAnalysis[key]) {
        periodAnalysis[key] = { calculations: 0, total_amount: 0, paid_amount: 0 };
      }
      periodAnalysis[key].calculations += 1;
      periodAnalysis[key].total_amount += calc.calculated_amount || 0;
      if (calc.status === 'paid') {
        periodAnalysis[key].paid_amount += calc.calculated_amount || 0;
      }
    });

    return {
      statusCode: 200,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        calculations: calculations || [],
        summary,
        period_analysis: periodAnalysis,
        period,
        sales_rep_id: salesRepId
      })
    };

  } catch (error) {
    console.error('Get commission dashboard error:', error);
    return {
      statusCode: 500,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ error: 'Failed to fetch commission dashboard' })
    };
  }
};

// Route requests to appropriate handlers
exports.handler = async (event, context) => {
  // Add CORS headers
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS'
  };

  // Handle preflight requests
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers,
      body: ''
    };
  }

  const path = event.path.replace('/.netlify/functions/commission-management', '');

  try {
    let response;

    if (event.httpMethod === 'GET') {
      if (path === '/dashboard' || path === '/dashboard/') {
        response = await getCommissionDashboard(event);
      } else {
        response = {
          statusCode: 404,
          body: JSON.stringify({ error: 'Endpoint not found' })
        };
      }
    } else if (event.httpMethod === 'POST') {
      if (path === '/schedules' || path === '/schedules/') {
        response = await createCommissionSchedule(event);
      } else if (path === '/calculate' || path === '/calculate/') {
        response = await calculateCommissionForRevenue(event);
      } else {
        response = {
          statusCode: 404,
          body: JSON.stringify({ error: 'Endpoint not found' })
        };
      }
    } else {
      response = {
        statusCode: 405,
        body: JSON.stringify({ error: 'Method not allowed' })
      };
    }

    // Add CORS headers to response
    response.headers = { ...response.headers, ...headers };
    return response;

  } catch (error) {
    console.error('Commission Management API error:', error);
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ error: 'Internal server error' })
    };
  }
};
