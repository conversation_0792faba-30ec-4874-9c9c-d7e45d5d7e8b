// Endorsement System API
// Backend Specialist: Peer endorsement and skill validation system
const { createClient } = require('@supabase/supabase-js');

// Supabase client
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_KEY
);

// Helper function to get user from request
const getUserFromRequest = (event) => {
  const authHeader = event.headers.authorization;
  if (!authHeader) return null;
  
  try {
    const token = authHeader.replace('Bearer ', '');
    const payload = JSON.parse(Buffer.from(token.split('.')[1], 'base64').toString());
    return payload.sub;
  } catch {
    return null;
  }
};

// Create Endorsement
const createEndorsement = async (event) => {
  try {
    const userId = getUserFromRequest(event);
    if (!userId) {
      return {
        statusCode: 401,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    const data = JSON.parse(event.body);
    
    if (!data.endorsed_user_id || !data.endorsement_type) {
      return {
        statusCode: 400,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          error: 'endorsed_user_id and endorsement_type are required' 
        })
      };
    }

    // Prevent self-endorsement
    if (data.endorsed_user_id === userId) {
      return {
        statusCode: 400,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Cannot endorse yourself' })
      };
    }

    // Validate endorsement type
    const validTypes = ['skill', 'collaboration', 'leadership', 'reliability', 'innovation', 'mentorship'];
    if (!validTypes.includes(data.endorsement_type)) {
      return {
        statusCode: 400,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Invalid endorsement type' })
      };
    }

    // Check if users are allies (required for endorsements)
    const { data: allyConnection } = await supabase
      .from('user_allies')
      .select('id')
      .or(`and(user_id.eq.${userId},ally_id.eq.${data.endorsed_user_id}),and(user_id.eq.${data.endorsed_user_id},ally_id.eq.${userId})`)
      .eq('status', 'accepted')
      .single();

    if (!allyConnection) {
      return {
        statusCode: 403,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Can only endorse allies. Connect first.' })
      };
    }

    // Check for existing endorsement
    const { data: existingEndorsement } = await supabase
      .from('user_endorsements')
      .select('id')
      .eq('endorser_id', userId)
      .eq('endorsed_user_id', data.endorsed_user_id)
      .eq('endorsement_type', data.endorsement_type)
      .eq('skill_id', data.skill_id || null)
      .eq('skill_name', data.skill_name || null)
      .single();

    if (existingEndorsement) {
      return {
        statusCode: 400,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Endorsement already exists' })
      };
    }

    // Calculate credibility score based on endorser's reputation
    const credibilityScore = await calculateEndorserCredibility(userId);

    // Create endorsement
    const endorsementData = {
      endorser_id: userId,
      endorsed_user_id: data.endorsed_user_id,
      endorsement_type: data.endorsement_type,
      skill_id: data.skill_id || null,
      skill_name: data.skill_name || null,
      endorsement_level: data.endorsement_level || 3,
      endorsement_title: data.endorsement_title || null,
      endorsement_message: data.endorsement_message || null,
      project_context: data.project_context || null,
      collaboration_duration: data.collaboration_duration || null,
      evidence_urls: data.evidence_urls || null,
      credibility_score: credibilityScore,
      is_public: data.is_public !== false // Default to true
    };

    const { data: endorsement, error: endorsementError } = await supabase
      .from('user_endorsements')
      .insert([endorsementData])
      .select(`
        id,
        endorsement_type,
        skill_name,
        endorsement_level,
        endorsement_title,
        endorsement_message,
        project_context,
        collaboration_duration,
        credibility_score,
        is_public,
        created_at,
        endorser:users!user_endorsements_endorser_id_fkey(
          id,
          display_name,
          avatar_url
        ),
        endorsed_user:users!user_endorsements_endorsed_user_id_fkey(
          id,
          display_name,
          avatar_url
        )
      `)
      .single();

    if (endorsementError) {
      throw new Error(`Failed to create endorsement: ${endorsementError.message}`);
    }

    // Create activity feed entry
    await createEndorsementActivity(endorsement);

    return {
      statusCode: 201,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ endorsement })
    };

  } catch (error) {
    console.error('Create endorsement error:', error);
    return {
      statusCode: 500,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ error: 'Failed to create endorsement' })
    };
  }
};

// Get User Endorsements
const getUserEndorsements = async (event) => {
  try {
    const userId = getUserFromRequest(event);
    if (!userId) {
      return {
        statusCode: 401,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    const queryParams = new URLSearchParams(event.queryStringParameters || {});
    const targetUserId = queryParams.get('user_id') || userId;
    const endorsementType = queryParams.get('type');
    const skillName = queryParams.get('skill');
    const verifiedOnly = queryParams.get('verified_only') === 'true';

    let query = supabase
      .from('user_endorsements')
      .select(`
        id,
        endorsement_type,
        skill_name,
        endorsement_level,
        endorsement_title,
        endorsement_message,
        project_context,
        collaboration_duration,
        evidence_urls,
        is_verified,
        verified_at,
        credibility_score,
        created_at,
        endorser:users!user_endorsements_endorser_id_fkey(
          id,
          display_name,
          avatar_url,
          is_premium
        )
      `)
      .eq('endorsed_user_id', targetUserId)
      .eq('status', 'active')
      .order('created_at', { ascending: false });

    // Apply filters
    if (endorsementType) {
      query = query.eq('endorsement_type', endorsementType);
    }
    if (skillName) {
      query = query.eq('skill_name', skillName);
    }
    if (verifiedOnly) {
      query = query.eq('is_verified', true);
    }

    // If viewing another user's endorsements, only show public ones
    if (targetUserId !== userId) {
      query = query.eq('is_public', true);
    }

    const { data: endorsements, error: endorsementsError } = await query;

    if (endorsementsError) {
      throw new Error(`Failed to fetch endorsements: ${endorsementsError.message}`);
    }

    // Group endorsements by type and skill
    const endorsementsByType = {};
    const endorsementsBySkill = {};
    
    endorsements?.forEach(endorsement => {
      // Group by type
      if (!endorsementsByType[endorsement.endorsement_type]) {
        endorsementsByType[endorsement.endorsement_type] = [];
      }
      endorsementsByType[endorsement.endorsement_type].push(endorsement);

      // Group by skill
      if (endorsement.skill_name) {
        if (!endorsementsBySkill[endorsement.skill_name]) {
          endorsementsBySkill[endorsement.skill_name] = [];
        }
        endorsementsBySkill[endorsement.skill_name].push(endorsement);
      }
    });

    // Calculate endorsement statistics
    const stats = {
      total_endorsements: endorsements?.length || 0,
      verified_endorsements: endorsements?.filter(e => e.is_verified).length || 0,
      average_level: endorsements?.length > 0 ? 
        endorsements.reduce((sum, e) => sum + e.endorsement_level, 0) / endorsements.length : 0,
      average_credibility: endorsements?.length > 0 ? 
        endorsements.reduce((sum, e) => sum + e.credibility_score, 0) / endorsements.length : 0,
      endorsement_types: Object.keys(endorsementsByType).length,
      unique_skills: Object.keys(endorsementsBySkill).length,
      unique_endorsers: new Set(endorsements?.map(e => e.endorser.id)).size
    };

    return {
      statusCode: 200,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        endorsements: endorsements || [],
        endorsements_by_type: endorsementsByType,
        endorsements_by_skill: endorsementsBySkill,
        statistics: stats
      })
    };

  } catch (error) {
    console.error('Get user endorsements error:', error);
    return {
      statusCode: 500,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ error: 'Failed to fetch endorsements' })
    };
  }
};

// Get Endorsement Recommendations
const getEndorsementRecommendations = async (event) => {
  try {
    const userId = getUserFromRequest(event);
    if (!userId) {
      return {
        statusCode: 401,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    const queryParams = new URLSearchParams(event.queryStringParameters || {});
    const limit = parseInt(queryParams.get('limit') || '10');

    // Get user's allies who they haven't endorsed recently
    const { data: allies } = await supabase
      .from('user_allies')
      .select(`
        ally_id,
        user_id,
        ally:users!user_allies_ally_id_fkey(
          id,
          display_name,
          avatar_url
        ),
        user:users!user_allies_user_id_fkey(
          id,
          display_name,
          avatar_url
        )
      `)
      .or(`user_id.eq.${userId},ally_id.eq.${userId}`)
      .eq('status', 'accepted');

    if (!allies || allies.length === 0) {
      return {
        statusCode: 200,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ recommendations: [] })
      };
    }

    // Get allies the user can endorse (exclude self)
    const allyUsers = allies.map(ally => {
      return ally.user_id === userId ? ally.ally : ally.user;
    }).filter(user => user.id !== userId);

    // Get recent collaborations to suggest endorsements
    const recommendations = await Promise.all(
      allyUsers.slice(0, limit).map(async (ally) => {
        // Check recent endorsements to avoid duplicates
        const { data: recentEndorsements } = await supabase
          .from('user_endorsements')
          .select('endorsement_type, skill_name')
          .eq('endorser_id', userId)
          .eq('endorsed_user_id', ally.id)
          .gte('created_at', new Date(Date.now() - 90 * 24 * 60 * 60 * 1000).toISOString()); // Last 90 days

        // Get collaboration history
        const { data: collaborations } = await supabase
          .from('project_contributors')
          .select(`
            project:projects(
              id,
              name,
              title,
              created_at
            )
          `)
          .eq('user_id', userId)
          .in('project_id', 
            supabase
              .from('project_contributors')
              .select('project_id')
              .eq('user_id', ally.id)
          )
          .limit(3);

        // Suggest endorsement types based on collaboration
        const suggestedTypes = [];
        if (collaborations && collaborations.length > 0) {
          suggestedTypes.push('collaboration', 'reliability');
          if (collaborations.length >= 3) {
            suggestedTypes.push('leadership');
          }
        }

        const alreadyEndorsedTypes = new Set(recentEndorsements?.map(e => e.endorsement_type) || []);
        const availableTypes = suggestedTypes.filter(type => !alreadyEndorsedTypes.has(type));

        return {
          ally: ally,
          collaboration_count: collaborations?.length || 0,
          recent_projects: collaborations?.map(c => c.project) || [],
          suggested_endorsement_types: availableTypes,
          recent_endorsements_count: recentEndorsements?.length || 0,
          recommendation_score: calculateRecommendationScore(collaborations, recentEndorsements)
        };
      })
    );

    // Sort by recommendation score and filter out low scores
    const filteredRecommendations = recommendations
      .filter(rec => rec.recommendation_score > 0.3)
      .sort((a, b) => b.recommendation_score - a.recommendation_score);

    return {
      statusCode: 200,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        recommendations: filteredRecommendations,
        total: filteredRecommendations.length
      })
    };

  } catch (error) {
    console.error('Get endorsement recommendations error:', error);
    return {
      statusCode: 500,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ error: 'Failed to get endorsement recommendations' })
    };
  }
};

// Update Endorsement
const updateEndorsement = async (event) => {
  try {
    const userId = getUserFromRequest(event);
    if (!userId) {
      return {
        statusCode: 401,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    const endorsementId = event.path.split('/').pop();
    const data = JSON.parse(event.body);

    // Verify user owns the endorsement
    const { data: endorsement, error: endorsementError } = await supabase
      .from('user_endorsements')
      .select('endorser_id')
      .eq('id', endorsementId)
      .single();

    if (endorsementError) {
      return {
        statusCode: 404,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Endorsement not found' })
      };
    }

    if (endorsement.endorser_id !== userId) {
      return {
        statusCode: 403,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Cannot update another user\'s endorsement' })
      };
    }

    // Update allowed fields
    const updateData = {};
    if (data.endorsement_level !== undefined) updateData.endorsement_level = data.endorsement_level;
    if (data.endorsement_message !== undefined) updateData.endorsement_message = data.endorsement_message;
    if (data.is_public !== undefined) updateData.is_public = data.is_public;
    if (data.evidence_urls !== undefined) updateData.evidence_urls = data.evidence_urls;
    updateData.updated_at = new Date().toISOString();

    const { data: updatedEndorsement, error: updateError } = await supabase
      .from('user_endorsements')
      .update(updateData)
      .eq('id', endorsementId)
      .select()
      .single();

    if (updateError) {
      throw new Error(`Failed to update endorsement: ${updateError.message}`);
    }

    return {
      statusCode: 200,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ endorsement: updatedEndorsement })
    };

  } catch (error) {
    console.error('Update endorsement error:', error);
    return {
      statusCode: 500,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ error: 'Failed to update endorsement' })
    };
  }
};

// Helper functions
const calculateEndorserCredibility = async (endorserId) => {
  try {
    // Get endorser's achievements and metrics
    const { data: achievements } = await supabase
      .from('user_achievements')
      .select('achievement_score')
      .eq('user_id', endorserId)
      .eq('is_verified', true);

    const { data: metrics } = await supabase
      .from('collaboration_metrics')
      .select('collaboration_success_rate, quality_score')
      .eq('user_id', endorserId)
      .order('period_start', { ascending: false })
      .limit(1)
      .single();

    // Calculate credibility based on achievements and performance
    const achievementScore = achievements?.reduce((sum, a) => sum + (a.achievement_score || 0), 0) || 0;
    const performanceScore = metrics ? (metrics.collaboration_success_rate + metrics.quality_score) / 2 : 50;
    
    return Math.min((achievementScore * 0.01) + (performanceScore * 0.01), 1.0);
  } catch (error) {
    console.error('Calculate endorser credibility error:', error);
    return 0.5; // Default credibility
  }
};

const calculateRecommendationScore = (collaborations, recentEndorsements) => {
  const collaborationScore = Math.min((collaborations?.length || 0) * 0.3, 1.0);
  const recencyPenalty = (recentEndorsements?.length || 0) * 0.2;
  return Math.max(collaborationScore - recencyPenalty, 0);
};

const createEndorsementActivity = async (endorsement) => {
  try {
    const activityData = {
      activity_type: 'skill_endorsed',
      activity_title: `Skill Endorsed: ${endorsement.skill_name || endorsement.endorsement_type}`,
      activity_description: endorsement.endorsement_message || `${endorsement.endorser.display_name} endorsed ${endorsement.endorsed_user.display_name}`,
      actor_id: endorsement.endorser.id,
      target_user_id: endorsement.endorsed_user.id,
      visibility: endorsement.is_public ? 'public' : 'private',
      metadata: {
        endorsement_id: endorsement.id,
        endorsement_type: endorsement.endorsement_type,
        skill_name: endorsement.skill_name,
        endorsement_level: endorsement.endorsement_level
      }
    };

    await supabase
      .from('activity_feeds')
      .insert([activityData]);

  } catch (error) {
    console.error('Create endorsement activity error:', error);
  }
};

// Route requests to appropriate handlers
exports.handler = async (event, context) => {
  // Add CORS headers
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS'
  };

  // Handle preflight requests
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers,
      body: ''
    };
  }

  const path = event.path.replace('/.netlify/functions/endorsement-system', '');

  try {
    let response;

    if (event.httpMethod === 'GET') {
      if (path === '' || path === '/') {
        response = await getUserEndorsements(event);
      } else if (path === '/recommendations' || path === '/recommendations/') {
        response = await getEndorsementRecommendations(event);
      } else {
        response = {
          statusCode: 404,
          body: JSON.stringify({ error: 'Endpoint not found' })
        };
      }
    } else if (event.httpMethod === 'POST') {
      if (path === '' || path === '/') {
        response = await createEndorsement(event);
      } else {
        response = {
          statusCode: 404,
          body: JSON.stringify({ error: 'Endpoint not found' })
        };
      }
    } else if (event.httpMethod === 'PUT') {
      if (path.includes('/')) {
        response = await updateEndorsement(event);
      } else {
        response = {
          statusCode: 404,
          body: JSON.stringify({ error: 'Endpoint not found' })
        };
      }
    } else {
      response = {
        statusCode: 405,
        body: JSON.stringify({ error: 'Method not allowed' })
      };
    }

    // Add CORS headers to response
    response.headers = { ...response.headers, ...headers };
    return response;

  } catch (error) {
    console.error('Endorsement System API error:', error);
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ error: 'Internal server error' })
    };
  }
};
