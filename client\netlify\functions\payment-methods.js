// Payment Methods Management API
// Integration & Services Agent: Comprehensive payment method management

const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_ANON_KEY
);

// CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS'
};

// Authenticate user from JWT token
const authenticateUser = async (authHeader) => {
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    throw new Error('Missing or invalid authorization header');
  }

  const token = authHeader.substring(7);
  
  try {
    const { data: { user }, error } = await supabase.auth.getUser(token);
    
    if (error || !user) {
      throw new Error('Invalid authentication token');
    }
    
    return user;
  } catch (error) {
    console.error('Authentication error:', error);
    throw new Error('Authentication failed');
  }
};

// Get user's payment methods
const getPaymentMethods = async (user) => {
  try {
    const { data: accounts, error } = await supabase
      .from('teller_accounts')
      .select(`
        id,
        account_name,
        institution_name,
        account_type,
        account_subtype,
        mask,
        available_balance,
        current_balance,
        currency,
        verification_status,
        capabilities,
        is_active,
        created_at,
        last_used_at
      `)
      .eq('user_id', user.id)
      .eq('is_active', true)
      .order('last_used_at', { ascending: false, nullsFirst: false })
      .order('created_at', { ascending: false });

    if (error) {
      throw new Error(`Failed to fetch payment methods: ${error.message}`);
    }

    // Get user's payment preferences
    const { data: preferences, error: prefError } = await supabase
      .from('payment_preferences')
      .select('*')
      .eq('user_id', user.id)
      .single();

    if (prefError && prefError.code !== 'PGRST116') { // Not found is OK
      console.error('Failed to fetch payment preferences:', prefError);
    }

    return {
      payment_methods: accounts || [],
      preferences: preferences || {
        default_account_id: null,
        auto_select_optimal: true,
        notification_preferences: {
          email: true,
          push: true,
          sms: false
        }
      }
    };

  } catch (error) {
    console.error('Get payment methods error:', error);
    throw error;
  }
};

// Set default payment method
const setDefaultPaymentMethod = async (user, accountId) => {
  try {
    // Verify account belongs to user
    const { data: account, error: accountError } = await supabase
      .from('teller_accounts')
      .select('id')
      .eq('id', accountId)
      .eq('user_id', user.id)
      .eq('is_active', true)
      .single();

    if (accountError || !account) {
      throw new Error('Payment method not found or unauthorized');
    }

    // Update or create payment preferences
    const { error: prefError } = await supabase
      .from('payment_preferences')
      .upsert({
        user_id: user.id,
        default_payment_account_id: accountId,
        updated_at: new Date().toISOString()
      });

    if (prefError) {
      throw new Error(`Failed to set default payment method: ${prefError.message}`);
    }

    return { success: true, default_payment_account_id: accountId };

  } catch (error) {
    console.error('Set default payment method error:', error);
    throw error;
  }
};

// Update payment preferences
const updatePaymentPreferences = async (user, preferences) => {
  try {
    const updateData = {
      user_id: user.id,
      ...preferences,
      updated_at: new Date().toISOString()
    };

    const { data, error } = await supabase
      .from('payment_preferences')
      .upsert(updateData)
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to update payment preferences: ${error.message}`);
    }

    return data;

  } catch (error) {
    console.error('Update payment preferences error:', error);
    throw error;
  }
};

// Remove payment method
const removePaymentMethod = async (user, accountId) => {
  try {
    // Verify account belongs to user
    const { data: account, error: accountError } = await supabase
      .from('teller_accounts')
      .select('id, default_account_id')
      .eq('id', accountId)
      .eq('user_id', user.id)
      .single();

    if (accountError || !account) {
      throw new Error('Payment method not found or unauthorized');
    }

    // Deactivate the account
    const { error: deactivateError } = await supabase
      .from('teller_accounts')
      .update({
        is_active: false,
        removed_at: new Date().toISOString()
      })
      .eq('id', accountId);

    if (deactivateError) {
      throw new Error(`Failed to remove payment method: ${deactivateError.message}`);
    }

    // If this was the default account, clear the default
    const { error: prefError } = await supabase
      .from('payment_preferences')
      .update({
        default_payment_account_id: null,
        updated_at: new Date().toISOString()
      })
      .eq('user_id', user.id)
      .eq('default_payment_account_id', accountId);

    if (prefError) {
      console.error('Failed to clear default payment method:', prefError);
    }

    return { success: true };

  } catch (error) {
    console.error('Remove payment method error:', error);
    throw error;
  }
};

// Get optimal payment method for a transaction
const getOptimalPaymentMethod = async (user, transactionData) => {
  try {
    const { amount, currency = 'USD', transaction_type = 'transfer' } = transactionData;

    // Get user's active accounts
    const { data: accounts, error } = await supabase
      .from('teller_accounts')
      .select('*')
      .eq('user_id', user.id)
      .eq('is_active', true);

    if (error || !accounts || accounts.length === 0) {
      throw new Error('No active payment methods found');
    }

    // Get payment routing rules
    const { data: rules, error: rulesError } = await supabase
      .from('payment_routing_rules')
      .select('*')
      .eq('user_id', user.id)
      .eq('is_active', true)
      .order('priority', { ascending: true });

    if (rulesError) {
      console.error('Failed to fetch routing rules:', rulesError);
    }

    // Apply routing logic
    let optimalAccount = null;

    // 1. Check routing rules first
    if (rules && rules.length > 0) {
      for (const rule of rules) {
        if (matchesRoutingRule(rule, transactionData)) {
          optimalAccount = accounts.find(acc => acc.id === rule.preferred_account_id);
          if (optimalAccount) break;
        }
      }
    }

    // 2. If no rule matches, use default logic
    if (!optimalAccount) {
      // Get user preferences
      const { data: preferences } = await supabase
        .from('payment_preferences')
        .select('default_payment_account_id')
        .eq('user_id', user.id)
        .single();

      // Use default account if set
      if (preferences?.default_payment_account_id) {
        optimalAccount = accounts.find(acc => acc.id === preferences.default_payment_account_id);
      }

      // Fall back to account with highest balance that can handle the transaction
      if (!optimalAccount) {
        optimalAccount = accounts
          .filter(acc => acc.available_balance >= amount && acc.supports_ach)
          .sort((a, b) => b.available_balance - a.available_balance)[0];
      }

      // Last resort: any active account
      if (!optimalAccount) {
        optimalAccount = accounts[0];
      }
    }

    return optimalAccount;

  } catch (error) {
    console.error('Get optimal payment method error:', error);
    throw error;
  }
};

// Check if transaction matches routing rule
const matchesRoutingRule = (rule, transactionData) => {
  const { amount, transaction_type, recipient_type } = transactionData;

  // Check amount range
  if (rule.amount_min && amount < rule.amount_min) return false;
  if (rule.amount_max && amount > rule.amount_max) return false;

  // Check transaction type
  if (rule.payment_type && transaction_type !== rule.payment_type) return false;

  return true;
};

// Main handler function
exports.handler = async (event, context) => {
  // Handle CORS preflight
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers: corsHeaders,
      body: ''
    };
  }

  try {
    // Authenticate user
    const user = await authenticateUser(event.headers.authorization);
    
    // Parse request path and method
    const pathParts = event.path.split('/').filter(Boolean);
    const action = pathParts[pathParts.length - 1];
    const httpMethod = event.httpMethod;
    const body = event.body ? JSON.parse(event.body) : {};
    const queryParams = new URLSearchParams(event.queryStringParameters || {});

    let result;

    switch (action) {
      case 'list':
        if (httpMethod !== 'GET') {
          throw new Error('Method not allowed');
        }
        result = await getPaymentMethods(user);
        break;

      case 'set-default':
        if (httpMethod !== 'POST') {
          throw new Error('Method not allowed');
        }
        if (!body.account_id) {
          throw new Error('Account ID is required');
        }
        result = await setDefaultPaymentMethod(user, body.account_id);
        break;

      case 'preferences':
        if (httpMethod === 'PUT') {
          result = await updatePaymentPreferences(user, body);
        } else {
          throw new Error('Method not allowed');
        }
        break;

      case 'remove':
        if (httpMethod !== 'DELETE') {
          throw new Error('Method not allowed');
        }
        const accountId = queryParams.get('account_id');
        if (!accountId) {
          throw new Error('Account ID is required');
        }
        result = await removePaymentMethod(user, accountId);
        break;

      case 'optimal':
        if (httpMethod !== 'POST') {
          throw new Error('Method not allowed');
        }
        result = await getOptimalPaymentMethod(user, body);
        break;

      default:
        throw new Error('Invalid action');
    }

    return {
      statusCode: 200,
      headers: corsHeaders,
      body: JSON.stringify({
        success: true,
        data: result,
        timestamp: new Date().toISOString()
      })
    };

  } catch (error) {
    console.error('Payment Methods API error:', error);
    
    return {
      statusCode: error.message.includes('Authentication') ? 401 : 
                  error.message.includes('not allowed') ? 405 : 400,
      headers: corsHeaders,
      body: JSON.stringify({
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      })
    };
  }
};
