// Standalone roadmap function that doesn't rely on any imports
exports.handler = async function(event, context) {
  // Handle OPTIONS request for CORS
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type',
        'Access-Control-Allow-Methods': 'GET, OPTIONS'
      },
      body: ''
    };
  }
  
  try {
    // Simple hardcoded roadmap data
    const roadmapData = [
      {
        id: 1,
        title: "Foundation & User Management",
        timeframe: "Completed",
        expanded: true,
        sections: [
          {
            id: "1.1",
            title: "Project Setup & Configuration",
            tasks: [
              { id: "1.1.1", text: "Finalize tech stack (React, Supabase)", completed: true },
              { id: "1.1.2", text: "Set up development environment", completed: true },
              { id: "1.1.3", text: "Configure Netlify deployment", completed: true }
            ]
          }
        ]
      },
      {
        id: 2,
        title: "Project Creation & Management",
        timeframe: "Phase 1",
        expanded: false,
        sections: [
          {
            id: "2.1",
            title: "Project Wizard",
            tasks: [
              { id: "2.1.1", text: "Design project creation flow", completed: true },
              { id: "2.1.2", text: "Implement project basics form", completed: true },
              { id: "2.1.3", text: "Add team & contributors section", completed: true }
            ]
          }
        ]
      }
    ];
    
    // Calculate stats
    const stats = calculateStats(roadmapData);
    
    // Return the data
    return {
      statusCode: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type',
        'Access-Control-Allow-Methods': 'GET, OPTIONS'
      },
      body: JSON.stringify({
        success: true,
        data: roadmapData,
        stats: stats,
        source: 'standalone-function'
      })
    };
  } catch (error) {
    console.error('Error in standalone-roadmap function:', error);
    
    return {
      statusCode: 500,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type',
        'Access-Control-Allow-Methods': 'GET, OPTIONS'
      },
      body: JSON.stringify({
        success: false,
        error: error.message
      })
    };
  }
};

// Function to calculate stats
function calculateStats(phases) {
  let totalTasks = 0;
  let completedTasks = 0;
  let phaseStats = [];

  phases.forEach(phase => {
    let phaseTotalTasks = 0;
    let phaseCompletedTasks = 0;
    
    phase.sections.forEach(section => {
      phaseTotalTasks += section.tasks.length;
      phaseCompletedTasks += section.tasks.filter(task => task.completed).length;
    });
    
    totalTasks += phaseTotalTasks;
    completedTasks += phaseCompletedTasks;
    
    phaseStats.push({
      id: phase.id,
      title: phase.title,
      timeframe: phase.timeframe,
      progress: phaseTotalTasks > 0 ? Math.round((phaseCompletedTasks / phaseTotalTasks) * 100) : 0
    });
  });

  return {
    totalTasks,
    completedTasks,
    progressPercentage: totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0,
    phases: phaseStats
  };
}
