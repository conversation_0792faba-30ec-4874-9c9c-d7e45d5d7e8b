// Reports Service API
// Integration & Services Agent: Custom report generation and management

const { createClient } = require('@supabase/supabase-js');
const PDFDocument = require('pdfkit');
const { Parser } = require('json2csv');

// Initialize Supabase client
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_ANON_KEY
);

// CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS'
};

// Authenticate user from JWT token
const authenticateUser = async (authHeader) => {
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    throw new Error('Missing or invalid authorization header');
  }

  const token = authHeader.substring(7);
  
  try {
    const { data: { user }, error } = await supabase.auth.getUser(token);
    
    if (error || !user) {
      throw new Error('Invalid authentication token');
    }
    
    return user;
  } catch (error) {
    console.error('Authentication error:', error);
    throw new Error('Authentication failed');
  }
};

// Create custom report
const createCustomReport = async (user, reportData) => {
  try {
    const {
      report_name,
      report_description,
      report_type,
      report_config,
      visualization_config = {},
      filter_config = {},
      schedule_config = {},
      is_scheduled = false
    } = reportData;

    if (!report_name || !report_type || !report_config) {
      throw new Error('report_name, report_type, and report_config are required');
    }

    // Calculate next run time if scheduled
    let next_run_at = null;
    if (is_scheduled && schedule_config.frequency) {
      next_run_at = calculateNextRunTime(schedule_config);
    }

    const { data: report, error } = await supabase
      .from('custom_reports')
      .insert({
        user_id: user.id,
        report_name,
        report_description,
        report_type,
        report_config,
        visualization_config,
        filter_config,
        schedule_config,
        is_scheduled,
        next_run_at
      })
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to create report: ${error.message}`);
    }

    return {
      success: true,
      report_id: report.id,
      report: report,
      message: 'Custom report created successfully'
    };

  } catch (error) {
    console.error('Create custom report error:', error);
    throw error;
  }
};

// Generate report
const generateReport = async (user, reportId, options = {}) => {
  try {
    // Get report configuration
    const { data: report, error: reportError } = await supabase
      .from('custom_reports')
      .select('*')
      .eq('id', reportId)
      .eq('user_id', user.id)
      .single();

    if (reportError || !report) {
      throw new Error('Report not found or access denied');
    }

    // Create report generation record
    const { data: generation, error: genError } = await supabase
      .from('report_generations')
      .insert({
        report_id: reportId,
        user_id: user.id,
        generation_type: options.generation_type || 'manual',
        status: 'processing',
        data_start_date: options.start_date,
        data_end_date: options.end_date
      })
      .select()
      .single();

    if (genError) {
      throw new Error(`Failed to create generation record: ${genError.message}`);
    }

    try {
      // Generate report data based on type
      const reportData = await generateReportData(user, report, options);
      
      // Generate file if requested
      let fileUrl = null;
      let fileSize = null;
      
      if (options.format && options.format !== 'json') {
        const fileResult = await generateReportFile(reportData, options.format, report);
        fileUrl = fileResult.url;
        fileSize = fileResult.size;
      }

      // Update generation record with results
      await supabase
        .from('report_generations')
        .update({
          status: 'completed',
          result_data: reportData,
          file_url: fileUrl,
          file_size: fileSize,
          processing_time_ms: Date.now() - new Date(generation.created_at).getTime(),
          data_points_processed: reportData.data_points || 0,
          completed_at: new Date().toISOString()
        })
        .eq('id', generation.id);

      // Update report last generated time
      await supabase
        .from('custom_reports')
        .update({
          last_generated_at: new Date().toISOString(),
          generation_count: report.generation_count + 1
        })
        .eq('id', reportId);

      return {
        success: true,
        generation_id: generation.id,
        report_data: reportData,
        file_url: fileUrl,
        processing_time_ms: Date.now() - new Date(generation.created_at).getTime()
      };

    } catch (error) {
      // Update generation record with error
      await supabase
        .from('report_generations')
        .update({
          status: 'failed',
          error_message: error.message,
          completed_at: new Date().toISOString()
        })
        .eq('id', generation.id);

      throw error;
    }

  } catch (error) {
    console.error('Generate report error:', error);
    throw error;
  }
};

// Get user's custom reports
const getUserReports = async (user, queryParams) => {
  try {
    let query = supabase
      .from('custom_reports')
      .select('*')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false });

    // Apply filters
    if (queryParams.get('report_type')) {
      query = query.eq('report_type', queryParams.get('report_type'));
    }

    if (queryParams.get('is_active')) {
      query = query.eq('is_active', queryParams.get('is_active') === 'true');
    }

    // Apply pagination
    const limit = Math.min(parseInt(queryParams.get('limit')) || 50, 100);
    const offset = parseInt(queryParams.get('offset')) || 0;
    
    query = query.range(offset, offset + limit - 1);

    const { data: reports, error } = await query;

    if (error) {
      throw new Error(`Failed to fetch reports: ${error.message}`);
    }

    return reports || [];

  } catch (error) {
    console.error('Get user reports error:', error);
    throw error;
  }
};

// Get report generations
const getReportGenerations = async (user, reportId, queryParams) => {
  try {
    let query = supabase
      .from('report_generations')
      .select('*')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false });

    if (reportId) {
      query = query.eq('report_id', reportId);
    }

    // Apply pagination
    const limit = Math.min(parseInt(queryParams.get('limit')) || 20, 50);
    const offset = parseInt(queryParams.get('offset')) || 0;
    
    query = query.range(offset, offset + limit - 1);

    const { data: generations, error } = await query;

    if (error) {
      throw new Error(`Failed to fetch report generations: ${error.message}`);
    }

    return generations || [];

  } catch (error) {
    console.error('Get report generations error:', error);
    throw error;
  }
};

// Delete custom report
const deleteCustomReport = async (user, reportId) => {
  try {
    const { error } = await supabase
      .from('custom_reports')
      .delete()
      .eq('id', reportId)
      .eq('user_id', user.id);

    if (error) {
      throw new Error(`Failed to delete report: ${error.message}`);
    }

    return {
      success: true,
      message: 'Report deleted successfully'
    };

  } catch (error) {
    console.error('Delete custom report error:', error);
    throw error;
  }
};

// Helper functions
const calculateNextRunTime = (scheduleConfig) => {
  const now = new Date();
  const { frequency, time, day_of_week, day_of_month } = scheduleConfig;

  switch (frequency) {
    case 'daily':
      const dailyNext = new Date(now);
      dailyNext.setDate(now.getDate() + 1);
      if (time) {
        const [hours, minutes] = time.split(':');
        dailyNext.setHours(parseInt(hours), parseInt(minutes), 0, 0);
      }
      return dailyNext.toISOString();

    case 'weekly':
      const weeklyNext = new Date(now);
      const targetDay = day_of_week || 1; // Default to Monday
      const daysUntilTarget = (targetDay - now.getDay() + 7) % 7 || 7;
      weeklyNext.setDate(now.getDate() + daysUntilTarget);
      if (time) {
        const [hours, minutes] = time.split(':');
        weeklyNext.setHours(parseInt(hours), parseInt(minutes), 0, 0);
      }
      return weeklyNext.toISOString();

    case 'monthly':
      const monthlyNext = new Date(now);
      monthlyNext.setMonth(now.getMonth() + 1);
      monthlyNext.setDate(day_of_month || 1);
      if (time) {
        const [hours, minutes] = time.split(':');
        monthlyNext.setHours(parseInt(hours), parseInt(minutes), 0, 0);
      }
      return monthlyNext.toISOString();

    default:
      return null;
  }
};

const generateReportData = async (user, report, options) => {
  const { report_type, report_config, filter_config } = report;
  const startDate = options.start_date || getDefaultStartDate(report_config.period || 'last_30_days');
  const endDate = options.end_date || new Date().toISOString().split('T')[0];

  switch (report_type) {
    case 'performance':
      return await generatePerformanceReport(user.id, startDate, endDate, report_config, filter_config);
    
    case 'financial':
      return await generateFinancialReport(user.id, startDate, endDate, report_config, filter_config);
    
    case 'project':
      return await generateProjectReport(user.id, startDate, endDate, report_config, filter_config);
    
    default:
      throw new Error(`Unsupported report type: ${report_type}`);
  }
};

const generatePerformanceReport = async (userId, startDate, endDate, config, filters) => {
  const { data: metrics, error } = await supabase
    .from('performance_metrics')
    .select('*')
    .eq('user_id', userId)
    .gte('period_start', startDate)
    .lte('period_end', endDate)
    .order('period_start', { ascending: true });

  if (error) {
    throw new Error(`Failed to fetch performance data: ${error.message}`);
  }

  return {
    report_type: 'performance',
    period: { start_date: startDate, end_date: endDate },
    metrics: metrics || [],
    summary: calculatePerformanceSummary(metrics || []),
    data_points: metrics?.length || 0
  };
};

const generateFinancialReport = async (userId, startDate, endDate, config, filters) => {
  const { data: summaries, error } = await supabase
    .from('financial_summaries')
    .select('*')
    .eq('user_id', userId)
    .gte('period_start', startDate)
    .lte('period_end', endDate)
    .order('period_start', { ascending: true });

  if (error) {
    throw new Error(`Failed to fetch financial data: ${error.message}`);
  }

  return {
    report_type: 'financial',
    period: { start_date: startDate, end_date: endDate },
    summaries: summaries || [],
    totals: calculateFinancialTotals(summaries || []),
    data_points: summaries?.length || 0
  };
};

const generateProjectReport = async (userId, startDate, endDate, config, filters) => {
  const { data: projects, error } = await supabase
    .from('project_analytics')
    .select('*')
    .eq('user_id', userId)
    .gte('actual_start_date', startDate)
    .lte('actual_end_date', endDate)
    .order('actual_start_date', { ascending: true });

  if (error) {
    throw new Error(`Failed to fetch project data: ${error.message}`);
  }

  return {
    report_type: 'project',
    period: { start_date: startDate, end_date: endDate },
    projects: projects || [],
    insights: calculateProjectInsights(projects || []),
    data_points: projects?.length || 0
  };
};

const generateReportFile = async (reportData, format, report) => {
  switch (format) {
    case 'csv':
      return generateCSVFile(reportData, report);
    case 'pdf':
      return generatePDFFile(reportData, report);
    default:
      throw new Error(`Unsupported file format: ${format}`);
  }
};

const generateCSVFile = async (reportData, report) => {
  try {
    let data = [];
    
    switch (reportData.report_type) {
      case 'performance':
        data = reportData.metrics;
        break;
      case 'financial':
        data = reportData.summaries;
        break;
      case 'project':
        data = reportData.projects;
        break;
    }

    const parser = new Parser();
    const csv = parser.parse(data);
    
    // In a real implementation, you would upload this to S3 or similar
    // For now, we'll return a mock URL
    return {
      url: `https://reports.royalty.technology/csv/${report.id}_${Date.now()}.csv`,
      size: Buffer.byteLength(csv, 'utf8')
    };
    
  } catch (error) {
    throw new Error(`Failed to generate CSV: ${error.message}`);
  }
};

const generatePDFFile = async (reportData, report) => {
  try {
    const doc = new PDFDocument();
    
    // Add report content
    doc.fontSize(20).text(report.report_name, 50, 50);
    doc.fontSize(12).text(`Generated: ${new Date().toLocaleDateString()}`, 50, 80);
    doc.text(`Period: ${reportData.period.start_date} to ${reportData.period.end_date}`, 50, 100);
    
    // Add summary data
    if (reportData.summary) {
      doc.text('Summary:', 50, 140);
      doc.text(JSON.stringify(reportData.summary, null, 2), 50, 160);
    }
    
    // In a real implementation, you would upload this to S3 or similar
    // For now, we'll return a mock URL
    return {
      url: `https://reports.royalty.technology/pdf/${report.id}_${Date.now()}.pdf`,
      size: 1024 // Mock size
    };
    
  } catch (error) {
    throw new Error(`Failed to generate PDF: ${error.message}`);
  }
};

const getDefaultStartDate = (period) => {
  const now = new Date();
  const startDate = new Date(now);

  switch (period) {
    case 'last_7_days':
      startDate.setDate(now.getDate() - 7);
      break;
    case 'last_30_days':
      startDate.setDate(now.getDate() - 30);
      break;
    case 'last_3_months':
      startDate.setMonth(now.getMonth() - 3);
      break;
    case 'last_6_months':
      startDate.setMonth(now.getMonth() - 6);
      break;
    default:
      startDate.setDate(now.getDate() - 30);
  }

  return startDate.toISOString().split('T')[0];
};

const calculatePerformanceSummary = (metrics) => {
  if (!metrics || metrics.length === 0) return {};

  const summary = {};
  const metricTypes = [...new Set(metrics.map(m => m.metric_type))];

  metricTypes.forEach(type => {
    const typeMetrics = metrics.filter(m => m.metric_type === type);
    const values = typeMetrics.map(m => parseFloat(m.metric_value));
    
    summary[type] = {
      latest: values[values.length - 1] || 0,
      average: values.reduce((sum, val) => sum + val, 0) / values.length,
      min: Math.min(...values),
      max: Math.max(...values),
      trend: values.length > 1 ? (values[values.length - 1] > values[0] ? 'increasing' : 'decreasing') : 'stable'
    };
  });

  return summary;
};

const calculateFinancialTotals = (summaries) => {
  if (!summaries || summaries.length === 0) return {};

  return {
    total_revenue: summaries.reduce((sum, s) => sum + (parseFloat(s.total_revenue) || 0), 0),
    total_expenses: summaries.reduce((sum, s) => sum + (parseFloat(s.total_expenses) || 0), 0),
    total_profit: summaries.reduce((sum, s) => sum + (parseFloat(s.net_profit) || 0), 0),
    avg_monthly_revenue: summaries.reduce((sum, s) => sum + (parseFloat(s.total_revenue) || 0), 0) / summaries.length,
    transaction_count: summaries.reduce((sum, s) => sum + (s.transaction_count || 0), 0)
  };
};

const calculateProjectInsights = (projects) => {
  if (!projects || projects.length === 0) return {};

  const completed = projects.filter(p => p.project_status === 'completed');
  
  return {
    total_projects: projects.length,
    completed_projects: completed.length,
    success_rate: completed.length > 0 ? (completed.filter(p => p.success_indicator).length / completed.length) * 100 : 0,
    avg_timeline_efficiency: completed.reduce((sum, p) => sum + (parseFloat(p.timeline_efficiency) || 0), 0) / completed.length,
    avg_budget_efficiency: completed.reduce((sum, p) => sum + (parseFloat(p.budget_efficiency) || 0), 0) / completed.length,
    avg_rating: completed.reduce((sum, p) => sum + (parseFloat(p.final_rating) || 0), 0) / completed.length
  };
};

// Main handler function
exports.handler = async (event, context) => {
  // Handle CORS preflight
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers: corsHeaders,
      body: ''
    };
  }

  try {
    // Authenticate user
    const user = await authenticateUser(event.headers.authorization);
    
    // Parse request path and method
    const pathParts = event.path.split('/').filter(Boolean);
    const action = pathParts[pathParts.length - 1];
    const httpMethod = event.httpMethod;
    const body = event.body ? JSON.parse(event.body) : {};
    const queryParams = new URLSearchParams(event.queryStringParameters || {});

    let result;

    switch (action) {
      case 'create':
        if (httpMethod !== 'POST') {
          throw new Error('Method not allowed');
        }
        result = await createCustomReport(user, body);
        break;

      case 'generate':
        if (httpMethod !== 'POST') {
          throw new Error('Method not allowed');
        }
        const reportId = body.report_id;
        if (!reportId) {
          throw new Error('report_id is required');
        }
        result = await generateReport(user, reportId, body.options || {});
        break;

      case 'list':
        if (httpMethod !== 'GET') {
          throw new Error('Method not allowed');
        }
        result = await getUserReports(user, queryParams);
        break;

      case 'generations':
        if (httpMethod !== 'GET') {
          throw new Error('Method not allowed');
        }
        const genReportId = queryParams.get('report_id');
        result = await getReportGenerations(user, genReportId, queryParams);
        break;

      case 'delete':
        if (httpMethod !== 'DELETE') {
          throw new Error('Method not allowed');
        }
        const deleteReportId = queryParams.get('report_id');
        if (!deleteReportId) {
          throw new Error('report_id is required');
        }
        result = await deleteCustomReport(user, deleteReportId);
        break;

      default:
        throw new Error('Invalid action');
    }

    return {
      statusCode: 200,
      headers: corsHeaders,
      body: JSON.stringify({
        success: true,
        data: result,
        timestamp: new Date().toISOString()
      })
    };

  } catch (error) {
    console.error('Reports Service API error:', error);
    
    return {
      statusCode: error.message.includes('Authentication') ? 401 : 
                  error.message.includes('not allowed') ? 405 : 400,
      headers: corsHeaders,
      body: JSON.stringify({
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      })
    };
  }
};
