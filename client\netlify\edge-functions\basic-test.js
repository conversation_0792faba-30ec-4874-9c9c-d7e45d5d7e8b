// Basic test edge function
export default async (request, context) => {
  // Set CORS headers for cross-origin access
  const headers = {
    'Content-Type': 'application/json',
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type',
    'Access-Control-Allow-Methods': 'GET, OPTIONS'
  };

  // Handle OPTIONS request for CORS preflight
  if (request.method === 'OPTIONS') {
    return new Response(null, {
      status: 204,
      headers
    });
  }

  try {
    // Create a simple response with environment information
    const response = {
      success: true,
      message: "Basic test edge function is working!",
      timestamp: new Date().toISOString(),
      environment: {
        runtime: "Netlify Edge Function",
        hasEnv: typeof context.env === 'object',
        envKeys: context.env ? Object.keys(context.env) : []
      },
      request: {
        method: request.method,
        url: request.url,
        headers: Object.fromEntries([...request.headers.entries()])
      }
    };

    return new Response(
      JSON.stringify(response, null, 2),
      { headers }
    );
  } catch (error) {
    console.error('Error in basic-test edge function:', error);

    return new Response(
      JSON.stringify({
        success: false,
        error: error.message,
        stack: error.stack
      }, null, 2),
      {
        status: 500,
        headers
      }
    );
  }
};
