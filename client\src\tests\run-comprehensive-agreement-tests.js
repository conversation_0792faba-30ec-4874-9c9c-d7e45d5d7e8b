#!/usr/bin/env node

/**
 * Test Runner for Comprehensive Agreement System Validation
 * 
 * This script runs the comprehensive agreement validation tests and provides
 * detailed output and reporting for the alliance, venture, and contributor
 * agreement generation systems.
 */

import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import { spawn } from 'child_process';
import fs from 'fs';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

console.log('🚀 COMPREHENSIVE AGREEMENT SYSTEM TEST RUNNER');
console.log('==============================================\n');

// Test configuration
const testConfig = {
  testFile: join(__dirname, 'comprehensive-agreement-validation.test.js'),
  outputDir: join(__dirname, 'output', 'agreement-validation'),
  logFile: join(__dirname, 'output', 'test-execution.log'),
  timeout: 300000, // 5 minutes timeout
  verbose: true
};

// Ensure output directory exists
if (!fs.existsSync(testConfig.outputDir)) {
  fs.mkdirSync(testConfig.outputDir, { recursive: true });
}

console.log('📋 Test Configuration:');
console.log(`   Test File: ${testConfig.testFile}`);
console.log(`   Output Directory: ${testConfig.outputDir}`);
console.log(`   Log File: ${testConfig.logFile}`);
console.log(`   Timeout: ${testConfig.timeout / 1000}s`);
console.log(`   Verbose: ${testConfig.verbose}\n`);

// Check if test file exists
if (!fs.existsSync(testConfig.testFile)) {
  console.error('❌ Test file not found:', testConfig.testFile);
  process.exit(1);
}

console.log('✅ Test file found, starting execution...\n');

// Function to run the test
function runTest() {
  return new Promise((resolve, reject) => {
    const startTime = Date.now();
    let output = '';
    let errorOutput = '';
    
    console.log('🧪 Executing comprehensive agreement validation tests...');
    
    // Spawn the test process
    const testProcess = spawn('node', [testConfig.testFile], {
      cwd: dirname(testConfig.testFile),
      stdio: ['pipe', 'pipe', 'pipe'],
      env: { ...process.env, NODE_ENV: 'test' }
    });
    
    // Capture stdout
    testProcess.stdout.on('data', (data) => {
      const text = data.toString();
      output += text;
      if (testConfig.verbose) {
        process.stdout.write(text);
      }
    });
    
    // Capture stderr
    testProcess.stderr.on('data', (data) => {
      const text = data.toString();
      errorOutput += text;
      if (testConfig.verbose) {
        process.stderr.write(text);
      }
    });
    
    // Handle process completion
    testProcess.on('close', (code) => {
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      const result = {
        exitCode: code,
        duration,
        output,
        errorOutput,
        success: code === 0
      };
      
      // Save execution log
      const logContent = `# Test Execution Log
## Timestamp: ${new Date().toISOString()}
## Duration: ${duration}ms
## Exit Code: ${code}
## Success: ${result.success}

### Standard Output
\`\`\`
${output}
\`\`\`

### Error Output
\`\`\`
${errorOutput}
\`\`\`
`;
      
      try {
        fs.writeFileSync(testConfig.logFile, logContent);
        console.log(`\n📄 Execution log saved: ${testConfig.logFile}`);
      } catch (logError) {
        console.warn('⚠️  Failed to save execution log:', logError.message);
      }
      
      if (result.success) {
        resolve(result);
      } else {
        reject(result);
      }
    });
    
    // Handle process errors
    testProcess.on('error', (error) => {
      reject({
        exitCode: -1,
        duration: Date.now() - startTime,
        output,
        errorOutput: errorOutput + error.message,
        success: false,
        error
      });
    });
    
    // Set timeout
    const timeoutId = setTimeout(() => {
      testProcess.kill('SIGTERM');
      reject({
        exitCode: -1,
        duration: testConfig.timeout,
        output,
        errorOutput: errorOutput + 'Test timed out',
        success: false,
        timeout: true
      });
    }, testConfig.timeout);
    
    // Clear timeout on completion
    testProcess.on('close', () => {
      clearTimeout(timeoutId);
    });
  });
}

// Function to analyze test results
function analyzeResults(outputDir) {
  console.log('\n📊 ANALYZING TEST RESULTS');
  console.log('=========================');
  
  try {
    const files = fs.readdirSync(outputDir);
    
    const agreementFiles = files.filter(f => f.includes('agreement') && f.endsWith('.md'));
    const reportFiles = files.filter(f => f.includes('validation-report') && f.endsWith('.md'));
    const summaryFiles = files.filter(f => f.includes('summary') && f.endsWith('.md'));
    
    console.log(`📄 Generated Files:`);
    console.log(`   Agreement Files: ${agreementFiles.length}`);
    console.log(`   Validation Reports: ${reportFiles.length}`);
    console.log(`   Summary Reports: ${summaryFiles.length}`);
    
    // List all generated files
    if (agreementFiles.length > 0) {
      console.log('\n📋 Agreement Files:');
      agreementFiles.forEach(file => {
        console.log(`   - ${file}`);
      });
    }
    
    if (reportFiles.length > 0) {
      console.log('\n📋 Validation Reports:');
      reportFiles.forEach(file => {
        console.log(`   - ${file}`);
      });
    }
    
    if (summaryFiles.length > 0) {
      console.log('\n📋 Summary Reports:');
      summaryFiles.forEach(file => {
        console.log(`   - ${file}`);
      });
      
      // Try to read and display key metrics from summary
      try {
        const summaryFile = summaryFiles[0];
        const summaryPath = join(outputDir, summaryFile);
        const summaryContent = fs.readFileSync(summaryPath, 'utf8');
        
        // Extract key metrics
        const totalTestsMatch = summaryContent.match(/\*\*Total Tests\*\*: (\d+)/);
        const successfulGenerationsMatch = summaryContent.match(/\*\*Successful Generations\*\*: (\d+)\/(\d+)/);
        const averageScoreMatch = summaryContent.match(/\*\*Average Similarity Score\*\*: ([\d.]+)%/);
        
        if (totalTestsMatch || successfulGenerationsMatch || averageScoreMatch) {
          console.log('\n🎯 Key Metrics:');
          if (totalTestsMatch) {
            console.log(`   Total Tests: ${totalTestsMatch[1]}`);
          }
          if (successfulGenerationsMatch) {
            console.log(`   Successful Generations: ${successfulGenerationsMatch[1]}/${successfulGenerationsMatch[2]}`);
          }
          if (averageScoreMatch) {
            console.log(`   Average Similarity Score: ${averageScoreMatch[1]}%`);
          }
        }
      } catch (summaryError) {
        console.warn('⚠️  Could not parse summary metrics:', summaryError.message);
      }
    }
    
    return {
      agreementFiles: agreementFiles.length,
      reportFiles: reportFiles.length,
      summaryFiles: summaryFiles.length,
      totalFiles: files.length
    };
    
  } catch (error) {
    console.error('❌ Failed to analyze results:', error.message);
    return null;
  }
}

// Main execution
async function main() {
  try {
    console.log('⏱️  Starting test execution...\n');
    
    const result = await runTest();
    
    console.log('\n✅ TEST EXECUTION COMPLETED SUCCESSFULLY');
    console.log('=======================================');
    console.log(`⏱️  Duration: ${result.duration}ms`);
    console.log(`📤 Exit Code: ${result.exitCode}`);
    
    // Analyze results
    const analysis = analyzeResults(testConfig.outputDir);
    
    if (analysis) {
      console.log(`\n📊 Generated ${analysis.totalFiles} files total`);
      console.log(`📁 Results available in: ${testConfig.outputDir}`);
    }
    
    console.log('\n🎉 Comprehensive agreement validation tests completed successfully!');
    console.log('🔍 Review the generated agreements and validation reports for detailed analysis.');
    
    process.exit(0);
    
  } catch (error) {
    console.error('\n❌ TEST EXECUTION FAILED');
    console.error('========================');
    console.error(`⏱️  Duration: ${error.duration}ms`);
    console.error(`📤 Exit Code: ${error.exitCode}`);
    
    if (error.timeout) {
      console.error('⏰ Test execution timed out');
    }
    
    if (error.errorOutput) {
      console.error('\n📋 Error Output:');
      console.error(error.errorOutput);
    }
    
    // Still try to analyze any partial results
    const analysis = analyzeResults(testConfig.outputDir);
    if (analysis && analysis.totalFiles > 0) {
      console.log(`\n📊 Partial results: ${analysis.totalFiles} files generated`);
      console.log(`📁 Available in: ${testConfig.outputDir}`);
    }
    
    process.exit(1);
  }
}

// Handle process signals
process.on('SIGINT', () => {
  console.log('\n⚠️  Test execution interrupted by user');
  process.exit(130);
});

process.on('SIGTERM', () => {
  console.log('\n⚠️  Test execution terminated');
  process.exit(143);
});

// Run the main function
main().catch(error => {
  console.error('💥 Unexpected error:', error);
  process.exit(1);
});
