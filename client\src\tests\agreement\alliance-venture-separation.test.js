/**
 * Alliance-Venture Separation Tests
 * 
 * Tests that agreement generation correctly distinguishes between:
 * - Alliance (the company/entity in the agreement)
 * - Venture (the project being worked on)
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { NewAgreementGenerator } from '../../utils/agreement/newAgreementGenerator.js';

// Mock Supabase
const mockSupabase = {
  from: vi.fn(() => ({
    select: vi.fn(() => ({
      eq: vi.fn(() => ({
        single: vi.fn()
      }))
    }))
  }))
};

// Mock the supabase import
vi.mock('../../utils/supabase/supabase.utils', () => ({
  supabase: mockSupabase
}));

describe('Alliance-Venture Separation in Agreement Generation', () => {
  let generator;
  let mockTemplate;
  let mockProject;
  let mockContributors;
  let mockCurrentUser;
  let mockAlliance;

  beforeEach(() => {
    generator = new NewAgreementGenerator();
    
    // Simple test template
    mockTemplate = `
# CITY OF GAMERS INC.
# CONTRIBUTOR AGREEMENT

This Contributor Agreement (this "Agreement") is effective as of [Date], by and between City of Gamers Inc., a Florida LLC with its principal place of business at 1205 43rd Street, Suite B, Orlando, Florida 32839 (the "Company") and [Contributor] (the "Contributor").

## Project Description
This project involves development work on "Village of The Ages," a village simulation game.

**COMPANY:**
City of Gamers Inc.
By: ______________________
Name: Gynell Journigan
Title: President

**CONTRIBUTOR:**
Name: [Contributor]
Address: [Contributor Address]
`;

    // Mock project data with alliance_id
    mockProject = {
      id: 'test-project-id',
      name: 'EcoTech Innovations',
      description: 'A platform connecting eco-conscious consumers with sustainable products',
      project_type: 'software',
      alliance_id: 'test-alliance-id',
      team_id: 'test-alliance-id'
    };

    // Mock contributors
    mockContributors = [
      {
        id: 'contributor-1',
        permission_level: 'Owner',
        display_name: 'John Doe',
        email: '<EMAIL>'
      }
    ];

    // Mock current user
    mockCurrentUser = {
      id: 'user-1',
      email: '<EMAIL>',
      user_metadata: {
        full_name: 'Test Contributor'
      }
    };

    // Mock alliance data
    mockAlliance = {
      id: 'test-alliance-id',
      name: 'Green Tech Alliance',
      description: 'A collective focused on sustainable technology solutions',
      alliance_type: 'established',
      industry: 'Technology',
      team_members: [
        {
          user_id: 'user-1',
          role: 'founder',
          users: {
            id: 'user-1',
            email: '<EMAIL>',
            user_metadata: {
              full_name: 'Jane Smith'
            }
          }
        }
      ]
    };

    // Reset mocks
    vi.clearAllMocks();
  });

  describe('With Alliance Data', () => {
    beforeEach(() => {
      // Mock successful alliance fetch
      mockSupabase.from.mockReturnValue({
        select: vi.fn().mockReturnValue({
          eq: vi.fn().mockReturnValue({
            single: vi.fn().mockResolvedValue({
              data: mockAlliance,
              error: null
            })
          })
        })
      });
    });

    it('should use alliance name as company in agreement header', async () => {
      const result = await generator.generateAgreement(mockTemplate, mockProject, {
        contributors: mockContributors,
        currentUser: mockCurrentUser,
        fullName: 'Test Contributor'
      });

      expect(result).toContain('Green Tech Alliance');
      expect(result).not.toContain('City of Gamers Inc.');
    });

    it('should use venture name as project in description', async () => {
      const result = await generator.generateAgreement(mockTemplate, mockProject, {
        contributors: mockContributors,
        currentUser: mockCurrentUser,
        fullName: 'Test Contributor'
      });

      expect(result).toContain('EcoTech Innovations');
      expect(result).toContain('sustainable products');
    });

    it('should use alliance founder as company signer', async () => {
      const result = await generator.generateAgreement(mockTemplate, mockProject, {
        contributors: mockContributors,
        currentUser: mockCurrentUser,
        fullName: 'Test Contributor'
      });

      expect(result).toContain('Jane Smith');
      expect(result).toContain('Founder');
      expect(result).not.toContain('Gynell Journigan');
    });

    it('should correctly structure agreement parties', async () => {
      const result = await generator.generateAgreement(mockTemplate, mockProject, {
        contributors: mockContributors,
        currentUser: mockCurrentUser,
        fullName: 'Test Contributor'
      });

      // Agreement should be between alliance and contributor
      expect(result).toMatch(/between Green Tech Alliance.*and.*Test Contributor/);
    });
  });

  describe('Without Alliance Data', () => {
    beforeEach(() => {
      // Mock failed alliance fetch
      mockSupabase.from.mockReturnValue({
        select: vi.fn().mockReturnValue({
          eq: vi.fn().mockReturnValue({
            single: vi.fn().mockResolvedValue({
              data: null,
              error: { message: 'Alliance not found' }
            })
          })
        })
      });
    });

    it('should fall back to project owner as company', async () => {
      const result = await generator.generateAgreement(mockTemplate, mockProject, {
        contributors: mockContributors,
        currentUser: mockCurrentUser,
        fullName: 'Test Contributor'
      });

      expect(result).toContain('John Doe');
      expect(result).not.toContain('Green Tech Alliance');
    });

    it('should still reference venture as project', async () => {
      const result = await generator.generateAgreement(mockTemplate, mockProject, {
        contributors: mockContributors,
        currentUser: mockCurrentUser,
        fullName: 'Test Contributor'
      });

      expect(result).toContain('EcoTech Innovations');
    });
  });

  describe('Project Without Alliance ID', () => {
    it('should not attempt to fetch alliance data', async () => {
      const projectWithoutAlliance = {
        ...mockProject,
        alliance_id: null,
        team_id: null
      };

      await generator.generateAgreement(mockTemplate, projectWithoutAlliance, {
        contributors: mockContributors,
        currentUser: mockCurrentUser,
        fullName: 'Test Contributor'
      });

      expect(mockSupabase.from).not.toHaveBeenCalled();
    });
  });

  describe('Alliance Member Role Priority', () => {
    it('should prioritize founder over other roles', async () => {
      const allianceWithMultipleMembers = {
        ...mockAlliance,
        team_members: [
          {
            user_id: 'user-2',
            role: 'member',
            users: {
              id: 'user-2',
              email: '<EMAIL>',
              user_metadata: { full_name: 'Bob Member' }
            }
          },
          {
            user_id: 'user-1',
            role: 'founder',
            users: {
              id: 'user-1',
              email: '<EMAIL>',
              user_metadata: { full_name: 'Jane Smith' }
            }
          },
          {
            user_id: 'user-3',
            role: 'admin',
            users: {
              id: 'user-3',
              email: '<EMAIL>',
              user_metadata: { full_name: 'Alice Admin' }
            }
          }
        ]
      };

      mockSupabase.from.mockReturnValue({
        select: vi.fn().mockReturnValue({
          eq: vi.fn().mockReturnValue({
            single: vi.fn().mockResolvedValue({
              data: allianceWithMultipleMembers,
              error: null
            })
          })
        })
      });

      const result = await generator.generateAgreement(mockTemplate, mockProject, {
        contributors: mockContributors,
        currentUser: mockCurrentUser,
        fullName: 'Test Contributor'
      });

      expect(result).toContain('Jane Smith');
      expect(result).toContain('Founder');
      expect(result).not.toContain('Bob Member');
      expect(result).not.toContain('Alice Admin');
    });
  });
});
