// Roadmap API function using ES modules
import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client
const initSupabase = () => {
  const supabaseUrl = Netlify.env.get("SUPABASE_URL");
  const supabaseKey = Netlify.env.get("SUPABASE_SERVICE_KEY") || Netlify.env.get("SUPABASE_ANON_KEY");

  if (!supabaseUrl || !supabaseKey) {
    throw new Error('Missing Supabase credentials');
  }

  return createClient(supabaseUrl, supabaseKey);
};

// Function to calculate stats
function calculateStats(phases) {
  let totalTasks = 0;
  let completedTasks = 0;
  let phaseStats = [];

  phases.forEach(phase => {
    let phaseTotalTasks = 0;
    let phaseCompletedTasks = 0;

    phase.sections.forEach(section => {
      phaseTotalTasks += section.tasks.length;
      phaseCompletedTasks += section.tasks.filter(task => task.completed).length;
    });

    totalTasks += phaseTotalTasks;
    completedTasks += phaseCompletedTasks;

    phaseStats.push({
      id: phase.id,
      title: phase.title,
      timeframe: phase.timeframe,
      progress: phaseTotalTasks > 0 ? Math.round((phaseCompletedTasks / phaseTotalTasks) * 100) : 0
    });
  });

  return {
    totalTasks,
    completedTasks,
    progressPercentage: totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0,
    phases: phaseStats
  };
}

// Main function handler
export default async (req, context) => {
  // Set CORS headers for cross-origin access
  const headers = {
    'Content-Type': 'application/json',
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Allow-Methods': 'GET, OPTIONS'
  };

  // Handle OPTIONS request for CORS preflight
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      status: 204,
      headers
    });
  }

  try {
    // Initialize Supabase client
    const supabase = initSupabase();

    // Query the roadmap data from Supabase
    const { data: roadmapData, error: roadmapError } = await supabase
      .from('roadmap')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(1);

    if (roadmapError) {
      console.error('Error fetching roadmap from Supabase:', roadmapError);
      throw roadmapError;
    }

    // Check if we have data
    if (!roadmapData || roadmapData.length === 0) {
      // Try the RPC function as a fallback
      const { data: rpcData, error: rpcError } = await supabase.rpc('get_roadmap');

      if (rpcError) {
        console.error('Error calling get_roadmap RPC:', rpcError);
        throw rpcError;
      }

      if (!rpcData) {
        throw new Error('No roadmap data found in Supabase');
      }

      // Calculate stats
      const stats = calculateStats(rpcData);

      // Try to find the latest feature in the data
      let latestFeature = null;
      const metadataItem = rpcData.find(item => item.type === 'metadata');
      if (metadataItem && metadataItem.latest_feature) {
        latestFeature = metadataItem.latest_feature;
      }

      // Return the data
      return new Response(
        JSON.stringify({
          success: true,
          data: rpcData,
          stats: stats,
          latest_feature: latestFeature,
          source: 'supabase-rpc'
        }),
        { headers }
      );
    }

    // Process the roadmap data
    const roadmap = roadmapData[0].data;

    // Calculate stats
    const stats = calculateStats(roadmap);

    // Get the latest feature from the dedicated column if available, otherwise from the data array
    let latestFeature = roadmapData[0].latest_feature;

    // If latest_feature column is empty, try to get it from the data array
    if (!latestFeature) {
      const metadataItem = roadmap.find(item => item.type === 'metadata');
      if (metadataItem && metadataItem.latest_feature) {
        latestFeature = metadataItem.latest_feature;
      }
    }

    // Return the data
    return new Response(
      JSON.stringify({
        success: true,
        data: roadmap,
        stats: stats,
        latest_feature: latestFeature,
        source: 'supabase-direct'
      }),
      { headers }
    );
  } catch (error) {
    console.error('Error in roadmap function:', error);

    return new Response(
      JSON.stringify({
        success: false,
        error: error.message
      }),
      {
        status: 500,
        headers
      }
    );
  }
};

// Configure the function path
export const config = {
  path: "/api/roadmap"
};
