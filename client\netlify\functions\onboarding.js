// Onboarding API endpoints for user onboarding flow integration
const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_KEY
);

// CORS headers
const headers = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Content-Type': 'application/json'
};

// Helper function to get user from JWT token
const getUserFromToken = async (authHeader) => {
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    throw new Error('No valid authorization header');
  }

  const token = authHeader.replace('Bearer ', '');
  const { data: { user }, error } = await supabase.auth.getUser(token);
  
  if (error || !user) {
    throw new Error('Invalid or expired token');
  }
  
  return user;
};

// Initialize onboarding session
const initializeOnboarding = async (event) => {
  try {
    const user = await getUserFromToken(event.headers.authorization);
    const { device_info } = JSON.parse(event.body || '{}');

    // Create a unique session ID
    const sessionId = `onboarding_${user.id}_${Date.now()}`;

    // Initialize or update user preferences with onboarding state
    const onboardingState = {
      session_id: sessionId,
      started_at: new Date().toISOString(),
      current_step: 1,
      device_info: device_info || {},
      completed: false
    };

    const { data: existingPrefs, error: fetchError } = await supabase
      .from('user_preferences')
      .select('*')
      .eq('user_id', user.id)
      .single();

    if (fetchError && fetchError.code !== 'PGRST116') {
      throw fetchError;
    }

    const currentFeatureFlags = existingPrefs?.feature_flags || {};
    const updatedFeatureFlags = {
      ...currentFeatureFlags,
      onboarding: onboardingState
    };

    if (existingPrefs) {
      // Update existing preferences
      const { error: updateError } = await supabase
        .from('user_preferences')
        .update({
          feature_flags: updatedFeatureFlags,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', user.id);

      if (updateError) throw updateError;
    } else {
      // Create new preferences record
      const { error: insertError } = await supabase
        .from('user_preferences')
        .insert({
          user_id: user.id,
          feature_flags: updatedFeatureFlags,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        });

      if (insertError) throw insertError;
    }

    return {
      statusCode: 200,
      headers,
      body: JSON.stringify({
        success: true,
        session_id: sessionId,
        user_id: user.id
      })
    };
  } catch (error) {
    console.error('Error initializing onboarding:', error);
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({
        success: false,
        error: error.message
      })
    };
  }
};

// Update onboarding progress
const updateProgress = async (event) => {
  try {
    const user = await getUserFromToken(event.headers.authorization);
    const { session_id, step_number, step_data } = JSON.parse(event.body);

    if (!session_id || !step_number) {
      throw new Error('session_id and step_number are required');
    }

    // Get current user preferences
    const { data: currentPrefs, error: fetchError } = await supabase
      .from('user_preferences')
      .select('*')
      .eq('user_id', user.id)
      .single();

    if (fetchError) throw fetchError;

    const currentOnboarding = currentPrefs.feature_flags?.onboarding || {};

    // Update onboarding progress
    const updatedOnboarding = {
      ...currentOnboarding,
      current_step: step_number,
      last_updated: new Date().toISOString(),
      steps_completed: [
        ...(currentOnboarding.steps_completed || []),
        {
          step: step_number,
          completed_at: new Date().toISOString(),
          data: step_data || {}
        }
      ]
    };

    const updatedFeatureFlags = {
      ...currentPrefs.feature_flags,
      onboarding: updatedOnboarding
    };

    // Update user preferences
    const { error: updateError } = await supabase
      .from('user_preferences')
      .update({
        feature_flags: updatedFeatureFlags,
        updated_at: new Date().toISOString()
      })
      .eq('user_id', user.id);

    if (updateError) throw updateError;

    return {
      statusCode: 200,
      headers,
      body: JSON.stringify({
        success: true,
        updated: updatedOnboarding
      })
    };
  } catch (error) {
    console.error('Error updating onboarding progress:', error);
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({
        success: false,
        error: error.message
      })
    };
  }
};

// Complete onboarding
const completeOnboarding = async (event) => {
  try {
    const user = await getUserFromToken(event.headers.authorization);
    const { session_id, completion_data } = JSON.parse(event.body);

    if (!session_id) {
      throw new Error('session_id is required');
    }

    // Get current user preferences
    const { data: currentPrefs, error: fetchError } = await supabase
      .from('user_preferences')
      .select('*')
      .eq('user_id', user.id)
      .single();

    if (fetchError) throw fetchError;

    const currentOnboarding = currentPrefs.feature_flags?.onboarding || {};
    const startTime = new Date(currentOnboarding.started_at);
    const endTime = new Date();
    const totalTime = endTime.getTime() - startTime.getTime();

    // Mark onboarding as completed
    const completedOnboarding = {
      ...currentOnboarding,
      completed: true,
      completed_at: endTime.toISOString(),
      total_time_ms: totalTime,
      completion_data: completion_data || {}
    };

    const updatedFeatureFlags = {
      ...currentPrefs.feature_flags,
      onboarding: completedOnboarding
    };

    // Update user preferences
    const { error: updateError } = await supabase
      .from('user_preferences')
      .update({
        feature_flags: updatedFeatureFlags,
        updated_at: new Date().toISOString()
      })
      .eq('user_id', user.id);

    if (updateError) throw updateError;

    // Get updated preferences
    const { data: updatedPrefs, error: prefError } = await supabase
      .from('user_preferences')
      .select('*')
      .eq('user_id', user.id)
      .single();

    if (prefError) {
      console.warn('Could not fetch updated preferences:', prefError);
    }

    return {
      statusCode: 200,
      headers,
      body: JSON.stringify({
        success: true,
        completed: completedOnboarding,
        preferences: updatedPrefs
      })
    };
  } catch (error) {
    console.error('Error completing onboarding:', error);
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({
        success: false,
        error: error.message
      })
    };
  }
};

// Get onboarding status
const getOnboardingStatus = async (event) => {
  try {
    const user = await getUserFromToken(event.headers.authorization);

    // Get user preferences
    const { data: preferences, error: prefError } = await supabase
      .from('user_preferences')
      .select('*')
      .eq('user_id', user.id)
      .single();

    if (prefError && prefError.code !== 'PGRST116') {
      throw prefError;
    }

    const onboardingData = preferences?.feature_flags?.onboarding || {};
    const isCompleted = onboardingData.completed || false;
    const currentStep = onboardingData.current_step || 1;

    // Determine if there's an active session
    const hasActiveSession = onboardingData.session_id && !isCompleted;

    return {
      statusCode: 200,
      headers,
      body: JSON.stringify({
        success: true,
        onboarding_completed: isCompleted,
        current_step: currentStep,
        current_session: hasActiveSession ? {
          id: onboardingData.session_id,
          started_at: onboardingData.started_at,
          current_step: currentStep,
          completion_path: onboardingData.completion_data || {}
        } : null,
        preferences,
        onboarding_data: onboardingData
      })
    };
  } catch (error) {
    console.error('Error getting onboarding status:', error);
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({
        success: false,
        error: error.message
      })
    };
  }
};

// Get onboarding analytics (for admin/analytics)
const getOnboardingAnalytics = async (event) => {
  try {
    const user = await getUserFromToken(event.headers.authorization);

    // Get user preferences to extract onboarding data
    const { data: preferences, error } = await supabase
      .from('user_preferences')
      .select('*')
      .eq('user_id', user.id)
      .single();

    if (error) throw error;

    const onboardingData = preferences?.feature_flags?.onboarding || {};
    const stepsCompleted = onboardingData.steps_completed || [];

    // Calculate basic metrics from the onboarding data
    const isCompleted = onboardingData.completed || false;
    const totalTime = onboardingData.total_time_ms || 0;
    const startTime = onboardingData.started_at;
    const completionTime = onboardingData.completed_at;

    return {
      statusCode: 200,
      headers,
      body: JSON.stringify({
        success: true,
        analytics: {
          total_sessions: 1, // Single session per user in this simplified model
          completed_sessions: isCompleted ? 1 : 0,
          completion_rate: isCompleted ? 1 : 0,
          average_completion_time_ms: totalTime,
          session_data: {
            session_id: onboardingData.session_id,
            started_at: startTime,
            completed_at: completionTime,
            total_time_ms: totalTime,
            steps_completed: stepsCompleted.length,
            current_step: onboardingData.current_step,
            completion_data: onboardingData.completion_data,
            is_completed: isCompleted
          }
        }
      })
    };
  } catch (error) {
    console.error('Error getting onboarding analytics:', error);
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({
        success: false,
        error: error.message
      })
    };
  }
};

// Main handler
exports.handler = async (event, context) => {
  // Handle CORS preflight
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers,
      body: ''
    };
  }

  const path = event.path.replace('/.netlify/functions/onboarding', '') || '/';
  const method = event.httpMethod;

  try {
    switch (`${method} ${path}`) {
      case 'POST /initialize':
        return await initializeOnboarding(event);
      case 'PUT /progress':
        return await updateProgress(event);
      case 'POST /complete':
        return await completeOnboarding(event);
      case 'GET /status':
        return await getOnboardingStatus(event);
      case 'GET /analytics':
        return await getOnboardingAnalytics(event);
      default:
        return {
          statusCode: 404,
          headers,
          body: JSON.stringify({
            success: false,
            error: 'Endpoint not found'
          })
        };
    }
  } catch (error) {
    console.error('Unhandled error in onboarding handler:', error);
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({
        success: false,
        error: 'Internal server error'
      })
    };
  }
};
