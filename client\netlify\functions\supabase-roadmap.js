// Supabase Roadmap API
// This function connects to Supabase and fetches the roadmap data
const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const initSupabase = () => {
  const supabaseUrl = process.env.SUPABASE_URL;
  const supabaseKey = process.env.SUPABASE_SERVICE_KEY || process.env.SUPABASE_ANON_KEY;
  
  if (!supabaseUrl || !supabaseKey) {
    throw new Error('Missing Supabase credentials. Please set SUPABASE_URL and SUPABASE_SERVICE_KEY environment variables.');
  }
  
  return createClient(supabaseUrl, supabaseKey);
};

// Function to calculate stats
function calculateStats(phases) {
  let totalTasks = 0;
  let completedTasks = 0;
  let phaseStats = [];

  phases.forEach(phase => {
    let phaseTotalTasks = 0;
    let phaseCompletedTasks = 0;
    
    phase.sections.forEach(section => {
      phaseTotalTasks += section.tasks.length;
      phaseCompletedTasks += section.tasks.filter(task => task.completed).length;
    });
    
    totalTasks += phaseTotalTasks;
    completedTasks += phaseCompletedTasks;
    
    phaseStats.push({
      id: phase.id,
      title: phase.title,
      timeframe: phase.timeframe,
      progress: phaseTotalTasks > 0 ? Math.round((phaseCompletedTasks / phaseTotalTasks) * 100) : 0
    });
  });

  return {
    totalTasks,
    completedTasks,
    progressPercentage: totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0,
    phases: phaseStats
  };
}

exports.handler = async function(event, context) {
  // Set CORS headers for all responses
  const headers = {
    'Access-Control-Allow-Origin': '*', // Allow any origin
    'Access-Control-Allow-Headers': 'Content-Type',
    'Access-Control-Allow-Methods': 'GET, OPTIONS'
  };
  
  // Handle OPTIONS request for CORS preflight
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers,
      body: ''
    };
  }
  
  try {
    // Initialize Supabase client
    const supabase = initSupabase();
    
    // Try to fetch roadmap data from Supabase
    const { data: roadmapData, error: roadmapError } = await supabase
      .from('roadmap')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(1);
    
    if (roadmapError) {
      console.error('Error fetching roadmap from Supabase:', roadmapError);
      throw roadmapError;
    }
    
    // Check if we have data
    if (!roadmapData || roadmapData.length === 0) {
      // Fall back to the RPC function
      const { data: rpcData, error: rpcError } = await supabase.rpc('get_roadmap');
      
      if (rpcError) {
        console.error('Error calling get_roadmap RPC:', rpcError);
        throw rpcError;
      }
      
      if (!rpcData) {
        throw new Error('No roadmap data found in Supabase');
      }
      
      // Calculate stats
      const stats = calculateStats(rpcData);
      
      // Return the data
      return {
        statusCode: 200,
        headers: {
          ...headers,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          success: true,
          data: rpcData,
          stats: stats,
          source: 'supabase-rpc'
        })
      };
    }
    
    // Process the roadmap data
    const roadmap = roadmapData[0].data;
    
    // Calculate stats
    const stats = calculateStats(roadmap);
    
    // Return the data
    return {
      statusCode: 200,
      headers: {
        ...headers,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        success: true,
        data: roadmap,
        stats: stats,
        source: 'supabase-direct'
      })
    };
  } catch (error) {
    console.error('Error in supabase-roadmap function:', error);
    
    return {
      statusCode: 500,
      headers: {
        ...headers,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        success: false,
        error: error.message,
        // Include fallback data
        data: require('../../static-roadmap.json').data,
        stats: require('../../static-roadmap.json').stats,
        source: 'static-fallback'
      })
    };
  }
};
