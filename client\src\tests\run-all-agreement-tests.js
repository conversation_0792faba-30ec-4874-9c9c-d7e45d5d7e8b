#!/usr/bin/env node

/**
 * Master Test Runner for All Agreement System Tests
 * 
 * This script runs all comprehensive agreement system tests including:
 * - CoG/VOTA scenario testing
 * - Document comparison validation
 * - Comprehensive agreement validation
 * - Integration flow testing
 */

import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import { spawn } from 'child_process';
import fs from 'fs';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

console.log('🚀 MASTER AGREEMENT SYSTEM TEST RUNNER');
console.log('======================================\n');

// Test suite configuration
const testSuites = [
  {
    name: 'CoG/VOTA Scenario Tests',
    file: 'cog-vota-scenario.test.js',
    description: 'Tests the specific City of Gamers alliance and Village of The Ages venture scenario',
    priority: 1,
    timeout: 120000 // 2 minutes
  },
  {
    name: 'Document Comparison Tests',
    file: 'agreement-document-comparison.test.js',
    description: 'Validates generated agreements against lawyer-approved template format',
    priority: 2,
    timeout: 90000 // 1.5 minutes
  },
  {
    name: 'Comprehensive Validation Tests',
    file: 'comprehensive-agreement-validation.test.js',
    description: 'Full system validation including dynamic contributor management',
    priority: 3,
    timeout: 180000 // 3 minutes
  }
];

const config = {
  outputDir: join(__dirname, 'output', 'master-test-results'),
  logFile: join(__dirname, 'output', 'master-test-execution.log'),
  summaryFile: join(__dirname, 'output', 'master-test-summary.md'),
  verbose: true,
  stopOnFailure: false,
  generateSummary: true
};

// Ensure output directory exists
if (!fs.existsSync(config.outputDir)) {
  fs.mkdirSync(config.outputDir, { recursive: true });
}

console.log('📋 Master Test Configuration:');
console.log(`   Test Suites: ${testSuites.length}`);
console.log(`   Output Directory: ${config.outputDir}`);
console.log(`   Stop on Failure: ${config.stopOnFailure}`);
console.log(`   Generate Summary: ${config.generateSummary}`);
console.log(`   Verbose Output: ${config.verbose}\n`);

// Test execution results
const testResults = [];
let masterLog = '';

// Function to run a single test suite
function runTestSuite(testSuite) {
  return new Promise((resolve, reject) => {
    const startTime = Date.now();
    let output = '';
    let errorOutput = '';
    
    console.log(`🧪 Running: ${testSuite.name}`);
    console.log(`   File: ${testSuite.file}`);
    console.log(`   Description: ${testSuite.description}`);
    console.log(`   Timeout: ${testSuite.timeout / 1000}s\n`);
    
    const testFilePath = join(__dirname, testSuite.file);
    
    // Check if test file exists
    if (!fs.existsSync(testFilePath)) {
      const error = `Test file not found: ${testFilePath}`;
      console.error(`   ❌ ${error}\n`);
      resolve({
        name: testSuite.name,
        file: testSuite.file,
        success: false,
        error,
        duration: 0,
        output: '',
        errorOutput: error
      });
      return;
    }
    
    // Spawn the test process
    const testProcess = spawn('node', [testFilePath], {
      cwd: __dirname,
      stdio: ['pipe', 'pipe', 'pipe'],
      env: { ...process.env, NODE_ENV: 'test' }
    });
    
    // Capture stdout
    testProcess.stdout.on('data', (data) => {
      const text = data.toString();
      output += text;
      if (config.verbose) {
        process.stdout.write(text);
      }
    });
    
    // Capture stderr
    testProcess.stderr.on('data', (data) => {
      const text = data.toString();
      errorOutput += text;
      if (config.verbose) {
        process.stderr.write(text);
      }
    });
    
    // Handle process completion
    testProcess.on('close', (code) => {
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      const result = {
        name: testSuite.name,
        file: testSuite.file,
        success: code === 0,
        exitCode: code,
        duration,
        output,
        errorOutput
      };
      
      const status = result.success ? '✅ PASSED' : '❌ FAILED';
      console.log(`   ${status} (${duration}ms)\n`);
      
      resolve(result);
    });
    
    // Handle process errors
    testProcess.on('error', (error) => {
      const duration = Date.now() - startTime;
      console.error(`   ❌ PROCESS ERROR: ${error.message}\n`);
      
      resolve({
        name: testSuite.name,
        file: testSuite.file,
        success: false,
        error: error.message,
        duration,
        output,
        errorOutput: errorOutput + error.message
      });
    });
    
    // Set timeout
    const timeoutId = setTimeout(() => {
      testProcess.kill('SIGTERM');
      console.error(`   ⏰ TIMEOUT after ${testSuite.timeout / 1000}s\n`);
      
      resolve({
        name: testSuite.name,
        file: testSuite.file,
        success: false,
        timeout: true,
        duration: testSuite.timeout,
        output,
        errorOutput: errorOutput + 'Test timed out'
      });
    }, testSuite.timeout);
    
    // Clear timeout on completion
    testProcess.on('close', () => {
      clearTimeout(timeoutId);
    });
  });
}

// Function to generate master summary report
function generateMasterSummary(results) {
  const summary = {
    totalSuites: results.length,
    passedSuites: results.filter(r => r.success).length,
    failedSuites: results.filter(r => !r.success).length,
    totalDuration: results.reduce((sum, r) => sum + r.duration, 0),
    averageDuration: 0
  };
  
  summary.averageDuration = summary.totalDuration / summary.totalSuites;
  
  const report = `# Master Agreement System Test Summary
## Generated: ${new Date().toISOString()}

### Overall Results
- **Total Test Suites**: ${summary.totalSuites}
- **Passed**: ${summary.passedSuites}
- **Failed**: ${summary.failedSuites}
- **Success Rate**: ${Math.round((summary.passedSuites / summary.totalSuites) * 100)}%
- **Total Duration**: ${summary.totalDuration}ms (${(summary.totalDuration / 1000).toFixed(1)}s)
- **Average Duration**: ${Math.round(summary.averageDuration)}ms

### Test Suite Results
${results.map(result => `
#### ${result.name}
- **File**: ${result.file}
- **Status**: ${result.success ? '✅ PASSED' : '❌ FAILED'}
- **Duration**: ${result.duration}ms
${result.exitCode !== undefined ? `- **Exit Code**: ${result.exitCode}` : ''}
${result.timeout ? '- **Result**: Timed out' : ''}
${result.error ? `- **Error**: ${result.error}` : ''}
`).join('')}

### Execution Log Summary
${results.map(result => `
#### ${result.name} Output
\`\`\`
${result.output.slice(-1000)}${result.output.length > 1000 ? '\n... (truncated)' : ''}
\`\`\`

${result.errorOutput ? `#### ${result.name} Errors
\`\`\`
${result.errorOutput.slice(-500)}${result.errorOutput.length > 500 ? '\n... (truncated)' : ''}
\`\`\`` : ''}
`).join('')}

### Recommendations
${summary.failedSuites === 0 ? 
  '🎉 All test suites passed successfully! The agreement system is ready for production use.' : 
  `⚠️ ${summary.failedSuites} test suite(s) failed. Review the errors above and fix issues before production deployment.`
}

### Next Steps
${summary.failedSuites === 0 ? 
  `- Review generated agreements in individual test output directories
- Validate agreement content against business requirements
- Deploy to staging environment for final testing` :
  `- Fix failing test suites
- Re-run master test suite
- Review error logs for specific issues`
}
`;

  return report;
}

// Main execution function
async function runAllTests() {
  console.log('⏱️  Starting master test execution...\n');
  const masterStartTime = Date.now();
  
  // Sort test suites by priority
  const sortedTestSuites = [...testSuites].sort((a, b) => a.priority - b.priority);
  
  // Run each test suite
  for (const testSuite of sortedTestSuites) {
    const result = await runTestSuite(testSuite);
    testResults.push(result);
    
    // Add to master log
    masterLog += `\n=== ${testSuite.name} ===\n`;
    masterLog += `Status: ${result.success ? 'PASSED' : 'FAILED'}\n`;
    masterLog += `Duration: ${result.duration}ms\n`;
    if (result.error) {
      masterLog += `Error: ${result.error}\n`;
    }
    masterLog += `Output:\n${result.output}\n`;
    if (result.errorOutput) {
      masterLog += `Errors:\n${result.errorOutput}\n`;
    }
    masterLog += '\n';
    
    // Stop on failure if configured
    if (!result.success && config.stopOnFailure) {
      console.log('🛑 Stopping execution due to test failure (stopOnFailure=true)\n');
      break;
    }
  }
  
  const masterEndTime = Date.now();
  const masterDuration = masterEndTime - masterStartTime;
  
  // Calculate final results
  const passedSuites = testResults.filter(r => r.success).length;
  const failedSuites = testResults.filter(r => !r.success).length;
  const successRate = Math.round((passedSuites / testResults.length) * 100);
  
  console.log('📊 MASTER TEST EXECUTION COMPLETE');
  console.log('=================================');
  console.log(`⏱️  Total Duration: ${masterDuration}ms (${(masterDuration / 1000).toFixed(1)}s)`);
  console.log(`📈 Results: ${passedSuites}/${testResults.length} passed (${successRate}%)`);
  console.log(`✅ Passed: ${passedSuites}`);
  console.log(`❌ Failed: ${failedSuites}`);
  
  if (failedSuites > 0) {
    console.log('\n❌ Failed Test Suites:');
    testResults.filter(r => !r.success).forEach(result => {
      console.log(`   - ${result.name}: ${result.error || 'Unknown error'}`);
    });
  }
  
  // Save master log
  try {
    fs.writeFileSync(config.logFile, masterLog);
    console.log(`\n📄 Master log saved: ${config.logFile}`);
  } catch (logError) {
    console.warn('⚠️  Failed to save master log:', logError.message);
  }
  
  // Generate and save summary report
  if (config.generateSummary) {
    try {
      const summaryReport = generateMasterSummary(testResults);
      fs.writeFileSync(config.summaryFile, summaryReport);
      console.log(`📄 Summary report saved: ${config.summaryFile}`);
    } catch (summaryError) {
      console.warn('⚠️  Failed to save summary report:', summaryError.message);
    }
  }
  
  // List all output directories
  console.log('\n📁 Test Output Directories:');
  const outputDirs = [
    'output/cog-vota-scenario',
    'output/document-comparison', 
    'output/agreement-validation',
    'output/comprehensive-agreement-tests'
  ];
  
  outputDirs.forEach(dir => {
    const fullPath = join(__dirname, dir);
    if (fs.existsSync(fullPath)) {
      try {
        const files = fs.readdirSync(fullPath);
        console.log(`   📂 ${dir}: ${files.length} files`);
      } catch (error) {
        console.log(`   📂 ${dir}: Error reading directory`);
      }
    } else {
      console.log(`   📂 ${dir}: Not found`);
    }
  });
  
  console.log(`\n📁 Master results: ${config.outputDir}`);
  
  // Final status
  if (failedSuites === 0) {
    console.log('\n🎉 ALL TESTS PASSED! Agreement system is ready for production.');
    process.exit(0);
  } else {
    console.log('\n⚠️  SOME TESTS FAILED. Review errors and fix issues before production.');
    process.exit(1);
  }
}

// Handle process signals
process.on('SIGINT', () => {
  console.log('\n⚠️  Master test execution interrupted by user');
  process.exit(130);
});

process.on('SIGTERM', () => {
  console.log('\n⚠️  Master test execution terminated');
  process.exit(143);
});

// Run all tests
runAllTests().catch(error => {
  console.error('💥 Unexpected error in master test runner:', error);
  process.exit(1);
});
