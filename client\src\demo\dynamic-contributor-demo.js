/**
 * Dynamic Contributor Management Demonstration
 * 
 * Shows how the enhanced system supports:
 * 1. Core team with fixed revenue shares
 * 2. Dynamic gigwork contributors joining over time
 * 3. Tranche-based revenue distribution
 * 4. Contribution point systems for fair revenue allocation
 */

import { dynamicContributorManagement } from '../utils/agreement/dynamicContributorManagement.js';

console.log('🚀 DYNAMIC CONTRIBUTOR MANAGEMENT DEMONSTRATION\n');
console.log('=' .repeat(80));

// ============================================================================
// SCENARIO: SCALABLE SOFTWARE VENTURE
// ============================================================================

console.log('1️⃣ INITIALIZING SCALABLE SOFTWARE VENTURE');

// Initialize venture with core team and scalability settings
const scalableVenture = dynamicContributorManagement.initializeScalableVenture({
  name: 'CloudSync Pro - Scalable Development',
  description: 'Enterprise cloud platform built with core team + dynamic gigwork contributors',
  allianceId: 'alliance_scalable_001',
  
  // Scalability configuration
  maxContributors: 25,
  allowDynamicJoining: true,
  requireApprovalForNewContributors: true,
  autoGenerateAgreements: true,
  
  // Revenue model for scalable team
  revenueModel: {
    calculationMethod: 'hybrid', // Fixed for core team + contribution-based for gigwork
    coreTeamReservedPercentage: 50, // 50% reserved for core team
    gigworkPoolPercentage: 40,     // 40% for gigwork contributors
    platformFeePercentage: 10,     // 10% platform fee
    contributionPointsWeight: 0.8,  // 80% based on contribution points
    timeParticipationWeight: 0.2    // 20% based on time participation
  },
  
  // Tranche configuration for release-based revenue
  trancheConfig: {
    trancheType: 'release',
    trancheDuration: 90, // 3-month release cycles
    revenueDistributionDelay: 30, // Distribute revenue 30 days after release
    allowRetrospectiveJoining: false
  },
  
  // Core team with fixed revenue shares
  coreTeam: [
    {
      email: '<EMAIL>',
      role: 'Lead Developer',
      revenueShare: 20, // 20% of total venture revenue
      responsibilities: ['Architecture', 'Code review', 'Team leadership'],
      ipRights: 'co_owner'
    },
    {
      email: '<EMAIL>',
      role: 'Lead Designer',
      revenueShare: 15, // 15% of total venture revenue
      responsibilities: ['UI/UX design', 'Design system', 'User research'],
      ipRights: 'co_owner'
    },
    {
      email: '<EMAIL>',
      role: 'DevOps Lead',
      revenueShare: 15, // 15% of total venture revenue
      responsibilities: ['Infrastructure', 'CI/CD', 'Security'],
      ipRights: 'co_owner'
    }
  ],
  
  autoStartFirstTranche: true
});

console.log(`   ✅ Venture: ${scalableVenture.name}`);
console.log(`   👥 Core Team: ${scalableVenture.contributorPools.coreTeam.length} members`);
console.log(`   💰 Core Team Reserved: ${scalableVenture.revenueModel.coreTeamReservedPercentage}%`);
console.log(`   🎯 Gigwork Pool: ${scalableVenture.revenueModel.gigworkPoolPercentage}%`);
console.log(`   📅 Tranche Duration: ${scalableVenture.trancheConfig.trancheDuration} days`);
console.log(`   🔧 Max Contributors: ${scalableVenture.scalabilityConfig.maxContributors}\n`);

// ============================================================================
// SCENARIO: FIRST RELEASE TRANCHE (v1.0)
// ============================================================================

console.log('2️⃣ STARTING FIRST RELEASE TRANCHE (v1.0)');

// Get the auto-created first tranche
const firstTranche = Array.from(dynamicContributorManagement.tranches.values())[0];

console.log(`   📦 Tranche: ${firstTranche.name} (${firstTranche.version})`);
console.log(`   📅 Start Date: ${new Date(firstTranche.startDate).toLocaleDateString()}`);
console.log(`   📅 Planned End: ${new Date(firstTranche.plannedEndDate).toLocaleDateString()}`);
console.log(`   👥 Active Contributors: ${firstTranche.activeContributors.length} (core team auto-added)\n`);

// ============================================================================
// SCENARIO: ADDING GIGWORK CONTRIBUTORS DYNAMICALLY
// ============================================================================

console.log('3️⃣ ADDING GIGWORK CONTRIBUTORS FROM PLATFORM');

// Add first gigwork contributor - Frontend specialist
const frontendDev = dynamicContributorManagement.addGigworkContributor(scalableVenture.id, {
  email: '<EMAIL>',
  role: 'Frontend Developer',
  skills: ['React', 'TypeScript', 'CSS'],
  experienceLevel: 'senior',
  expectedContributionLevel: 'high',
  platformRating: 4.8,
  completedProjects: 15,
  participationModel: 'tranche_based',
  responsibilities: ['Frontend components', 'State management', 'Performance optimization']
});

// Add second gigwork contributor - Backend specialist
const backendDev = dynamicContributorManagement.addGigworkContributor(scalableVenture.id, {
  email: '<EMAIL>',
  role: 'Backend Developer',
  skills: ['Node.js', 'PostgreSQL', 'API Design'],
  experienceLevel: 'intermediate',
  expectedContributionLevel: 'medium',
  platformRating: 4.5,
  completedProjects: 8,
  participationModel: 'tranche_based',
  responsibilities: ['API development', 'Database optimization', 'Integration testing']
});

// Add third gigwork contributor - QA specialist
const qaDev = dynamicContributorManagement.addGigworkContributor(scalableVenture.id, {
  email: '<EMAIL>',
  role: 'QA Engineer',
  skills: ['Test Automation', 'Cypress', 'Jest'],
  experienceLevel: 'intermediate',
  expectedContributionLevel: 'medium',
  platformRating: 4.3,
  completedProjects: 12,
  participationModel: 'tranche_based',
  responsibilities: ['Test automation', 'Quality assurance', 'Bug reporting']
});

console.log(`   ✅ Added Frontend Dev: ${frontendDev.email} (${frontendDev.experienceLevel}, rating: ${frontendDev.gigworkProfile.platformRating})`);
console.log(`   ✅ Added Backend Dev: ${backendDev.email} (${backendDev.experienceLevel}, rating: ${backendDev.gigworkProfile.platformRating})`);
console.log(`   ✅ Added QA Engineer: ${qaDev.email} (${qaDev.experienceLevel}, rating: ${qaDev.gigworkProfile.platformRating})`);
console.log(`   👥 Total Contributors: ${Object.values(scalableVenture.contributorPools).flat().length}`);
console.log(`   🎯 Gigwork Contributors: ${scalableVenture.contributorPools.gigwork.length}\n`);

// ============================================================================
// SCENARIO: RECORDING CONTRIBUTIONS DURING TRANCHE
// ============================================================================

console.log('4️⃣ RECORDING CONTRIBUTIONS DURING v1.0 TRANCHE');

// Record various contributions from different contributors
const contributions = [
  // Core team contributions
  {
    contributor: '<EMAIL>',
    type: 'code',
    description: 'Backend API architecture and core services',
    hoursWorked: 40,
    difficultyLevel: 'expert',
    qualityRating: 5
  },
  {
    contributor: '<EMAIL>',
    type: 'design',
    description: 'Complete UI/UX design system and user flows',
    hoursWorked: 35,
    difficultyLevel: 'hard',
    qualityRating: 5
  },
  
  // Gigwork contributions
  {
    contributor: '<EMAIL>',
    type: 'code',
    description: 'React components and frontend state management',
    hoursWorked: 45,
    difficultyLevel: 'hard',
    qualityRating: 4
  },
  {
    contributor: '<EMAIL>',
    type: 'code',
    description: 'Database schema and API endpoints',
    hoursWorked: 30,
    difficultyLevel: 'medium',
    qualityRating: 4
  },
  {
    contributor: '<EMAIL>',
    type: 'testing',
    description: 'Automated test suite and quality assurance',
    hoursWorked: 25,
    difficultyLevel: 'medium',
    qualityRating: 4
  }
];

let totalContributionPoints = 0;
contributions.forEach((contrib, index) => {
  // Find contributor ID by email
  const allContributors = Object.values(scalableVenture.contributorPools).flat();
  const contributor = allContributors.find(c => c.email === contrib.contributor);
  
  if (contributor) {
    const contribution = dynamicContributorManagement.recordContribution(
      firstTranche.id,
      contributor.id,
      contrib
    );
    
    totalContributionPoints += contribution.finalPoints;
    
    console.log(`   📊 ${contrib.contributor}: ${contribution.finalPoints.toFixed(1)} points (${contrib.hoursWorked}h, ${contrib.difficultyLevel})`);
  }
});

console.log(`   🎯 Total Contribution Points: ${totalContributionPoints.toFixed(1)}\n`);

// ============================================================================
// SCENARIO: COMPLETING TRANCHE AND CALCULATING REVENUE
// ============================================================================

console.log('5️⃣ COMPLETING v1.0 TRANCHE AND CALCULATING REVENUE');

// Simulate tranche completion with revenue
const trancheRevenue = 150000; // $150k revenue for v1.0 release

const revenueDistribution = dynamicContributorManagement.calculateTrancheRevenueDistribution(
  firstTranche.id,
  trancheRevenue
);

console.log(`   💰 Total Revenue: $${revenueDistribution.totalRevenue.toLocaleString()}`);
console.log(`   🏢 Platform Fee (${scalableVenture.revenueModel.platformFeePercentage}%): $${revenueDistribution.platformFee.toLocaleString()}`);
console.log(`   👑 Core Team Pool (${scalableVenture.revenueModel.coreTeamReservedPercentage}%): $${revenueDistribution.coreTeamPool.toLocaleString()}`);
console.log(`   🎯 Gigwork Pool (${scalableVenture.revenueModel.gigworkPoolPercentage}%): $${revenueDistribution.gigworkPool.toLocaleString()}`);
console.log(`   📊 Total Contribution Points: ${revenueDistribution.totalContributionPoints.toFixed(1)}\n`);

console.log('   💵 CORE TEAM DISTRIBUTION (Fixed Percentages):');
Object.entries(revenueDistribution.coreTeamDistribution).forEach(([contributorId, amount]) => {
  const contributor = scalableVenture.contributorPools.coreTeam.find(c => c.id === contributorId);
  if (contributor) {
    console.log(`      ${contributor.email}: $${amount.toLocaleString()} (${contributor.fixedRevenueShare}% fixed share)`);
  }
});

console.log('\n   🎯 GIGWORK DISTRIBUTION (Contribution-Based):');
Object.entries(revenueDistribution.gigworkDistribution).forEach(([contributorId, amount]) => {
  const contributor = scalableVenture.contributorPools.gigwork.find(c => c.id === contributorId);
  if (contributor) {
    const points = firstTranche.contributionPoints.get(contributorId) || 0;
    const percentage = ((points / revenueDistribution.totalContributionPoints) * 100).toFixed(1);
    console.log(`      ${contributor.email}: $${amount.toLocaleString()} (${points.toFixed(1)} points, ${percentage}%)`);
  }
});

// ============================================================================
// SCENARIO: STARTING SECOND TRANCHE WITH NEW CONTRIBUTORS
// ============================================================================

console.log('\n6️⃣ STARTING v2.0 TRANCHE WITH ADDITIONAL CONTRIBUTORS');

// Create second tranche
const secondTranche = dynamicContributorManagement.createTranche(scalableVenture.id, {
  name: 'Advanced Features Release',
  description: 'v2.0 with advanced collaboration features and mobile app',
  version: 'v2.0',
  autoStart: true,
  deliverables: [
    'Mobile application',
    'Advanced collaboration tools',
    'Enterprise integrations'
  ]
});

// Add new gigwork contributor for mobile development
const mobileDev = dynamicContributorManagement.addGigworkContributor(scalableVenture.id, {
  email: '<EMAIL>',
  role: 'Mobile Developer',
  skills: ['React Native', 'iOS', 'Android'],
  experienceLevel: 'expert',
  expectedContributionLevel: 'high',
  platformRating: 4.9,
  completedProjects: 20,
  participationModel: 'tranche_based',
  responsibilities: ['Mobile app development', 'Cross-platform optimization', 'App store deployment']
});

console.log(`   📦 New Tranche: ${secondTranche.name} (${secondTranche.version})`);
console.log(`   ✅ Added Mobile Dev: ${mobileDev.email} (${mobileDev.experienceLevel}, rating: ${mobileDev.gigworkProfile.platformRating})`);
console.log(`   👥 Total Contributors Now: ${Object.values(scalableVenture.contributorPools).flat().length}`);
console.log(`   🎯 Active in v2.0: Core team + ${scalableVenture.contributorPools.gigwork.length} gigwork contributors\n`);

// ============================================================================
// SCENARIO: DEMONSTRATING FLEXIBLE PARTICIPATION
// ============================================================================

console.log('7️⃣ DEMONSTRATING FLEXIBLE PARTICIPATION');

console.log('   📋 CONTRIBUTOR PARTICIPATION SUMMARY:');
console.log('   Core Team (Continuous Participation):');
scalableVenture.contributorPools.coreTeam.forEach(member => {
  console.log(`      ${member.email}: ${member.fixedRevenueShare}% fixed share, active in all tranches`);
});

console.log('\n   Gigwork Contributors (Tranche-Based):');
scalableVenture.contributorPools.gigwork.forEach(member => {
  const v1Points = firstTranche.contributionPoints.get(member.id) || 0;
  const v1Revenue = revenueDistribution.gigworkDistribution[member.id] || 0;
  console.log(`      ${member.email}: ${v1Points.toFixed(1)} points in v1.0 → $${v1Revenue.toLocaleString()}`);
});

// ============================================================================
// DEMONSTRATION SUMMARY
// ============================================================================

console.log('\n' + '=' .repeat(80));
console.log('🎉 DYNAMIC CONTRIBUTOR MANAGEMENT DEMONSTRATION COMPLETE!\n');

console.log('📊 SYSTEM CAPABILITIES DEMONSTRATED:');
console.log('   ✅ Core team with fixed revenue shares (50% reserved)');
console.log('   ✅ Dynamic gigwork contributors joining over time');
console.log('   ✅ Tranche-based revenue distribution by release periods');
console.log('   ✅ Contribution point system for fair revenue allocation');
console.log('   ✅ Flexible participation (contributors can join/leave tranches)');
console.log('   ✅ Automatic agreement generation for new contributors');
console.log('   ✅ Scalable to 25+ contributors with approval workflow\n');

console.log('💰 REVENUE DISTRIBUTION MODEL:');
console.log(`   • Core Team: ${scalableVenture.revenueModel.coreTeamReservedPercentage}% (fixed percentages)`);
console.log(`   • Gigwork Pool: ${scalableVenture.revenueModel.gigworkPoolPercentage}% (contribution-based)`);
console.log(`   • Platform Fee: ${scalableVenture.revenueModel.platformFeePercentage}%`);
console.log(`   • Calculation: ${scalableVenture.revenueModel.contributionPointsWeight * 100}% contribution points + ${scalableVenture.revenueModel.timeParticipationWeight * 100}% time participation\n`);

console.log('🚀 SCALABILITY FEATURES:');
console.log(`   • Maximum Contributors: ${scalableVenture.scalabilityConfig.maxContributors}`);
console.log(`   • Dynamic Joining: ${scalableVenture.scalabilityConfig.allowDynamicJoining ? 'Enabled' : 'Disabled'}`);
console.log(`   • Approval Required: ${scalableVenture.scalabilityConfig.requireApprovalForNewContributors ? 'Yes' : 'No'}`);
console.log(`   • Auto Agreements: ${scalableVenture.scalabilityConfig.autoGenerateAgreements ? 'Enabled' : 'Disabled'}\n`);

console.log('=' .repeat(80));
console.log('✨ ROYALTEA DYNAMIC CONTRIBUTOR SYSTEM - READY FOR GIGWORK SCALING! ✨');
console.log('=' .repeat(80));
