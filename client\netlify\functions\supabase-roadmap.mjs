// Supabase Roadmap API function using ES modules
import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client
const initSupabase = () => {
  const supabaseUrl = Netlify.env.get("SUPABASE_URL");
  const supabaseKey = Netlify.env.get("SUPABASE_SERVICE_KEY") || Netlify.env.get("SUPABASE_ANON_KEY");
  
  if (!supabaseUrl || !supabaseKey) {
    throw new Error('Missing Supabase credentials');
  }
  
  return createClient(supabaseUrl, supabaseKey);
};

// Function to calculate stats
function calculateStats(phases) {
  let totalTasks = 0;
  let completedTasks = 0;
  let phaseStats = [];

  phases.forEach(phase => {
    let phaseTotalTasks = 0;
    let phaseCompletedTasks = 0;
    
    phase.sections.forEach(section => {
      phaseTotalTasks += section.tasks.length;
      phaseCompletedTasks += section.tasks.filter(task => task.completed).length;
    });
    
    totalTasks += phaseTotalTasks;
    completedTasks += phaseCompletedTasks;
    
    phaseStats.push({
      id: phase.id,
      title: phase.title,
      timeframe: phase.timeframe,
      progress: phaseTotalTasks > 0 ? Math.round((phaseCompletedTasks / phaseTotalTasks) * 100) : 0
    });
  });

  return {
    totalTasks,
    completedTasks,
    progressPercentage: totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0,
    phases: phaseStats
  };
}

// Main function handler
export default async (req, context) => {
  // Set CORS headers for cross-origin access
  const headers = {
    'Content-Type': 'application/json',
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Allow-Methods': 'GET, OPTIONS'
  };
  
  // Handle OPTIONS request for CORS preflight
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      status: 204,
      headers
    });
  }
  
  try {
    // Initialize Supabase client
    const supabase = initSupabase();
    
    // Query the roadmap data from Supabase
    const { data: roadmapData, error: roadmapError } = await supabase
      .from('roadmap')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(1);
    
    if (roadmapError) {
      console.error('Error fetching roadmap from Supabase:', roadmapError);
      throw roadmapError;
    }
    
    // Check if we have data
    if (!roadmapData || roadmapData.length === 0) {
      // Try the RPC function as a fallback
      const { data: rpcData, error: rpcError } = await supabase.rpc('get_roadmap');
      
      if (rpcError) {
        console.error('Error calling get_roadmap RPC:', rpcError);
        throw rpcError;
      }
      
      if (!rpcData) {
        throw new Error('No roadmap data found in Supabase');
      }
      
      // Calculate stats
      const stats = calculateStats(rpcData);
      
      // Return the data
      return new Response(
        JSON.stringify({
          success: true,
          data: rpcData,
          stats: stats,
          source: 'supabase-rpc'
        }),
        { headers }
      );
    }
    
    // Process the roadmap data
    const roadmap = roadmapData[0].data;
    
    // Calculate stats
    const stats = calculateStats(roadmap);
    
    // Return the data
    return new Response(
      JSON.stringify({
        success: true,
        data: roadmap,
        stats: stats,
        source: 'supabase-direct'
      }),
      { headers }
    );
  } catch (error) {
    console.error('Error in supabase-roadmap function:', error);
    
    // Fall back to hardcoded data
    const fallbackData = [
      {
        id: 1,
        title: "Foundation & User Management",
        timeframe: "Completed",
        expanded: true,
        sections: [
          {
            id: "1.1",
            title: "Project Setup & Configuration",
            tasks: [
              { id: "1.1.1", text: "Finalize tech stack (React, Supabase)", completed: true },
              { id: "1.1.2", text: "Set up development environment", completed: true },
              { id: "1.1.3", text: "Configure Netlify deployment", completed: true }
            ]
          }
        ]
      },
      {
        id: 2,
        title: "Project Creation & Management",
        timeframe: "Phase 1",
        expanded: false,
        sections: [
          {
            id: "2.1",
            title: "Project Wizard",
            tasks: [
              { id: "2.1.1", text: "Design project creation flow", completed: true },
              { id: "2.1.2", text: "Implement project basics form", completed: true },
              { id: "2.1.3", text: "Add team & contributors section", completed: true }
            ]
          }
        ]
      },
      {
        id: 3,
        title: "Contribution Tracking System",
        timeframe: "Phase 2",
        expanded: false,
        sections: [
          {
            id: "3.1",
            title: "Manual Contribution Entry",
            tasks: [
              { id: "3.1.1", text: "Design contribution entry forms", completed: true },
              { id: "3.1.2", text: "Implement time tracking functionality", completed: true },
              { id: "3.1.3", text: "Add task selection from configured types", completed: true },
              { id: "3.1.4", text: "Implement difficulty rating selection", completed: true },
              { id: "3.1.5", text: "Create contribution description field", completed: true },
              { id: "3.1.6", text: "Add date range selection", completed: true },
              { id: "3.1.7", text: "Implement file/asset attachment", completed: false }
            ]
          }
        ]
      }
    ];
    
    // Calculate stats for fallback data
    const fallbackStats = calculateStats(fallbackData);
    
    return new Response(
      JSON.stringify({
        success: true,
        data: fallbackData,
        stats: fallbackStats,
        source: 'fallback-data',
        error: error.message
      }),
      { 
        status: 200, // Return 200 with fallback data instead of 500
        headers
      }
    );
  }
};

// Configure the function path
export const config = {
  path: "/api/supabase-roadmap"
};
