import { test, expect } from '@playwright/test';

test.describe('Dashboard Navigation - Production Site', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate directly to the production site
    await page.goto('https://royalty.technology');
    
    // Login with test credentials
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'TestPassword123!');
    await page.click('button[type="submit"]');
    
    // Wait for login to complete
    await page.waitForSelector('text=Welcome back', { timeout: 15000 });
    
    // Handle experimental navigation - check if we're in grid view
    const isGridView = await page.locator('.absolute.inset-0.z-20').isVisible();
    if (isGridView) {
      // Look for the "Back to Content" button (green button with 📄)
      const backToContentButton = page.locator('button:has-text("📄")');
      if (await backToContentButton.isVisible()) {
        await backToContentButton.click();
        await page.waitForTimeout(1000);
      }
    }
    
    // Wait for dashboard content to be fully loaded
    await page.waitForSelector('button:has-text("Start New Project")', { timeout: 10000 });
    await page.waitForTimeout(2000);
  });

  test('Dashboard buttons are clickable and trigger navigation changes', async ({ page }) => {
    const initialUrl = page.url();
    console.log(`Initial URL: ${initialUrl}`);
    
    // Test Start New Project button
    console.log('Testing Start New Project button...');
    const startButton = page.locator('button:has-text("Start New Project")');
    await expect(startButton).toBeVisible();
    await startButton.click({ force: true });
    await page.waitForTimeout(3000);
    
    let currentUrl = page.url();
    let urlChanged = currentUrl !== initialUrl;
    let hasNewContent = await page.locator('text=Project').isVisible() || 
                       await page.locator('text=Start').isVisible() ||
                       await page.locator('text=Create').isVisible();
    
    console.log(`After Start click: URL=${currentUrl}, Changed=${urlChanged}, HasContent=${hasNewContent}`);
    expect(urlChanged || hasNewContent).toBeTruthy();
    
    // Go back to dashboard
    await page.goto('https://royalty.technology');
    await page.waitForTimeout(2000);
    
    // Handle grid view again if needed
    const backToContentButton = page.locator('button:has-text("📄")');
    if (await backToContentButton.isVisible()) {
      await backToContentButton.click();
      await page.waitForTimeout(1000);
    }
    await page.waitForSelector('button:has-text("Track Contribution")', { timeout: 10000 });
    
    // Test Track Contribution button
    console.log('Testing Track Contribution button...');
    const trackButton = page.locator('button:has-text("Track Contribution")');
    await expect(trackButton).toBeVisible();
    await trackButton.click({ force: true });
    await page.waitForTimeout(3000);
    
    currentUrl = page.url();
    urlChanged = currentUrl !== 'https://royalty.technology/';
    hasNewContent = await page.locator('text=Track').isVisible() || 
                   await page.locator('text=Contribution').isVisible() ||
                   await page.locator('text=Earn').isVisible();
    
    console.log(`After Track click: URL=${currentUrl}, Changed=${urlChanged}, HasContent=${hasNewContent}`);
    expect(urlChanged || hasNewContent).toBeTruthy();
    
    // Go back to dashboard
    await page.goto('https://royalty.technology');
    await page.waitForTimeout(2000);
    
    // Handle grid view again if needed
    const backToContentButton2 = page.locator('button:has-text("📄")');
    if (await backToContentButton2.isVisible()) {
      await backToContentButton2.click();
      await page.waitForTimeout(1000);
    }
    await page.waitForSelector('button:has-text("View Analytics")', { timeout: 10000 });
    
    // Test View Analytics button
    console.log('Testing View Analytics button...');
    const analyticsButton = page.locator('button:has-text("View Analytics")');
    await expect(analyticsButton).toBeVisible();
    await analyticsButton.click({ force: true });
    await page.waitForTimeout(3000);
    
    currentUrl = page.url();
    urlChanged = currentUrl !== 'https://royalty.technology/';
    hasNewContent = await page.locator('text=Analytics').isVisible() || 
                   await page.locator('text=Insights').isVisible() ||
                   await page.locator('text=Performance').isVisible();
    
    console.log(`After Analytics click: URL=${currentUrl}, Changed=${urlChanged}, HasContent=${hasNewContent}`);
    expect(urlChanged || hasNewContent).toBeTruthy();
    
    console.log('✅ All dashboard buttons are functional!');
  });

  test('Dashboard buttons exist and are properly styled', async ({ page }) => {
    // Verify all expected buttons exist
    const startButton = page.locator('button:has-text("Start New Project")');
    const trackButton = page.locator('button:has-text("Track Contribution")');
    const analyticsButton = page.locator('button:has-text("View Analytics")');
    
    await expect(startButton).toBeVisible();
    await expect(trackButton).toBeVisible();
    await expect(analyticsButton).toBeVisible();
    
    // Verify buttons are enabled
    await expect(startButton).toBeEnabled();
    await expect(trackButton).toBeEnabled();
    await expect(analyticsButton).toBeEnabled();
    
    // Verify buttons have proper styling (gradient backgrounds)
    const startButtonClass = await startButton.getAttribute('class');
    const trackButtonClass = await trackButton.getAttribute('class');
    const analyticsButtonClass = await analyticsButton.getAttribute('class');
    
    expect(startButtonClass).toContain('bg-gradient');
    expect(trackButtonClass).toContain('bg-gradient');
    expect(analyticsButtonClass).toContain('bg-gradient');
    
    console.log('✅ All dashboard buttons are properly styled and enabled!');
  });

  test('Dashboard shows user welcome message and stats', async ({ page }) => {
    // Verify welcome message
    const welcomeMessage = page.locator('text=Welcome back');
    await expect(welcomeMessage).toBeVisible();
    
    // Verify dashboard has some stats or content
    const hasStats = await page.locator('text=Active Projects').isVisible() ||
                    await page.locator('text=Total Revenue').isVisible() ||
                    await page.locator('text=System Online').isVisible();
    
    expect(hasStats).toBeTruthy();
    
    console.log('✅ Dashboard shows proper welcome message and stats!');
  });
});
