/**
 * Comprehensive Legal Agreement Generation Unit Tests
 * 
 * This test suite performs full end-to-end validation of the legal agreement generation system
 * by comparing generated agreements against the lawyer-approved template at 
 * client/public/example-cog-contributor-agreement.md
 * 
 * Test Coverage:
 * - Multiple Alliance Types (CoG alliances, custom user alliances)
 * - Multiple Venture Types (software, creative projects, business automation)
 * - Comprehensive Structure and Content Validation
 * - Legal Formatting and Signature Block Validation
 * - Cross-jurisdiction Testing (Florida, Texas, etc.)
 * - Individual vs Company Contributors
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { NewAgreementGenerator } from '../utils/agreement/newAgreementGenerator.js';
import { LegalAccuracyValidator } from '../utils/agreement/legalAccuracyValidator.js';

// Get current directory for ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Test configuration
const TEST_CONFIG = {
  outputDirectory: path.join(__dirname, 'output', 'comprehensive-legal-validation'),
  templatePath: path.join(__dirname, '../../public/example-cog-contributor-agreement.md'),
  requirePerfectScore: true,
  saveResults: true,
  minAccuracyThreshold: 98 // Minimum 98% accuracy required for production
};

// Ensure output directory exists
if (!fs.existsSync(TEST_CONFIG.outputDirectory)) {
  fs.mkdirSync(TEST_CONFIG.outputDirectory, { recursive: true });
}

/**
 * Test Scenarios - Comprehensive coverage of alliance and venture types
 */
const TEST_SCENARIOS = {
  // CoG Alliance Scenarios
  COG_SOFTWARE_INDIVIDUAL: {
    name: 'CoG Software Development - Individual Contributor',
    alliance: {
      id: 'cog-alliance-001',
      name: 'City of Gamers Inc.',
      alliance_type: 'cog_alliance',
      industry: 'software_development',
      legal_entity_info: {
        incorporationState: 'Florida',
        entityType: 'corporation'
      },
      business_address: {
        full_address: '1205 43rd Street, Suite B, Orlando, Florida 32839',
        city: 'Orlando',
        state: 'Florida',
        county: 'Orange County',
        zip: '32839'
      },
      contact_information: {
        primaryContact: {
          name: 'Gynell Journigan',
          title: 'President',
          email: '<EMAIL>'
        }
      }
    },
    venture: {
      id: 'venture-software-001',
      name: 'Advanced Business Automation Platform',
      description: 'A comprehensive software platform for automating business processes and workflows',
      venture_type: 'software',
      projectType: 'software',
      revenueSharing: {
        model: 'unified_pool',
        contributorPercentage: 60,
        companyPercentage: 40
      },
      milestones: [
        { name: 'Core Architecture', description: 'Foundation system architecture', deadline: '2025-09-01' },
        { name: 'User Interface', description: 'Complete user interface implementation', deadline: '2025-12-01' },
        { name: 'Beta Release', description: 'Feature-complete beta version', deadline: '2026-03-01' }
      ]
    },
    contributor: {
      id: 'contributor-001',
      name: 'Sarah Johnson',
      email: '<EMAIL>',
      address: '456 Developer Lane, Tech City, Florida 33101',
      isCompany: false,
      role: 'Senior Software Developer'
    }
  },

  COG_CREATIVE_COMPANY: {
    name: 'CoG Creative Project - Company Contributor',
    alliance: {
      id: 'cog-alliance-002',
      name: 'City of Gamers Inc.',
      alliance_type: 'cog_alliance',
      industry: 'creative_media',
      legal_entity_info: {
        incorporationState: 'Florida',
        entityType: 'corporation'
      },
      business_address: {
        full_address: '1205 43rd Street, Suite B, Orlando, Florida 32839',
        city: 'Orlando',
        state: 'Florida',
        county: 'Orange County',
        zip: '32839'
      },
      contact_information: {
        primaryContact: {
          name: 'Gynell Journigan',
          title: 'President',
          email: '<EMAIL>'
        }
      }
    },
    venture: {
      id: 'venture-creative-001',
      name: 'Interactive Media Experience',
      description: 'An immersive interactive media project combining video, audio, and user interaction',
      venture_type: 'creative',
      projectType: 'creative work',
      revenueSharing: {
        model: 'tranche_based',
        contributorPercentage: 50,
        companyPercentage: 50
      },
      milestones: [
        { name: 'Concept Development', description: 'Complete creative concept and storyboard', deadline: '2025-08-15' },
        { name: 'Production Phase', description: 'Content creation and asset development', deadline: '2025-11-30' },
        { name: 'Final Delivery', description: 'Completed interactive experience', deadline: '2026-02-15' }
      ]
    },
    contributor: {
      id: 'contributor-company-001',
      name: 'Creative Studios LLC',
      companyName: 'Creative Studios LLC',
      email: '<EMAIL>',
      address: '789 Creative Boulevard, Art District, Florida 33102',
      isCompany: true,
      signerName: 'Michael Chen',
      signerTitle: 'Creative Director',
      role: 'Creative Development Partner'
    }
  },

  CUSTOM_TEXAS_SOFTWARE: {
    name: 'Custom Texas Alliance - Software Platform',
    alliance: {
      id: 'custom-alliance-001',
      name: 'Innovative Tech Solutions LLC',
      alliance_type: 'custom_user_alliance',
      industry: 'technology',
      legal_entity_info: {
        incorporationState: 'Texas',
        entityType: 'limited_liability_company'
      },
      business_address: {
        full_address: '123 Innovation Drive, Austin, Texas 78701',
        city: 'Austin',
        state: 'Texas',
        county: 'Travis County',
        zip: '78701'
      },
      contact_information: {
        primaryContact: {
          name: 'Jennifer Martinez',
          title: 'CEO',
          email: '<EMAIL>'
        }
      }
    },
    venture: {
      id: 'venture-texas-001',
      name: 'Enterprise Data Analytics Platform',
      description: 'Advanced analytics platform for enterprise data processing and visualization',
      venture_type: 'software',
      projectType: 'software',
      revenueSharing: {
        model: 'contribution_points',
        contributorPercentage: 65,
        companyPercentage: 35
      },
      milestones: [
        { name: 'Data Pipeline', description: 'Core data processing pipeline', deadline: '2025-10-01' },
        { name: 'Analytics Engine', description: 'Advanced analytics and ML capabilities', deadline: '2026-01-15' },
        { name: 'Visualization Layer', description: 'Interactive data visualization interface', deadline: '2026-04-01' }
      ]
    },
    contributor: {
      id: 'contributor-texas-001',
      name: 'Robert Kim',
      email: '<EMAIL>',
      address: '321 Tech Street, Austin, Texas 78702',
      isCompany: false,
      role: 'Lead Data Scientist'
    }
  },

  CUSTOM_CALIFORNIA_GAME: {
    name: 'Custom California Alliance - Game Development',
    alliance: {
      id: 'custom-alliance-002',
      name: 'Pacific Game Studios Inc.',
      alliance_type: 'custom_user_alliance',
      industry: 'gaming',
      legal_entity_info: {
        incorporationState: 'California',
        entityType: 'corporation'
      },
      business_address: {
        full_address: '555 Game Developer Way, San Francisco, California 94102',
        city: 'San Francisco',
        state: 'California',
        county: 'San Francisco County',
        zip: '94102'
      },
      contact_information: {
        primaryContact: {
          name: 'Alex Thompson',
          title: 'Studio Director',
          email: '<EMAIL>'
        }
      }
    },
    venture: {
      id: 'venture-game-001',
      name: 'Next-Gen Adventure Game',
      description: 'An innovative adventure game with cutting-edge graphics and immersive storytelling',
      venture_type: 'game',
      projectType: 'game',
      revenueSharing: {
        model: 'milestone_based',
        contributorPercentage: 55,
        companyPercentage: 45
      },
      milestones: [
        { name: 'Game Design Document', description: 'Complete game design and technical specifications', deadline: '2025-07-30' },
        { name: 'Prototype Build', description: 'Playable prototype with core mechanics', deadline: '2025-11-15' },
        { name: 'Alpha Release', description: 'Feature-complete alpha version', deadline: '2026-03-30' },
        { name: 'Gold Master', description: 'Final release-ready version', deadline: '2026-07-15' }
      ]
    },
    contributor: {
      id: 'contributor-game-001',
      name: 'Emma Rodriguez',
      email: '<EMAIL>',
      address: '987 Developer Circle, San Francisco, California 94103',
      isCompany: false,
      role: 'Senior Game Developer'
    }
  },

  CUSTOM_NEW_YORK_BUSINESS: {
    name: 'Custom New York Alliance - Business Automation',
    alliance: {
      id: 'custom-alliance-003',
      name: 'Metropolitan Business Solutions Corp.',
      alliance_type: 'custom_user_alliance',
      industry: 'business_services',
      legal_entity_info: {
        incorporationState: 'New York',
        entityType: 'corporation'
      },
      business_address: {
        full_address: '100 Wall Street, Suite 2500, New York, New York 10005',
        city: 'New York',
        state: 'New York',
        county: 'New York County',
        zip: '10005'
      },
      contact_information: {
        primaryContact: {
          name: 'David Wilson',
          title: 'Chief Technology Officer',
          email: '<EMAIL>'
        }
      }
    },
    venture: {
      id: 'venture-business-001',
      name: 'Automated Workflow Management System',
      description: 'Comprehensive business process automation and workflow management platform',
      venture_type: 'software',
      projectType: 'business automation',
      revenueSharing: {
        model: 'hybrid_model',
        contributorPercentage: 60,
        companyPercentage: 40
      },
      milestones: [
        { name: 'Requirements Analysis', description: 'Complete business requirements and system design', deadline: '2025-08-01' },
        { name: 'Core Platform', description: 'Basic workflow automation engine', deadline: '2025-12-15' },
        { name: 'Advanced Features', description: 'AI-powered optimization and analytics', deadline: '2026-05-01' }
      ]
    },
    contributor: {
      id: 'contributor-business-001',
      name: 'Lisa Chang',
      email: '<EMAIL>',
      address: '456 Business Avenue, New York, New York 10016',
      isCompany: false,
      role: 'Business Process Analyst'
    }
  }
};

/**
 * Template Structure Validator
 * Validates that generated agreements match the lawyer-approved template structure
 */
class TemplateStructureValidator {
  constructor(lawyerTemplate) {
    this.lawyerTemplate = lawyerTemplate;
    this.requiredSections = this.extractRequiredSections();
    this.requiredSignatureBlocks = this.extractSignatureBlocks();
  }

  extractRequiredSections() {
    const sections = [];
    const sectionRegex = /^##?\s+(.+)$/gm;
    let match;
    
    while ((match = sectionRegex.exec(this.lawyerTemplate)) !== null) {
      sections.push(match[1].trim());
    }
    
    return sections;
  }

  extractSignatureBlocks() {
    const signatureBlocks = {
      company: {
        byLine: 'By: ______________________',
        dateLine: 'Date: ______________________'
      },
      individual: {
        dateLine: 'Date: ________________________'
      },
      companyContributor: {
        byLine: 'By: _________________________',
        dateLine: 'Date: _______________________'
      }
    };
    
    return signatureBlocks;
  }

  validateStructure(generatedAgreement) {
    const errors = [];
    const warnings = [];

    // Check all required sections are present
    this.requiredSections.forEach(section => {
      if (!generatedAgreement.includes(section)) {
        errors.push(`Missing required section: "${section}"`);
      }
    });

    // Check signature block formatting
    const hasCompanySignature = generatedAgreement.includes(this.requiredSignatureBlocks.company.byLine);
    const hasIndividualSignature = generatedAgreement.includes(this.requiredSignatureBlocks.individual.dateLine);

    if (!hasCompanySignature) {
      errors.push('Missing properly formatted company signature block');
    }

    if (!hasIndividualSignature) {
      errors.push('Missing properly formatted contributor signature block');
    }

    // Check for placeholder remnants
    const placeholderPatterns = [
      /\[.*?\]/g,
      /\{.*?\}/g,
      /__+/g
    ];

    placeholderPatterns.forEach((pattern, index) => {
      const matches = generatedAgreement.match(pattern);
      if (matches && matches.length > 10) { // Allow some underscores for signature lines
        warnings.push(`Potential unreplaced placeholders found (pattern ${index + 1}): ${matches.slice(0, 3).join(', ')}...`);
      }
    });

    return { errors, warnings };
  }
}

/**
 * Content Accuracy Validator
 * Validates that content is properly substituted and legally accurate
 */
class ContentAccuracyValidator {
  validateContent(generatedAgreement, scenario) {
    const errors = [];
    const warnings = [];

    // Validate company information
    const companyValidation = this.validateCompanyInfo(generatedAgreement, scenario.alliance);
    errors.push(...companyValidation.errors);
    warnings.push(...companyValidation.warnings);

    // Validate contributor information
    const contributorValidation = this.validateContributorInfo(generatedAgreement, scenario.contributor);
    errors.push(...contributorValidation.errors);
    warnings.push(...contributorValidation.warnings);

    // Validate venture information
    const ventureValidation = this.validateVentureInfo(generatedAgreement, scenario.venture);
    errors.push(...ventureValidation.errors);
    warnings.push(...ventureValidation.warnings);

    // Validate jurisdiction information
    const jurisdictionValidation = this.validateJurisdictionInfo(generatedAgreement, scenario.alliance);
    errors.push(...jurisdictionValidation.errors);
    warnings.push(...jurisdictionValidation.warnings);

    return { errors, warnings };
  }

  validateCompanyInfo(agreement, alliance) {
    const errors = [];
    const warnings = [];

    // Check company name
    if (!agreement.includes(alliance.name)) {
      errors.push(`Company name "${alliance.name}" not found in agreement`);
    }

    // Check company address
    if (!agreement.includes(alliance.business_address.full_address)) {
      errors.push(`Company address "${alliance.business_address.full_address}" not found in agreement`);
    }

    // Check signer information
    const signer = alliance.contact_information.primaryContact;
    if (!agreement.includes(signer.name)) {
      errors.push(`Signer name "${signer.name}" not found in agreement`);
    }

    if (!agreement.includes(signer.title)) {
      errors.push(`Signer title "${signer.title}" not found in agreement`);
    }

    return { errors, warnings };
  }

  validateContributorInfo(agreement, contributor) {
    const errors = [];
    const warnings = [];

    // Check contributor name
    if (!agreement.includes(contributor.name)) {
      errors.push(`Contributor name "${contributor.name}" not found in agreement`);
    }

    // Check contributor address
    if (!agreement.includes(contributor.address)) {
      errors.push(`Contributor address "${contributor.address}" not found in agreement`);
    }

    // For company contributors, check additional fields
    if (contributor.isCompany) {
      if (contributor.companyName && !agreement.includes(contributor.companyName)) {
        errors.push(`Contributor company name "${contributor.companyName}" not found in agreement`);
      }
      
      if (contributor.signerName && !agreement.includes(contributor.signerName)) {
        errors.push(`Contributor signer name "${contributor.signerName}" not found in agreement`);
      }
      
      if (contributor.signerTitle && !agreement.includes(contributor.signerTitle)) {
        errors.push(`Contributor signer title "${contributor.signerTitle}" not found in agreement`);
      }
    }

    return { errors, warnings };
  }

  validateVentureInfo(agreement, venture) {
    const errors = [];
    const warnings = [];

    // Check venture name/title
    if (!agreement.includes(venture.name)) {
      errors.push(`Venture name "${venture.name}" not found in agreement`);
    }

    // Check venture description
    if (!agreement.includes(venture.description)) {
      errors.push(`Venture description not found in agreement`);
    }

    // Check milestones
    venture.milestones.forEach((milestone, index) => {
      if (!agreement.includes(milestone.name)) {
        warnings.push(`Milestone "${milestone.name}" not found in agreement`);
      }
    });

    return { errors, warnings };
  }

  validateJurisdictionInfo(agreement, alliance) {
    const errors = [];
    const warnings = [];

    const state = alliance.legal_entity_info.incorporationState;
    const county = alliance.business_address.county;

    // Check state jurisdiction
    if (!agreement.includes(state)) {
      errors.push(`State jurisdiction "${state}" not found in agreement`);
    }

    // Check county jurisdiction
    if (!agreement.includes(county)) {
      errors.push(`County jurisdiction "${county}" not found in agreement`);
    }

    // Check for hardcoded Florida when it should be different
    if (agreement.includes('Florida') && state !== 'Florida') {
      errors.push(`CRITICAL: Agreement contains "Florida" jurisdiction when it should be "${state}"`);
    }

    // Check for hardcoded Orange County when it should be different
    if (agreement.includes('Orange County') && county !== 'Orange County') {
      errors.push(`CRITICAL: Agreement contains "Orange County" jurisdiction when it should be "${county}"`);
    }

    return { errors, warnings };
  }
}

/**
 * Main Test Runner
 * Executes comprehensive validation tests for all scenarios
 */
class ComprehensiveLegalTestRunner {
  constructor() {
    this.agreementGenerator = new NewAgreementGenerator();
    this.legalValidator = new LegalAccuracyValidator();
    this.lawyerTemplate = null;
    this.structureValidator = null;
    this.contentValidator = null;
    this.results = [];
  }

  async initialize() {
    console.log('🎯 COMPREHENSIVE LEGAL AGREEMENT VALIDATION TESTS');
    console.log('='.repeat(60));

    // Load lawyer-approved template
    try {
      this.lawyerTemplate = fs.readFileSync(TEST_CONFIG.templatePath, 'utf8');
      console.log('✅ Lawyer-approved template loaded for validation');
    } catch (error) {
      throw new Error(`Failed to load lawyer template: ${error.message}`);
    }

    // Initialize validators
    this.structureValidator = new TemplateStructureValidator(this.lawyerTemplate);
    this.contentValidator = new ContentAccuracyValidator();

    console.log('📋 Test Configuration:');
    console.log(`   Test Scenarios: ${Object.keys(TEST_SCENARIOS).length}`);
    console.log(`   Minimum Accuracy: ${TEST_CONFIG.minAccuracyThreshold}%`);
    console.log(`   Output Directory: ${TEST_CONFIG.outputDirectory}`);
    console.log(`   Save Results: ${TEST_CONFIG.saveResults}`);
    console.log('');
  }

  async runAllTests() {
    console.log('🧪 RUNNING COMPREHENSIVE VALIDATION TESTS');
    console.log('='.repeat(50));
    console.log('');

    for (const [scenarioKey, scenario] of Object.entries(TEST_SCENARIOS)) {
      await this.runSingleTest(scenarioKey, scenario);
    }

    return this.generateSummaryReport();
  }

  async runSingleTest(scenarioKey, scenario) {
    console.log(`📝 Testing: ${scenario.name}`);
    console.log(`   Alliance: ${scenario.alliance.name} (${scenario.alliance.alliance_type})`);
    console.log(`   State: ${scenario.alliance.legal_entity_info.incorporationState}`);
    console.log(`   Venture: ${scenario.venture.name} (${scenario.venture.venture_type})`);
    console.log(`   Contributor: ${scenario.contributor.name} (${scenario.contributor.isCompany ? 'Company' : 'Individual'})`);

    try {
      // Prepare test data for agreement generation
      const testData = this.prepareTestData(scenario);

      console.log('   📋 Test data prepared for agreement generation');

      // Generate agreement
      const generatedAgreement = await this.agreementGenerator.generateAgreement(
        this.lawyerTemplate,
        testData.project,
        testData.options
      );

      console.log('   ✅ Agreement generated');

      // Perform comprehensive validation
      const validationResults = await this.performComprehensiveValidation(
        generatedAgreement,
        scenario,
        scenarioKey
      );

      // Save results if configured
      if (TEST_CONFIG.saveResults) {
        await this.saveTestResults(scenarioKey, scenario, generatedAgreement, validationResults);
      }

      // Store results for summary
      this.results.push({
        scenarioKey,
        scenario,
        validationResults,
        agreement: generatedAgreement
      });

      // Display results
      this.displayTestResults(validationResults);

    } catch (error) {
      console.log(`   ❌ Test failed: ${error.message}`);
      this.results.push({
        scenarioKey,
        scenario,
        error: error.message,
        validationResults: { accuracyScore: 0, isProductionReady: false, criticalErrors: [error.message] }
      });
    }

    console.log('');
  }

  prepareTestData(scenario) {
    // Create project data structure for NewAgreementGenerator
    const project = {
      id: scenario.venture.id,
      name: scenario.venture.name,
      title: scenario.venture.name,
      description: scenario.venture.description,
      project_type: scenario.venture.venture_type,
      projectType: scenario.venture.projectType,
      team_id: scenario.alliance.id,
      alliance_id: scenario.alliance.id,
      created_by: scenario.contributor.id,
      is_active: true,
      is_public: false
    };

    // Prepare contributor data
    const contributor = {
      id: scenario.contributor.id,
      name: scenario.contributor.name,
      email: scenario.contributor.email,
      address: scenario.contributor.address,
      isCompany: scenario.contributor.isCompany,
      role: scenario.contributor.role
    };

    // Add company-specific fields if contributor is a company
    if (scenario.contributor.isCompany) {
      contributor.companyName = scenario.contributor.companyName;
      contributor.signerName = scenario.contributor.signerName;
      contributor.signerTitle = scenario.contributor.signerTitle;
    }

    // Prepare options
    const options = {
      contributors: [contributor],
      currentUser: {
        id: scenario.contributor.id,
        email: scenario.contributor.email,
        user_metadata: {
          full_name: scenario.contributor.name
        }
      },
      fullName: scenario.contributor.name,
      royaltyModel: scenario.venture.revenueSharing,
      milestones: scenario.venture.milestones,
      allianceInfo: scenario.alliance
    };

    return { project, options };
  }

  async performComprehensiveValidation(generatedAgreement, scenario, scenarioKey) {
    // 1. Structure validation against lawyer template
    const structureValidation = this.structureValidator.validateStructure(generatedAgreement);

    // 2. Content accuracy validation
    const contentValidation = this.contentValidator.validateContent(generatedAgreement, scenario);

    // 3. Legal accuracy validation using existing validator
    const expectedData = {
      company: {
        name: scenario.alliance.name,
        address: scenario.alliance.business_address.full_address,
        state: scenario.alliance.legal_entity_info.incorporationState,
        signerName: scenario.alliance.contact_information.primaryContact.name,
        signerTitle: scenario.alliance.contact_information.primaryContact.title
      },
      contributor: {
        name: scenario.contributor.name,
        address: scenario.contributor.address,
        isCompany: scenario.contributor.isCompany
      },
      jurisdiction: {
        state: scenario.alliance.legal_entity_info.incorporationState,
        county: scenario.alliance.business_address.county
      }
    };

    const legalValidation = this.legalValidator.validateAgreement(generatedAgreement, expectedData);

    // Combine all validation results
    const allErrors = [
      ...structureValidation.errors,
      ...contentValidation.errors,
      ...legalValidation.criticalErrors
    ];

    const allWarnings = [
      ...structureValidation.warnings,
      ...contentValidation.warnings,
      ...legalValidation.validationErrors
    ];

    // Calculate overall accuracy score
    const totalChecks = 50; // Base number of validation checks
    const errorCount = allErrors.length;
    const warningCount = allWarnings.length;
    const accuracyScore = Math.max(0, Math.round(((totalChecks - errorCount - (warningCount * 0.5)) / totalChecks) * 100));

    // Determine if production ready
    const isProductionReady = accuracyScore >= TEST_CONFIG.minAccuracyThreshold && errorCount === 0;

    return {
      accuracyScore,
      isProductionReady,
      criticalErrors: allErrors,
      validationErrors: allWarnings,
      structureValidation,
      contentValidation,
      legalValidation
    };
  }

  displayTestResults(results) {
    console.log(`   📊 Accuracy Score: ${results.accuracyScore}%`);
    console.log(`   🎯 Production Ready: ${results.isProductionReady ? 'YES' : 'NO'}`);
    console.log(`   ❌ Critical Errors: ${results.criticalErrors.length}`);
    console.log(`   ⚠️  Validation Errors: ${results.validationErrors.length}`);

    if (results.criticalErrors.length > 0) {
      console.log('   🚨 CRITICAL ERRORS:');
      results.criticalErrors.forEach(error => {
        console.log(`      - ${error}`);
      });
    }

    if (results.validationErrors.length > 0 && results.validationErrors.length <= 5) {
      console.log('   ⚠️  VALIDATION WARNINGS:');
      results.validationErrors.forEach(warning => {
        console.log(`      - ${warning}`);
      });
    }
  }

  async saveTestResults(scenarioKey, scenario, agreement, validationResults) {
    const timestamp = new Date().toISOString().split('T')[0];

    // Save generated agreement
    const agreementPath = path.join(TEST_CONFIG.outputDirectory, `${scenarioKey.toLowerCase()}-agreement.md`);
    fs.writeFileSync(agreementPath, agreement);

    // Save validation report
    const reportPath = path.join(TEST_CONFIG.outputDirectory, `${scenarioKey.toLowerCase()}-validation-report.md`);
    const report = this.generateValidationReport(scenario, validationResults);
    fs.writeFileSync(reportPath, report);

    console.log(`   💾 Saved: ${path.basename(agreementPath)} & ${path.basename(reportPath)}`);
  }

  generateValidationReport(scenario, results) {
    return `# Validation Report: ${scenario.name}

## Test Configuration
- **Alliance**: ${scenario.alliance.name} (${scenario.alliance.alliance_type})
- **State**: ${scenario.alliance.legal_entity_info.incorporationState}
- **Venture**: ${scenario.venture.name} (${scenario.venture.venture_type})
- **Contributor**: ${scenario.contributor.name} (${scenario.contributor.isCompany ? 'Company' : 'Individual'})

## Validation Results
- **Accuracy Score**: ${results.accuracyScore}%
- **Production Ready**: ${results.isProductionReady ? 'YES' : 'NO'}
- **Critical Errors**: ${results.criticalErrors.length}
- **Validation Warnings**: ${results.validationErrors.length}

## Critical Errors
${results.criticalErrors.length > 0 ? results.criticalErrors.map(error => `- ${error}`).join('\n') : 'None'}

## Validation Warnings
${results.validationErrors.length > 0 ? results.validationErrors.map(warning => `- ${warning}`).join('\n') : 'None'}

## Detailed Analysis
### Structure Validation
- **Errors**: ${results.structureValidation.errors.length}
- **Warnings**: ${results.structureValidation.warnings.length}

### Content Validation
- **Errors**: ${results.contentValidation.errors.length}
- **Warnings**: ${results.contentValidation.warnings.length}

### Legal Validation
- **Critical Errors**: ${results.legalValidation.criticalErrors.length}
- **Validation Errors**: ${results.legalValidation.validationErrors.length}

---
Generated on: ${new Date().toISOString()}
`;
  }

  generateSummaryReport() {
    const timestamp = new Date().toISOString().split('T')[0];
    const totalTests = this.results.length;
    const successfulTests = this.results.filter(r => !r.error).length;
    const productionReadyTests = this.results.filter(r => r.validationResults.isProductionReady).length;
    const perfectScoreTests = this.results.filter(r => r.validationResults.accuracyScore === 100).length;
    const averageAccuracy = this.results.reduce((sum, r) => sum + r.validationResults.accuracyScore, 0) / totalTests;
    const totalCriticalErrors = this.results.reduce((sum, r) => sum + r.validationResults.criticalErrors.length, 0);

    console.log('📊 COMPREHENSIVE VALIDATION SUMMARY');
    console.log('='.repeat(40));
    console.log('📈 Summary Results:');
    console.log(`   Total Tests: ${totalTests}`);
    console.log(`   Successful: ${successfulTests}/${totalTests}`);
    console.log(`   Perfect Accuracy (100%): ${perfectScoreTests}/${totalTests}`);
    console.log(`   Production Ready: ${productionReadyTests}/${totalTests}`);
    console.log(`   Average Accuracy: ${averageAccuracy.toFixed(1)}%`);
    console.log(`   Total Critical Errors: ${totalCriticalErrors}`);
    console.log('');

    console.log('📋 Individual Results:');
    this.results.forEach(result => {
      const status = result.validationResults.isProductionReady ? '✅ READY' : '⚠️ NEEDS WORK';
      const score = result.validationResults.accuracyScore;
      const errors = result.validationResults.criticalErrors.length;
      console.log(`   ${result.scenario.name}: ${status} (${score}%) - ${errors} errors`);
    });

    console.log('');
    console.log('🎯 FINAL ASSESSMENT');
    console.log('='.repeat(20));

    if (perfectScoreTests === totalTests) {
      console.log('🎉 ALL TESTS ACHIEVE 100% ACCURACY - SYSTEM READY FOR PRODUCTION!');
    } else if (productionReadyTests === totalTests) {
      console.log('✅ ALL TESTS MEET PRODUCTION REQUIREMENTS');
    } else {
      console.log('❌ SYSTEM DOES NOT MEET PRODUCTION REQUIREMENTS');
      console.log(`⚠️  ${totalTests - productionReadyTests} tests failed to meet minimum accuracy threshold`);
      console.log(`🚨 ${totalCriticalErrors} critical errors must be fixed`);
      console.log('🔧 Review validation reports and fix all issues before production');
    }

    // Save summary report
    if (TEST_CONFIG.saveResults) {
      const summaryPath = path.join(TEST_CONFIG.outputDirectory, `comprehensive-validation-summary-${timestamp}.md`);
      const summaryReport = this.generateDetailedSummaryReport();
      fs.writeFileSync(summaryPath, summaryReport);
      console.log('');
      console.log(`💾 Summary report saved: ${path.basename(summaryPath)}`);
    }

    console.log('');
    console.log(`📁 All results saved to: ${TEST_CONFIG.outputDirectory}`);
    console.log('🎯 Comprehensive Legal Agreement Validation Complete!');

    return {
      totalTests,
      successfulTests,
      productionReadyTests,
      perfectScoreTests,
      averageAccuracy,
      totalCriticalErrors,
      allTestsPassed: productionReadyTests === totalTests,
      results: this.results
    };
  }

  generateDetailedSummaryReport() {
    const timestamp = new Date().toISOString();
    const totalTests = this.results.length;
    const successfulTests = this.results.filter(r => !r.error).length;
    const productionReadyTests = this.results.filter(r => r.validationResults.isProductionReady).length;
    const averageAccuracy = this.results.reduce((sum, r) => sum + r.validationResults.accuracyScore, 0) / totalTests;

    return `# Comprehensive Legal Agreement Validation Summary

## Test Overview
- **Test Date**: ${timestamp}
- **Total Scenarios**: ${totalTests}
- **Successful Tests**: ${successfulTests}/${totalTests}
- **Production Ready**: ${productionReadyTests}/${totalTests}
- **Average Accuracy**: ${averageAccuracy.toFixed(1)}%

## Test Scenarios Covered
${this.results.map(r => `
### ${r.scenario.name}
- **Alliance Type**: ${r.scenario.alliance.alliance_type}
- **State**: ${r.scenario.alliance.legal_entity_info.incorporationState}
- **Venture Type**: ${r.scenario.venture.venture_type}
- **Contributor Type**: ${r.scenario.contributor.isCompany ? 'Company' : 'Individual'}
- **Accuracy**: ${r.validationResults.accuracyScore}%
- **Production Ready**: ${r.validationResults.isProductionReady ? 'YES' : 'NO'}
- **Critical Errors**: ${r.validationResults.criticalErrors.length}
`).join('')}

## Validation Criteria
- **Minimum Accuracy Threshold**: ${TEST_CONFIG.minAccuracyThreshold}%
- **Structure Validation**: Template compliance
- **Content Validation**: Data accuracy
- **Legal Validation**: Legal formatting and requirements
- **Signature Block Validation**: Proper formatting
- **Jurisdiction Validation**: State and county accuracy

## Recommendations
${productionReadyTests === totalTests
  ? '✅ All tests pass production requirements. System is ready for deployment.'
  : `⚠️ ${totalTests - productionReadyTests} tests require attention before production deployment.`
}

---
Generated by Comprehensive Legal Agreement Validation Suite
`;
  }
}

/**
 * Main execution function - runs when script is executed directly
 */
async function runComprehensiveTests() {
  const testRunner = new ComprehensiveLegalTestRunner();

  try {
    await testRunner.initialize();
    const results = await testRunner.runAllTests();

    // Exit with appropriate code
    process.exit(results.allTestsPassed ? 0 : 1);
  } catch (error) {
    console.error('❌ Test execution failed:', error.message);
    console.error(error.stack);
    process.exit(1);
  }
}

// Run tests if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runComprehensiveTests();
}

// Export test configuration and classes for use in other modules
export {
  TEST_CONFIG,
  TEST_SCENARIOS,
  TemplateStructureValidator,
  ContentAccuracyValidator,
  ComprehensiveLegalTestRunner,
  runComprehensiveTests
};
