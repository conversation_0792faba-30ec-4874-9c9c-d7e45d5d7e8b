// Enhanced Escrow Management API
// Backend Specialist: Milestone-based escrow releases and dispute resolution
const { createClient } = require('@supabase/supabase-js');

// Supabase client
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_KEY
);

// Helper function to get user from request
const getUserFromRequest = (event) => {
  const authHeader = event.headers.authorization;
  if (!authHeader) return null;
  
  try {
    const token = authHeader.replace('Bearer ', '');
    const payload = JSON.parse(Buffer.from(token.split('.')[1], 'base64').toString());
    return payload.sub;
  } catch {
    return null;
  }
};

// Create Escrow Account
const createEscrowAccount = async (event) => {
  try {
    const userId = getUserFromRequest(event);
    if (!userId) {
      return {
        statusCode: 401,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    const data = JSON.parse(event.body);
    
    // Validate required fields
    if (!data.escrow_name || !data.total_amount || !data.project_id) {
      return {
        statusCode: 400,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          error: 'escrow_name, total_amount, and project_id are required' 
        })
      };
    }

    // Verify user has permission to create escrow for this project
    const { data: project, error: projectError } = await supabase
      .from('projects')
      .select(`
        id,
        created_by,
        team_id,
        teams(
          team_members(user_id, role)
        )
      `)
      .eq('id', data.project_id)
      .single();

    if (projectError) {
      return {
        statusCode: 404,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Project not found' })
      };
    }

    const hasPermission = project.created_by === userId ||
      (project.teams && project.teams.team_members.some(m => 
        m.user_id === userId && ['founder', 'owner', 'admin'].includes(m.role)
      ));

    if (!hasPermission) {
      return {
        statusCode: 403,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Insufficient permissions to create escrow' })
      };
    }

    // Create escrow account using existing financial_transactions table for now
    // TODO: Use escrow_accounts table when Plaid migration is applied
    const escrowData = {
      company_id: data.company_id || null,
      project_id: data.project_id,
      transaction_type: 'escrow_creation',
      transaction_category: 'escrow_management',
      gross_amount: parseFloat(data.total_amount),
      currency: data.currency || 'USD',
      payee_user_id: userId,
      description: `Escrow account: ${data.escrow_name}`,
      reference_number: `ESCROW-${Date.now()}`,
      created_by: userId,
      status: 'pending_funding'
    };

    const { data: escrow, error: escrowError } = await supabase
      .from('financial_transactions')
      .insert([escrowData])
      .select()
      .single();

    if (escrowError) {
      throw new Error(`Failed to create escrow: ${escrowError.message}`);
    }

    // Store escrow metadata in existing revenue_escrow table
    const escrowMetadata = {
      revenue_id: escrow.id,
      project_id: data.project_id,
      amount: parseFloat(data.total_amount),
      currency: data.currency || 'USD',
      escrow_date: new Date().toISOString().split('T')[0],
      release_date: data.auto_release_date || null,
      status: 'active',
      reason: data.escrow_name,
      release_condition: JSON.stringify(data.release_conditions || {}),
      created_by: userId
    };

    const { data: metadata, error: metadataError } = await supabase
      .from('revenue_escrow')
      .insert([escrowMetadata])
      .select()
      .single();

    if (metadataError) {
      // Rollback escrow creation
      await supabase.from('financial_transactions').delete().eq('id', escrow.id);
      throw new Error(`Failed to create escrow metadata: ${metadataError.message}`);
    }

    return {
      statusCode: 201,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        escrow: {
          id: escrow.id,
          escrow_name: data.escrow_name,
          total_amount: parseFloat(data.total_amount),
          current_balance: 0,
          project_id: data.project_id,
          status: 'pending_funding',
          release_conditions: data.release_conditions || {},
          auto_release_date: data.auto_release_date,
          requires_manual_approval: data.requires_manual_approval !== false,
          created_at: escrow.created_at,
          metadata_id: metadata.id
        }
      })
    };

  } catch (error) {
    console.error('Create escrow error:', error);
    return {
      statusCode: 500,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ error: 'Failed to create escrow account' })
    };
  }
};

// Get Escrow Account Details
const getEscrowAccount = async (event) => {
  try {
    const userId = getUserFromRequest(event);
    if (!userId) {
      return {
        statusCode: 401,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    const escrowId = event.path.split('/').pop();

    // Get escrow details from financial_transactions and revenue_escrow
    const { data: escrow, error: escrowError } = await supabase
      .from('financial_transactions')
      .select(`
        *,
        projects(
          id,
          name,
          title,
          created_by,
          teams(
            id,
            name,
            team_members(user_id, role)
          )
        )
      `)
      .eq('id', escrowId)
      .eq('transaction_type', 'escrow_creation')
      .single();

    if (escrowError) {
      return {
        statusCode: 404,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Escrow account not found' })
      };
    }

    // Check access permissions
    const hasAccess = escrow.created_by === userId ||
      escrow.payee_user_id === userId ||
      (escrow.projects?.teams?.team_members?.some(m => m.user_id === userId));

    if (!hasAccess) {
      return {
        statusCode: 403,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Access denied' })
      };
    }

    // Get escrow metadata
    const { data: metadata, error: metadataError } = await supabase
      .from('revenue_escrow')
      .select('*')
      .eq('revenue_id', escrowId)
      .single();

    // Get escrow releases
    const { data: releases, error: releasesError } = await supabase
      .from('financial_transactions')
      .select(`
        *,
        payee:users!financial_transactions_payee_user_id_fkey(
          id,
          display_name,
          email
        )
      `)
      .eq('reference_number', `ESCROW-RELEASE-${escrowId}`)
      .order('created_at', { ascending: false });

    const response = {
      id: escrow.id,
      escrow_name: escrow.description.replace('Escrow account: ', ''),
      total_amount: escrow.gross_amount,
      current_balance: escrow.gross_amount - (releases?.reduce((sum, r) => sum + r.gross_amount, 0) || 0),
      project: escrow.projects ? {
        id: escrow.projects.id,
        name: escrow.projects.name || escrow.projects.title,
        alliance: escrow.projects.teams ? {
          id: escrow.projects.teams.id,
          name: escrow.projects.teams.name
        } : null
      } : null,
      status: escrow.status,
      release_conditions: metadata ? JSON.parse(metadata.release_condition || '{}') : {},
      auto_release_date: metadata?.release_date,
      requires_manual_approval: true,
      created_at: escrow.created_at,
      releases: releases || [],
      metadata: metadata
    };

    return {
      statusCode: 200,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(response)
    };

  } catch (error) {
    console.error('Get escrow error:', error);
    return {
      statusCode: 500,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ error: 'Failed to get escrow account' })
    };
  }
};

// List User's Escrow Accounts
const listEscrowAccounts = async (event) => {
  try {
    const userId = getUserFromRequest(event);
    if (!userId) {
      return {
        statusCode: 401,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    const queryParams = new URLSearchParams(event.queryStringParameters || {});
    const projectId = queryParams.get('project_id');
    const status = queryParams.get('status');

    let query = supabase
      .from('financial_transactions')
      .select(`
        *,
        projects(
          id,
          name,
          title,
          teams(
            id,
            name
          )
        )
      `)
      .eq('transaction_type', 'escrow_creation')
      .or(`created_by.eq.${userId},payee_user_id.eq.${userId}`)
      .order('created_at', { ascending: false });

    if (projectId) {
      query = query.eq('project_id', projectId);
    }

    if (status) {
      query = query.eq('status', status);
    }

    const { data: escrows, error: escrowsError } = await query;

    if (escrowsError) {
      throw new Error(`Failed to fetch escrows: ${escrowsError.message}`);
    }

    // Get metadata for each escrow
    const escrowIds = escrows.map(e => e.id);
    const { data: metadataList } = await supabase
      .from('revenue_escrow')
      .select('*')
      .in('revenue_id', escrowIds);

    const escrowsWithMetadata = escrows.map(escrow => {
      const metadata = metadataList?.find(m => m.revenue_id === escrow.id);
      
      return {
        id: escrow.id,
        escrow_name: escrow.description.replace('Escrow account: ', ''),
        total_amount: escrow.gross_amount,
        current_balance: escrow.gross_amount, // TODO: Calculate actual balance
        project: escrow.projects ? {
          id: escrow.projects.id,
          name: escrow.projects.name || escrow.projects.title,
          alliance: escrow.projects.teams ? {
            id: escrow.projects.teams.id,
            name: escrow.projects.teams.name
          } : null
        } : null,
        status: escrow.status,
        release_conditions: metadata ? JSON.parse(metadata.release_condition || '{}') : {},
        auto_release_date: metadata?.release_date,
        created_at: escrow.created_at,
        user_role: escrow.created_by === userId ? 'creator' : 'beneficiary'
      };
    });

    return {
      statusCode: 200,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ escrows: escrowsWithMetadata })
    };

  } catch (error) {
    console.error('List escrows error:', error);
    return {
      statusCode: 500,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ error: 'Failed to list escrow accounts' })
    };
  }
};

// Route requests to appropriate handlers
exports.handler = async (event, context) => {
  // Add CORS headers
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS'
  };

  // Handle preflight requests
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers,
      body: ''
    };
  }

  const path = event.path.replace('/.netlify/functions/escrow-management', '');

  try {
    let response;

    if (event.httpMethod === 'GET') {
      if (path === '' || path === '/') {
        response = await listEscrowAccounts(event);
      } else {
        response = await getEscrowAccount(event);
      }
    } else if (event.httpMethod === 'POST') {
      response = await createEscrowAccount(event);
    } else {
      response = {
        statusCode: 405,
        body: JSON.stringify({ error: 'Method not allowed' })
      };
    }

    // Add CORS headers to response
    response.headers = { ...response.headers, ...headers };
    return response;

  } catch (error) {
    console.error('Escrow Management API error:', error);
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ error: 'Internal server error' })
    };
  }
};
