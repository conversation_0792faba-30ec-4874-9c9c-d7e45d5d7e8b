# Legal Agreement System Documentation

## Purpose & Scope

The Royaltea platform's legal agreement generator creates **contributor agreements for user ventures**. This system helps users establish proper legal relationships with contributors they add to their projects.

## Agreement Parties

### ✅ CORRECT: What We Generate
```
COMPANY: User's Venture/Business
    ↕️ (Agreement)
CONTRIBUTOR: Person joining the user's project
```

**Example:**
- **Company**: "TaskMaster Pro LLC" (user's venture)
- **Contributor**: "Jane Developer" (person joining TaskMaster Pro team)

### ❌ INCORRECT: What We DON'T Generate
```
COMPANY: City of Gamers Inc.
    ↕️ (Agreement)  
USER: Platform user
```

## How It Works

### 1. User Creates Venture
- User goes through venture setup wizard
- Answers questions about their project
- System generates project data structure

### 2. Agreement Template Generation
- Uses our lawyer-approved agreement as structural template
- Customizes content for user's specific venture
- Includes user's company name, project details, revenue model

### 3. Contributor Onboarding
- When user adds contributors to their project
- Generated agreement governs the relationship
- User and contributor sign the agreement (not us)

## Legal Template Source

Our lawyer-approved agreement (`client/public/example-cog-contributor-agreement.md`) serves as:
- ✅ **Structural template** for user agreements
- ✅ **Legal language reference** for professional formatting
- ✅ **Best practices guide** for venture collaboration

It is NOT:
- ❌ The actual agreement users sign
- ❌ An agreement between users and City of Gamers Inc.
- ❌ A contract for platform usage

## Generated Agreement Structure

### Header
```markdown
# [USER'S VENTURE NAME]
# CONTRIBUTOR AGREEMENT

This Agreement is between [User's Venture] and [Contributor Name]
```

### Key Sections
1. **Recitals** - Legal background and purpose
2. **Definitions** - Technical and legal terms
3. **Standard Legal Sections** - 21 sections covering all legal aspects
4. **Schedule A** - Project-specific service descriptions
5. **Schedule B** - Revenue sharing and contribution tracking
6. **Exhibit I** - Technical specifications
7. **Exhibit II** - Project roadmap and milestones
8. **Signature Blocks** - For venture owner and contributor

### Customization
- **Company Name**: User's venture name
- **Project Details**: From venture setup answers
- **Revenue Model**: Based on user's funding approach
- **Technical Specs**: Adapted to project type
- **Milestones**: Generated from timeline and goals

## Usage in Platform

### Venture Creation Flow
1. User completes venture setup wizard
2. System generates agreement template
3. User reviews agreement in preview
4. Agreement saved with venture for future use

### Adding Contributors
1. User invites contributor to project
2. System presents generated agreement
3. Both parties review and sign
4. Contributor gains access to project

### Legal Compliance
- Agreements follow professional legal standards
- Based on lawyer-approved template structure
- Include all necessary legal protections
- Ready for execution without modification

## Technical Implementation

### Core Functions
- `generateLegalAgreement()` - Main generation function
- `mapVentureAnswersToProjectData()` - Data transformation
- Template rendering with dynamic content

### File Locations
- Generator: `client/src/utils/legal-agreement-generator.js`
- Mapping: `client/src/utils/venture-mapping.js`
- Template: `client/public/example-cog-contributor-agreement.md`

### Testing
- Unit tests verify all agreement components
- Integration tests generate sample agreements
- Quality validation ensures legal completeness

## Important Notes

### For Developers
- Always remember: agreements are FOR users, not WITH users
- Our template is a MODEL, not the actual agreement
- Users own and control their generated agreements

### For Legal Review
- Generated agreements follow approved template structure
- All legal language sourced from vetted template
- Users responsible for their own legal compliance

### For Product Team
- Feature enables user venture collaboration
- Reduces legal barriers for project teams
- Provides professional documentation automatically

## Future Enhancements

### Potential Features
- Multiple agreement templates for different venture types
- Custom clause library for specific industries
- Integration with e-signature services
- Legal review workflow for complex ventures

### Considerations
- State-specific legal requirements
- International venture support
- Industry-specific compliance needs
- Integration with legal service providers
