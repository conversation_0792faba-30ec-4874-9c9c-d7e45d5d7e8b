/**
 * Error Handling Tests
 * 
 * Tests that the agreement generator handles errors gracefully
 * and provides appropriate fallbacks
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { NewAgreementGenerator } from '../../utils/agreement/newAgreementGenerator.js';

// Mock Supabase
const mockSupabase = {
  from: vi.fn(() => ({
    select: vi.fn(() => ({
      eq: vi.fn(() => ({
        single: vi.fn()
      }))
    }))
  }))
};

vi.mock('../../utils/supabase/supabase.utils', () => ({
  supabase: mockSupabase
}));

describe('Error Handling in Agreement Generation', () => {
  let generator;

  beforeEach(() => {
    generator = new NewAgreementGenerator();
    vi.clearAllMocks();
  });

  describe('Missing Required Parameters', () => {
    it('should throw error when template is missing', async () => {
      const project = { name: 'Test Project' };
      
      await expect(
        generator.generateAgreement(null, project)
      ).rejects.toThrow('Template text and project data are required');
    });

    it('should throw error when project is missing', async () => {
      const template = 'Test template';
      
      await expect(
        generator.generateAgreement(template, null)
      ).rejects.toThrow('Template text and project data are required');
    });

    it('should throw error when both template and project are missing', async () => {
      await expect(
        generator.generateAgreement(null, null)
      ).rejects.toThrow('Template text and project data are required');
    });
  });

  describe('Database Connection Errors', () => {
    it('should handle alliance fetch failure gracefully', async () => {
      const template = 'Agreement with [Contributor]';
      const project = {
        id: 'test-project',
        name: 'Test Project',
        alliance_id: 'alliance-123'
      };

      // Mock database error
      mockSupabase.from.mockReturnValue({
        select: vi.fn().mockReturnValue({
          eq: vi.fn().mockReturnValue({
            single: vi.fn().mockRejectedValue(new Error('Database connection failed'))
          })
        })
      });

      const result = await generator.generateAgreement(template, project, {
        contributors: [{ permission_level: 'Owner', display_name: 'Fallback Owner' }],
        currentUser: { email: '<EMAIL>' },
        fullName: 'Test Contributor'
      });

      // Should fall back to project owner info
      expect(result).toContain('Test Contributor');
      expect(result).toContain('Fallback Owner'); // Uses fallback company name
    });

    it('should handle malformed alliance data', async () => {
      const template = 'Agreement with [Contributor]';
      const project = {
        id: 'test-project',
        name: 'Test Project',
        alliance_id: 'alliance-123'
      };

      // Mock malformed alliance data
      mockSupabase.from.mockReturnValue({
        select: vi.fn().mockReturnValue({
          eq: vi.fn().mockReturnValue({
            single: vi.fn().mockResolvedValue({
              data: {
                id: 'alliance-123',
                name: 'Test Alliance'
                // Missing team_members
              },
              error: null
            })
          })
        })
      });

      const result = await generator.generateAgreement(template, project, {
        contributors: [{ permission_level: 'Owner', display_name: 'Fallback Owner' }],
        currentUser: { email: '<EMAIL>' },
        fullName: 'Test Contributor'
      });

      // Should handle missing team_members gracefully
      expect(result).toContain('Test Contributor');
      expect(result).toContain('Test Alliance');
    });
  });

  describe('Missing Optional Data', () => {
    it('should handle missing contributors array', async () => {
      const template = 'Agreement with [Contributor]';
      const project = { name: 'Test Project' };

      const result = await generator.generateAgreement(template, project, {
        currentUser: { email: '<EMAIL>', user_metadata: { full_name: 'Current User' } },
        fullName: 'Test Contributor'
      });

      expect(result).toContain('Test Contributor');
      expect(result).toContain('Acme Corporation'); // Default company name
    });

    it('should handle missing current user', async () => {
      const template = 'Agreement with [Contributor]';
      const project = { name: 'Test Project' };

      const result = await generator.generateAgreement(template, project, {
        contributors: [{ permission_level: 'Owner', display_name: 'Project Owner' }],
        fullName: 'Test Contributor'
      });

      expect(result).toContain('Test Contributor');
      expect(result).toContain('Project Owner');
    });

    it('should handle missing full name', async () => {
      const template = 'Agreement with [Contributor]';
      const project = { name: 'Test Project' };

      const result = await generator.generateAgreement(template, project, {
        contributors: [{ permission_level: 'Owner', display_name: 'Project Owner' }],
        currentUser: { email: '<EMAIL>', user_metadata: { full_name: 'Current User' } }
      });

      expect(result).toContain('Current User'); // Falls back to current user name
    });
  });

  describe('Invalid Project Data', () => {
    it('should handle missing project name', async () => {
      const template = 'Project: [Project Name]';
      const project = { description: 'A project without a name' };

      const result = await generator.generateAgreement(template, project, {
        contributors: [{ permission_level: 'Owner', display_name: 'Owner' }],
        currentUser: { email: '<EMAIL>' },
        fullName: 'Test Contributor'
      });

      expect(result).toContain('[Project Title]'); // Default project name
    });

    it('should handle missing project description', async () => {
      const template = 'Description: village simulation game where players guide communities through historical progressions and manage resource-based challenges';
      const project = { name: 'Test Project' };

      const result = await generator.generateAgreement(template, project, {
        contributors: [{ permission_level: 'Owner', display_name: 'Owner' }],
        currentUser: { email: '<EMAIL>' },
        fullName: 'Test Contributor'
      });

      expect(result).toContain('a collaborative project'); // Default description
    });

    it('should handle invalid project type', async () => {
      const template = 'Type: [Project Type]';
      const project = { 
        name: 'Test Project',
        project_type: null
      };

      const result = await generator.generateAgreement(template, project, {
        contributors: [{ permission_level: 'Owner', display_name: 'Owner' }],
        currentUser: { email: '<EMAIL>' },
        fullName: 'Test Contributor'
      });

      expect(result).toContain('creative work'); // Default project type
    });
  });

  describe('Empty or Invalid Templates', () => {
    it('should handle empty template', async () => {
      const template = '';
      const project = { name: 'Test Project' };

      const result = await generator.generateAgreement(template, project, {
        contributors: [{ permission_level: 'Owner', display_name: 'Owner' }],
        currentUser: { email: '<EMAIL>' },
        fullName: 'Test Contributor'
      });

      expect(result).toBe('\n'); // Should return clean empty result
    });

    it('should handle template with only whitespace', async () => {
      const template = '   \n\n   \t   \n   ';
      const project = { name: 'Test Project' };

      const result = await generator.generateAgreement(template, project, {
        contributors: [{ permission_level: 'Owner', display_name: 'Owner' }],
        currentUser: { email: '<EMAIL>' },
        fullName: 'Test Contributor'
      });

      expect(result.trim()).toBe(''); // Should clean up whitespace
    });
  });

  describe('Alliance Data Edge Cases', () => {
    it('should handle alliance with no team members', async () => {
      const template = 'Company: [Company]';
      const project = {
        name: 'Test Project',
        alliance_id: 'alliance-123'
      };

      mockSupabase.from.mockReturnValue({
        select: vi.fn().mockReturnValue({
          eq: vi.fn().mockReturnValue({
            single: vi.fn().mockResolvedValue({
              data: {
                id: 'alliance-123',
                name: 'Empty Alliance',
                team_members: []
              },
              error: null
            })
          })
        })
      });

      const result = await generator.generateAgreement(template, project, {
        contributors: [{ permission_level: 'Owner', display_name: 'Fallback Owner' }],
        currentUser: { email: '<EMAIL>' },
        fullName: 'Test Contributor'
      });

      expect(result).toContain('Empty Alliance');
      expect(result).toContain('Alliance Representative'); // Default signer name
    });

    it('should handle alliance with members but no founder/owner', async () => {
      const template = 'Signer: [Signer]';
      const project = {
        name: 'Test Project',
        alliance_id: 'alliance-123'
      };

      mockSupabase.from.mockReturnValue({
        select: vi.fn().mockReturnValue({
          eq: vi.fn().mockReturnValue({
            single: vi.fn().mockResolvedValue({
              data: {
                id: 'alliance-123',
                name: 'Member Alliance',
                team_members: [{
                  user_id: 'user-1',
                  role: 'member',
                  users: {
                    id: 'user-1',
                    email: '<EMAIL>',
                    user_metadata: { full_name: 'Regular Member' }
                  }
                }]
              },
              error: null
            })
          })
        })
      });

      const result = await generator.generateAgreement(template, project, {
        contributors: [{ permission_level: 'Owner', display_name: 'Project Owner' }],
        currentUser: { email: '<EMAIL>' },
        fullName: 'Test Contributor'
      });

      // Should fall back to project owner when no founder/owner found
      expect(result).toContain('Project Owner');
    });
  });
});
