/**
 * Licensing Framework
 * 
 * Comprehensive licensing system for different usage rights, territories, and exclusivity levels:
 * - License creation and management
 * - Usage rights and restrictions
 * - Territory and exclusivity management
 * - License compliance and monitoring
 * - Sublicensing and transfer rights
 */

import { rightsManagementSystem } from './rightsManagementSystem.js';
import { ipRightsFramework } from './ipRightsFramework.js';

// ============================================================================
// LICENSE TYPES AND STRUCTURES
// ============================================================================

export const LICENSE_TYPES = {
  EXCLUSIVE: 'exclusive',
  NON_EXCLUSIVE: 'non_exclusive',
  SOLE: 'sole',
  CO_EXCLUSIVE: 'co_exclusive'
};

export const LICENSE_MODELS = {
  ROYALTY_BASED: 'royalty_based',
  FLAT_FEE: 'flat_fee',
  REVENUE_SHARE: 'revenue_share',
  EQUITY_BASED: 'equity_based',
  HYBRID: 'hybrid',
  FREE: 'free'
};

export const USAGE_SCOPES = {
  COMMERCIAL: 'commercial',
  NON_COMMERCIAL: 'non_commercial',
  EDUCATIONAL: 'educational',
  RESEARCH: 'research',
  PERSONAL: 'personal',
  INTERNAL: 'internal'
};

export const TERRITORIAL_MODELS = {
  WORLDWIDE: 'worldwide',
  REGIONAL: 'regional',
  COUNTRY_SPECIFIC: 'country_specific',
  JURISDICTION_SPECIFIC: 'jurisdiction_specific',
  CUSTOM: 'custom'
};

export const FIELD_OF_USE_CATEGORIES = {
  TECHNOLOGY: 'technology',
  ENTERTAINMENT: 'entertainment',
  EDUCATION: 'education',
  HEALTHCARE: 'healthcare',
  FINANCE: 'finance',
  MANUFACTURING: 'manufacturing',
  RETAIL: 'retail',
  MEDIA: 'media'
};

// ============================================================================
// LICENSING FRAMEWORK CLASS
// ============================================================================

export class LicensingFramework {
  constructor() {
    this.licenses = new Map();
    this.licenseTemplates = new Map();
    this.sublicenses = new Map();
    this.licenseCompliance = new Map();
    this.royaltyTracking = new Map();
    this.territoryManagement = new Map();
  }

  /**
   * Create a comprehensive license agreement
   */
  createLicense(licenseDefinition) {
    const license = {
      id: licenseDefinition.id || this.generateLicenseId(),
      
      // Parties
      licensor: licenseDefinition.licensor,
      licensee: licenseDefinition.licensee,
      
      // Licensed assets
      licensedAssets: licenseDefinition.licensedAssets || [],
      assetDescriptions: licenseDefinition.assetDescriptions || {},
      
      // License type and scope
      licenseType: licenseDefinition.licenseType || LICENSE_TYPES.NON_EXCLUSIVE,
      licenseModel: licenseDefinition.licenseModel || LICENSE_MODELS.ROYALTY_BASED,
      usageScope: licenseDefinition.usageScope || USAGE_SCOPES.COMMERCIAL,
      
      // Rights granted
      grantedRights: this.processGrantedRights(licenseDefinition.grantedRights),
      reservedRights: licenseDefinition.reservedRights || [],
      restrictions: licenseDefinition.restrictions || [],
      
      // Territory and field of use
      territory: this.processTerritorialScope(licenseDefinition.territory),
      fieldOfUse: this.processFieldOfUse(licenseDefinition.fieldOfUse),
      
      // Duration and renewal
      duration: this.processDuration(licenseDefinition.duration),
      renewalOptions: licenseDefinition.renewalOptions || [],
      terminationConditions: licenseDefinition.terminationConditions || [],
      
      // Financial terms
      financialTerms: this.processFinancialTerms(licenseDefinition.financialTerms, licenseDefinition.licenseModel),
      
      // Performance requirements
      performanceStandards: licenseDefinition.performanceStandards || [],
      minimumCommitments: licenseDefinition.minimumCommitments || {},
      reportingRequirements: licenseDefinition.reportingRequirements || [],
      
      // Quality control
      qualityStandards: licenseDefinition.qualityStandards || [],
      approvalRights: licenseDefinition.approvalRights || {},
      inspectionRights: licenseDefinition.inspectionRights || {},
      
      // Sublicensing
      sublicensingRights: this.processSublicensingRights(licenseDefinition.sublicensingRights),
      
      // Transfer and assignment
      transferRights: licenseDefinition.transferRights || {},
      assignmentRestrictions: licenseDefinition.assignmentRestrictions || [],
      
      // Compliance and monitoring
      complianceRequirements: licenseDefinition.complianceRequirements || [],
      monitoringRights: licenseDefinition.monitoringRights || {},
      auditRights: licenseDefinition.auditRights || {},
      
      // Legal terms
      governingLaw: licenseDefinition.governingLaw,
      disputeResolution: licenseDefinition.disputeResolution || 'arbitration',
      indemnification: licenseDefinition.indemnification || {},
      warranties: licenseDefinition.warranties || [],
      
      // Status and metadata
      status: 'active',
      effectiveDate: licenseDefinition.effectiveDate || new Date().toISOString(),
      createdAt: new Date().toISOString(),
      lastUpdated: new Date().toISOString()
    };

    this.licenses.set(license.id, license);
    
    // Set up compliance monitoring
    this.setupComplianceMonitoring(license);
    
    // Initialize royalty tracking if applicable
    if (license.licenseModel === LICENSE_MODELS.ROYALTY_BASED || license.licenseModel === LICENSE_MODELS.REVENUE_SHARE) {
      this.initializeRoyaltyTracking(license);
    }
    
    return license;
  }

  /**
   * Process granted rights with detailed permissions
   */
  processGrantedRights(grantedRights) {
    const defaultRights = {
      use: false,
      reproduce: false,
      distribute: false,
      display: false,
      perform: false,
      createDerivatives: false,
      modify: false,
      sublicense: false,
      transfer: false,
      commercialUse: false
    };

    if (!grantedRights) return defaultRights;

    return {
      // Basic usage rights
      use: grantedRights.use !== false,
      reproduce: grantedRights.reproduce || false,
      distribute: grantedRights.distribute || false,
      display: grantedRights.display || false,
      perform: grantedRights.perform || false,
      
      // Modification rights
      createDerivatives: grantedRights.createDerivatives || false,
      modify: grantedRights.modify || false,
      adapt: grantedRights.adapt || false,
      translate: grantedRights.translate || false,
      
      // Distribution rights
      sell: grantedRights.sell || false,
      rent: grantedRights.rent || false,
      lend: grantedRights.lend || false,
      broadcast: grantedRights.broadcast || false,
      stream: grantedRights.stream || false,
      
      // Commercial rights
      commercialUse: grantedRights.commercialUse || false,
      merchandising: grantedRights.merchandising || false,
      advertising: grantedRights.advertising || false,
      
      // Transfer rights
      sublicense: grantedRights.sublicense || false,
      transfer: grantedRights.transfer || false,
      assign: grantedRights.assign || false,
      
      // Digital rights
      digitalDistribution: grantedRights.digitalDistribution || false,
      onlineUse: grantedRights.onlineUse || false,
      mobileUse: grantedRights.mobileUse || false,
      
      // Specific usage contexts
      contextualRights: grantedRights.contextualRights || {}
    };
  }

  /**
   * Process territorial scope with detailed geographic coverage
   */
  processTerritorialScope(territory) {
    if (!territory) {
      return {
        model: TERRITORIAL_MODELS.WORLDWIDE,
        coverage: 'worldwide',
        inclusions: [],
        exclusions: []
      };
    }

    return {
      model: territory.model || TERRITORIAL_MODELS.WORLDWIDE,
      coverage: territory.coverage || 'worldwide',
      
      // Specific territories
      countries: territory.countries || [],
      regions: territory.regions || [],
      jurisdictions: territory.jurisdictions || [],
      
      // Inclusions and exclusions
      inclusions: territory.inclusions || [],
      exclusions: territory.exclusions || [],
      
      // Special provisions
      futureExpansion: territory.futureExpansion || false,
      territorialRestrictions: territory.restrictions || [],
      
      // Digital territory considerations
      digitalTerritories: territory.digitalTerritories || {},
      geoBlocking: territory.geoBlocking || false
    };
  }

  /**
   * Process field of use restrictions
   */
  processFieldOfUse(fieldOfUse) {
    if (!fieldOfUse) {
      return {
        unrestricted: true,
        categories: [],
        restrictions: []
      };
    }

    return {
      unrestricted: fieldOfUse.unrestricted || false,
      
      // Permitted categories
      categories: fieldOfUse.categories || [],
      specificUses: fieldOfUse.specificUses || [],
      
      // Restrictions
      restrictions: fieldOfUse.restrictions || [],
      prohibitedUses: fieldOfUse.prohibitedUses || [],
      
      // Industry-specific provisions
      industrySpecific: fieldOfUse.industrySpecific || {},
      
      // Future use considerations
      futureUses: fieldOfUse.futureUses || 'excluded',
      technologyEvolution: fieldOfUse.technologyEvolution || {}
    };
  }

  /**
   * Process license duration and renewal terms
   */
  processDuration(duration) {
    if (!duration) {
      return {
        type: 'perpetual',
        renewable: false
      };
    }

    return {
      type: duration.type || 'fixed_term',
      
      // Fixed term details
      startDate: duration.startDate || new Date().toISOString(),
      endDate: duration.endDate,
      termLength: duration.termLength,
      
      // Renewal provisions
      renewable: duration.renewable || false,
      renewalTerms: duration.renewalTerms || {},
      automaticRenewal: duration.automaticRenewal || false,
      renewalNotice: duration.renewalNotice || {},
      
      // Termination provisions
      terminationRights: duration.terminationRights || {},
      earlyTermination: duration.earlyTermination || {},
      
      // Extension options
      extensionOptions: duration.extensionOptions || [],
      
      // Post-expiration rights
      postExpirationRights: duration.postExpirationRights || {}
    };
  }

  /**
   * Process financial terms based on license model
   */
  processFinancialTerms(financialTerms, licenseModel) {
    const baseTerms = {
      model: licenseModel,
      currency: financialTerms?.currency || 'USD',
      paymentFrequency: financialTerms?.paymentFrequency || 'quarterly',
      paymentMethod: financialTerms?.paymentMethod || 'bank_transfer'
    };

    switch (licenseModel) {
      case LICENSE_MODELS.ROYALTY_BASED:
        return {
          ...baseTerms,
          royaltyRate: financialTerms?.royaltyRate || 0,
          royaltyBase: financialTerms?.royaltyBase || 'net_sales',
          minimumRoyalty: financialTerms?.minimumRoyalty || 0,
          maximumRoyalty: financialTerms?.maximumRoyalty,
          royaltyTiers: financialTerms?.royaltyTiers || [],
          advancePayment: financialTerms?.advancePayment || 0,
          recoupment: financialTerms?.recoupment || {}
        };

      case LICENSE_MODELS.FLAT_FEE:
        return {
          ...baseTerms,
          licenseeFee: financialTerms?.licenseeFee || 0,
          paymentSchedule: financialTerms?.paymentSchedule || [],
          milestonePayments: financialTerms?.milestonePayments || []
        };

      case LICENSE_MODELS.REVENUE_SHARE:
        return {
          ...baseTerms,
          revenueSharePercentage: financialTerms?.revenueSharePercentage || 0,
          revenueDefinition: financialTerms?.revenueDefinition || 'gross_revenue',
          minimumPayment: financialTerms?.minimumPayment || 0,
          paymentThreshold: financialTerms?.paymentThreshold || 0
        };

      case LICENSE_MODELS.EQUITY_BASED:
        return {
          ...baseTerms,
          equityPercentage: financialTerms?.equityPercentage || 0,
          equityType: financialTerms?.equityType || 'common_stock',
          vestingSchedule: financialTerms?.vestingSchedule || {},
          dilutionProtection: financialTerms?.dilutionProtection || false
        };

      case LICENSE_MODELS.HYBRID:
        return {
          ...baseTerms,
          components: financialTerms?.components || [],
          weightings: financialTerms?.weightings || {}
        };

      default:
        return baseTerms;
    }
  }

  /**
   * Process sublicensing rights and restrictions
   */
  processSublicensingRights(sublicensingRights) {
    if (!sublicensingRights) {
      return {
        permitted: false,
        restrictions: []
      };
    }

    return {
      permitted: sublicensingRights.permitted || false,
      
      // Approval requirements
      approvalRequired: sublicensingRights.approvalRequired !== false,
      approvalCriteria: sublicensingRights.approvalCriteria || [],
      
      // Revenue sharing
      revenueSharing: sublicensingRights.revenueSharing || {},
      sublicenseRoyalties: sublicensingRights.sublicenseRoyalties || {},
      
      // Restrictions
      restrictions: sublicensingRights.restrictions || [],
      prohibitedSublicensees: sublicensingRights.prohibitedSublicensees || [],
      
      // Terms and conditions
      sublicenseTerms: sublicensingRights.sublicenseTerms || {},
      standardTerms: sublicensingRights.standardTerms || {},
      
      // Monitoring and control
      reportingRequirements: sublicensingRights.reportingRequirements || [],
      auditRights: sublicensingRights.auditRights || {},
      
      // Termination provisions
      terminationRights: sublicensingRights.terminationRights || {}
    };
  }

  /**
   * Create sublicense agreement
   */
  createSublicense(sublicenseDefinition) {
    const parentLicense = this.licenses.get(sublicenseDefinition.parentLicenseId);
    if (!parentLicense) {
      throw new Error(`Parent license ${sublicenseDefinition.parentLicenseId} not found`);
    }

    // Validate sublicensing rights
    if (!parentLicense.sublicensingRights.permitted) {
      throw new Error('Sublicensing not permitted under parent license');
    }

    const sublicense = {
      id: this.generateSublicenseId(),
      parentLicenseId: sublicenseDefinition.parentLicenseId,
      
      // Parties
      sublicensor: parentLicense.licensee, // Original licensee becomes sublicensor
      sublicensee: sublicenseDefinition.sublicensee,
      
      // Inherited terms (cannot exceed parent license scope)
      grantedRights: this.inheritRights(parentLicense.grantedRights, sublicenseDefinition.grantedRights),
      territory: this.inheritTerritory(parentLicense.territory, sublicenseDefinition.territory),
      fieldOfUse: this.inheritFieldOfUse(parentLicense.fieldOfUse, sublicenseDefinition.fieldOfUse),
      
      // Sublicense-specific terms
      duration: sublicenseDefinition.duration,
      financialTerms: sublicenseDefinition.financialTerms,
      
      // Revenue sharing with parent
      parentRoyalties: this.calculateParentRoyalties(parentLicense, sublicenseDefinition.financialTerms),
      
      // Status
      status: 'pending_approval',
      createdAt: new Date().toISOString()
    };

    this.sublicenses.set(sublicense.id, sublicense);
    
    // Request approval if required
    if (parentLicense.sublicensingRights.approvalRequired) {
      this.requestSublicenseApproval(sublicense);
    } else {
      sublicense.status = 'active';
    }

    return sublicense;
  }

  /**
   * Monitor license compliance
   */
  monitorCompliance(licenseId) {
    const license = this.licenses.get(licenseId);
    if (!license) {
      throw new Error(`License ${licenseId} not found`);
    }

    const complianceCheck = {
      id: this.generateComplianceId(),
      licenseId,
      checkDate: new Date().toISOString(),
      
      // Compliance areas
      usageCompliance: this.checkUsageCompliance(license),
      territorialCompliance: this.checkTerritorialCompliance(license),
      financialCompliance: this.checkFinancialCompliance(license),
      reportingCompliance: this.checkReportingCompliance(license),
      qualityCompliance: this.checkQualityCompliance(license),
      
      // Overall status
      overallStatus: 'compliant', // Will be updated based on checks
      violations: [],
      warnings: [],
      recommendations: []
    };

    // Determine overall compliance status
    const allChecks = [
      complianceCheck.usageCompliance,
      complianceCheck.territorialCompliance,
      complianceCheck.financialCompliance,
      complianceCheck.reportingCompliance,
      complianceCheck.qualityCompliance
    ];

    const hasViolations = allChecks.some(check => check.status === 'violation');
    const hasWarnings = allChecks.some(check => check.status === 'warning');

    if (hasViolations) {
      complianceCheck.overallStatus = 'non_compliant';
    } else if (hasWarnings) {
      complianceCheck.overallStatus = 'warning';
    }

    // Collect violations and warnings
    allChecks.forEach(check => {
      if (check.violations) complianceCheck.violations.push(...check.violations);
      if (check.warnings) complianceCheck.warnings.push(...check.warnings);
      if (check.recommendations) complianceCheck.recommendations.push(...check.recommendations);
    });

    this.licenseCompliance.set(complianceCheck.id, complianceCheck);
    return complianceCheck;
  }

  /**
   * Calculate and track royalties
   */
  calculateRoyalties(licenseId, salesData, period) {
    const license = this.licenses.get(licenseId);
    if (!license) {
      throw new Error(`License ${licenseId} not found`);
    }

    if (license.licenseModel !== LICENSE_MODELS.ROYALTY_BASED && license.licenseModel !== LICENSE_MODELS.REVENUE_SHARE) {
      throw new Error(`License ${licenseId} is not royalty-based`);
    }

    const royaltyCalculation = {
      id: this.generateRoyaltyId(),
      licenseId,
      period,
      calculationDate: new Date().toISOString(),
      
      // Sales data
      grossSales: salesData.grossSales || 0,
      netSales: salesData.netSales || 0,
      unitsSold: salesData.unitsSold || 0,
      
      // Deductions
      allowedDeductions: this.calculateAllowedDeductions(license, salesData),
      royaltyBase: 0,
      
      // Royalty calculation
      royaltyRate: license.financialTerms.royaltyRate || 0,
      royaltyAmount: 0,
      
      // Adjustments
      minimumRoyalty: license.financialTerms.minimumRoyalty || 0,
      maximumRoyalty: license.financialTerms.maximumRoyalty,
      advanceRecoupment: 0,
      
      // Final amounts
      grossRoyalty: 0,
      netRoyalty: 0,
      
      // Currency conversion (if applicable)
      originalCurrency: salesData.currency || license.financialTerms.currency,
      convertedAmount: 0,
      exchangeRate: 1.0
    };

    // Calculate royalty base
    royaltyCalculation.royaltyBase = royaltyCalculation.netSales - royaltyCalculation.allowedDeductions.total;

    // Calculate gross royalty
    royaltyCalculation.grossRoyalty = royaltyCalculation.royaltyBase * (royaltyCalculation.royaltyRate / 100);

    // Apply minimum/maximum royalty constraints
    if (royaltyCalculation.grossRoyalty < royaltyCalculation.minimumRoyalty) {
      royaltyCalculation.grossRoyalty = royaltyCalculation.minimumRoyalty;
    }

    if (royaltyCalculation.maximumRoyalty && royaltyCalculation.grossRoyalty > royaltyCalculation.maximumRoyalty) {
      royaltyCalculation.grossRoyalty = royaltyCalculation.maximumRoyalty;
    }

    // Handle advance recoupment
    royaltyCalculation.advanceRecoupment = this.calculateAdvanceRecoupment(license, royaltyCalculation.grossRoyalty);
    royaltyCalculation.netRoyalty = Math.max(0, royaltyCalculation.grossRoyalty - royaltyCalculation.advanceRecoupment);

    this.royaltyTracking.set(royaltyCalculation.id, royaltyCalculation);
    return royaltyCalculation;
  }

  // ============================================================================
  // HELPER METHODS
  // ============================================================================

  /**
   * Setup compliance monitoring for a license
   */
  setupComplianceMonitoring(license) {
    // Implementation for setting up automated compliance monitoring
  }

  /**
   * Initialize royalty tracking
   */
  initializeRoyaltyTracking(license) {
    const tracking = {
      licenseId: license.id,
      totalRoyaltiesPaid: 0,
      outstandingAdvances: license.financialTerms.advancePayment || 0,
      lastPaymentDate: null,
      nextPaymentDue: this.calculateNextPaymentDate(license),
      paymentHistory: []
    };

    this.royaltyTracking.set(license.id, tracking);
  }

  /**
   * Inherit rights from parent license (cannot exceed parent scope)
   */
  inheritRights(parentRights, requestedRights) {
    const inheritedRights = {};
    
    Object.keys(parentRights).forEach(right => {
      inheritedRights[right] = parentRights[right] && (requestedRights?.[right] || false);
    });

    return inheritedRights;
  }

  /**
   * Inherit territory from parent license
   */
  inheritTerritory(parentTerritory, requestedTerritory) {
    // Sublicense territory cannot exceed parent territory
    if (!requestedTerritory) return parentTerritory;

    return {
      ...parentTerritory,
      countries: this.intersectArrays(parentTerritory.countries, requestedTerritory.countries),
      regions: this.intersectArrays(parentTerritory.regions, requestedTerritory.regions)
    };
  }

  /**
   * Inherit field of use from parent license
   */
  inheritFieldOfUse(parentFieldOfUse, requestedFieldOfUse) {
    if (!requestedFieldOfUse) return parentFieldOfUse;

    return {
      ...parentFieldOfUse,
      categories: this.intersectArrays(parentFieldOfUse.categories, requestedFieldOfUse.categories),
      specificUses: this.intersectArrays(parentFieldOfUse.specificUses, requestedFieldOfUse.specificUses)
    };
  }

  /**
   * Calculate parent royalties for sublicense
   */
  calculateParentRoyalties(parentLicense, sublicenseFinancialTerms) {
    const parentRoyaltyRate = parentLicense.sublicensingRights.sublicenseRoyalties?.parentRate || 50; // 50% default
    
    return {
      rate: parentRoyaltyRate,
      calculation: 'percentage_of_sublicense_royalties',
      paymentFrequency: parentLicense.financialTerms.paymentFrequency
    };
  }

  /**
   * Request sublicense approval
   */
  requestSublicenseApproval(sublicense) {
    // Implementation for sublicense approval workflow
    sublicense.approvalStatus = 'pending';
    sublicense.approvalRequestDate = new Date().toISOString();
  }

  /**
   * Compliance checking methods
   */
  checkUsageCompliance(license) {
    return {
      status: 'compliant',
      violations: [],
      warnings: [],
      recommendations: []
    };
  }

  checkTerritorialCompliance(license) {
    return {
      status: 'compliant',
      violations: [],
      warnings: [],
      recommendations: []
    };
  }

  checkFinancialCompliance(license) {
    return {
      status: 'compliant',
      violations: [],
      warnings: [],
      recommendations: []
    };
  }

  checkReportingCompliance(license) {
    return {
      status: 'compliant',
      violations: [],
      warnings: [],
      recommendations: []
    };
  }

  checkQualityCompliance(license) {
    return {
      status: 'compliant',
      violations: [],
      warnings: [],
      recommendations: []
    };
  }

  /**
   * Calculate allowed deductions for royalty base
   */
  calculateAllowedDeductions(license, salesData) {
    const deductions = {
      returns: salesData.returns || 0,
      discounts: salesData.discounts || 0,
      taxes: salesData.taxes || 0,
      shipping: salesData.shipping || 0,
      total: 0
    };

    deductions.total = Object.values(deductions).reduce((sum, value) => sum + (typeof value === 'number' ? value : 0), 0) - deductions.total;
    return deductions;
  }

  /**
   * Calculate advance recoupment
   */
  calculateAdvanceRecoupment(license, grossRoyalty) {
    const tracking = this.royaltyTracking.get(license.id);
    if (!tracking || tracking.outstandingAdvances <= 0) {
      return 0;
    }

    const recoupment = Math.min(grossRoyalty, tracking.outstandingAdvances);
    tracking.outstandingAdvances -= recoupment;
    
    return recoupment;
  }

  /**
   * Calculate next payment date
   */
  calculateNextPaymentDate(license) {
    const frequency = license.financialTerms.paymentFrequency;
    const now = new Date();
    
    switch (frequency) {
      case 'monthly':
        return new Date(now.getFullYear(), now.getMonth() + 1, 1);
      case 'quarterly':
        return new Date(now.getFullYear(), Math.floor(now.getMonth() / 3) * 3 + 3, 1);
      case 'annually':
        return new Date(now.getFullYear() + 1, 0, 1);
      default:
        return new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000); // 30 days
    }
  }

  /**
   * Utility method to intersect arrays
   */
  intersectArrays(arr1, arr2) {
    if (!arr1 || !arr2) return arr1 || arr2 || [];
    return arr1.filter(item => arr2.includes(item));
  }

  /**
   * Generate unique IDs
   */
  generateLicenseId() {
    return 'license_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  generateSublicenseId() {
    return 'sublicense_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  generateComplianceId() {
    return 'compliance_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  generateRoyaltyId() {
    return 'royalty_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  /**
   * Get all licenses
   */
  getAllLicenses() {
    return Array.from(this.licenses.values());
  }

  /**
   * Get licenses by licensor
   */
  getLicensesByLicensor(licensorId) {
    return Array.from(this.licenses.values()).filter(license => license.licensor === licensorId);
  }

  /**
   * Get licenses by licensee
   */
  getLicensesByLicensee(licenseeId) {
    return Array.from(this.licenses.values()).filter(license => license.licensee === licenseeId);
  }

  /**
   * Export licensing data
   */
  exportLicensingData() {
    return {
      licenses: Array.from(this.licenses.values()),
      sublicenses: Array.from(this.sublicenses.values()),
      compliance: Array.from(this.licenseCompliance.values()),
      royalties: Array.from(this.royaltyTracking.values()),
      exportedAt: new Date().toISOString()
    };
  }
}

// Export the licensing framework
export const licensingFramework = new LicensingFramework();
