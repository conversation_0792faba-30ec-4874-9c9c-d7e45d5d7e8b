/**
 * Expense Management System
 * 
 * Comprehensive system for handling expense deductions, cost allocation, and net revenue calculations:
 * - Expense tracking and categorization
 * - Cost allocation and distribution
 * - Revenue deduction calculations
 * - Expense approval workflows
 * - Financial reporting and analytics
 */

// ============================================================================
// EXPENSE CATEGORIES AND TYPES
// ============================================================================

export const EXPENSE_CATEGORIES = {
  // Development and Production Costs
  DEVELOPMENT_COSTS: 'development_costs',
  PRODUCTION_COSTS: 'production_costs',
  EQUIPMENT_COSTS: 'equipment_costs',
  SOFTWARE_LICENSES: 'software_licenses',
  
  // Operational Expenses
  HOSTING_INFRASTRUCTURE: 'hosting_infrastructure',
  PLATFORM_FEES: 'platform_fees',
  PAYMENT_PROCESSING: 'payment_processing',
  CUSTOMER_SUPPORT: 'customer_support',
  
  // Marketing and Sales
  MARKETING_EXPENSES: 'marketing_expenses',
  ADVERTISING_COSTS: 'advertising_costs',
  PROMOTIONAL_MATERIALS: 'promotional_materials',
  SALES_COMMISSIONS: 'sales_commissions',
  
  // Legal and Professional
  LEGAL_FEES: 'legal_fees',
  ACCOUNTING_FEES: 'accounting_fees',
  CONSULTING_FEES: 'consulting_fees',
  PROFESSIONAL_SERVICES: 'professional_services',
  
  // Third-party and Licensing
  THIRD_PARTY_LICENSES: 'third_party_licenses',
  API_USAGE_FEES: 'api_usage_fees',
  CONTENT_LICENSING: 'content_licensing',
  DISTRIBUTION_COSTS: 'distribution_costs',
  
  // Administrative
  OFFICE_EXPENSES: 'office_expenses',
  TRAVEL_EXPENSES: 'travel_expenses',
  COMMUNICATION_COSTS: 'communication_costs',
  INSURANCE: 'insurance',
  
  // Other
  MISCELLANEOUS: 'miscellaneous',
  CONTINGENCY: 'contingency'
};

export const EXPENSE_ALLOCATION_METHODS = {
  DIRECT: 'direct',                    // Directly attributable to specific project/contributor
  PROPORTIONAL: 'proportional',        // Allocated based on revenue/contribution share
  EQUAL_SPLIT: 'equal_split',         // Split equally among all contributors
  USAGE_BASED: 'usage_based',         // Based on actual usage metrics
  TIME_BASED: 'time_based',           // Based on time spent on project
  CUSTOM: 'custom'                    // Custom allocation rules
};

export const EXPENSE_APPROVAL_LEVELS = {
  AUTO_APPROVED: 'auto_approved',      // Under threshold, automatically approved
  MANAGER_APPROVAL: 'manager_approval', // Requires manager/lead approval
  ADMIN_APPROVAL: 'admin_approval',    // Requires admin approval
  BOARD_APPROVAL: 'board_approval'     // Requires board/owner approval
};

// ============================================================================
// EXPENSE MANAGEMENT SYSTEM CLASS
// ============================================================================

export class ExpenseManagementSystem {
  constructor() {
    this.expenses = new Map();
    this.expenseCategories = new Map();
    this.allocationRules = new Map();
    this.approvalWorkflows = new Map();
    this.expenseHistory = [];
  }

  /**
   * Define expense category with allocation rules
   */
  defineExpenseCategory(categoryDefinition) {
    const category = {
      id: categoryDefinition.id || this.generateCategoryId(),
      name: categoryDefinition.name,
      type: categoryDefinition.type,
      description: categoryDefinition.description,
      
      // Allocation configuration
      defaultAllocationMethod: categoryDefinition.defaultAllocationMethod || EXPENSE_ALLOCATION_METHODS.PROPORTIONAL,
      allocationRules: categoryDefinition.allocationRules || {},
      
      // Approval configuration
      approvalRequired: categoryDefinition.approvalRequired || false,
      approvalThreshold: categoryDefinition.approvalThreshold || 0,
      approvalLevel: categoryDefinition.approvalLevel || EXPENSE_APPROVAL_LEVELS.AUTO_APPROVED,
      
      // Deduction configuration
      deductibleFromRevenue: categoryDefinition.deductibleFromRevenue !== false,
      deductionCap: categoryDefinition.deductionCap || null, // Percentage of revenue
      deductionPriority: categoryDefinition.deductionPriority || 1,
      
      // Recoupment configuration
      recoupable: categoryDefinition.recoupable || false,
      recoupmentTerms: categoryDefinition.recoupmentTerms || {},
      
      // Metadata
      industry: categoryDefinition.industry,
      collaborationType: categoryDefinition.collaborationType,
      isActive: true,
      createdAt: new Date().toISOString()
    };

    this.expenseCategories.set(category.id, category);
    return category;
  }

  /**
   * Record an expense
   */
  recordExpense(expenseData) {
    const expense = {
      id: expenseData.id || this.generateExpenseId(),
      
      // Basic expense information
      description: expenseData.description,
      amount: expenseData.amount,
      currency: expenseData.currency || 'USD',
      date: expenseData.date || new Date().toISOString(),
      
      // Categorization
      categoryId: expenseData.categoryId,
      subcategory: expenseData.subcategory,
      tags: expenseData.tags || [],
      
      // Project/venture association
      projectId: expenseData.projectId,
      allianceId: expenseData.allianceId,
      
      // Allocation information
      allocationMethod: expenseData.allocationMethod,
      allocationRules: expenseData.allocationRules || {},
      allocatedTo: expenseData.allocatedTo || [], // Array of contributor allocations
      
      // Approval information
      submittedBy: expenseData.submittedBy,
      approvalStatus: expenseData.approvalStatus || 'pending',
      approvedBy: expenseData.approvedBy,
      approvedAt: expenseData.approvedAt,
      
      // Supporting documentation
      receipts: expenseData.receipts || [],
      invoices: expenseData.invoices || [],
      notes: expenseData.notes,
      
      // Recoupment tracking
      recoupable: expenseData.recoupable || false,
      recoupedAmount: 0,
      recoupmentStatus: 'pending',
      
      // Metadata
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    // Auto-allocate if allocation method is specified
    if (expense.allocationMethod && !expense.allocatedTo.length) {
      expense.allocatedTo = this.autoAllocateExpense(expense, expenseData.contributors || []);
    }

    this.expenses.set(expense.id, expense);
    this.expenseHistory.push({
      action: 'created',
      expenseId: expense.id,
      timestamp: new Date().toISOString(),
      data: expense
    });

    return expense;
  }

  /**
   * Auto-allocate expense based on allocation method
   */
  autoAllocateExpense(expense, contributors) {
    const category = this.expenseCategories.get(expense.categoryId);
    const allocationMethod = expense.allocationMethod || category?.defaultAllocationMethod || EXPENSE_ALLOCATION_METHODS.PROPORTIONAL;

    switch (allocationMethod) {
      case EXPENSE_ALLOCATION_METHODS.EQUAL_SPLIT:
        return this.allocateEqualSplit(expense.amount, contributors);
      
      case EXPENSE_ALLOCATION_METHODS.PROPORTIONAL:
        return this.allocateProportional(expense.amount, contributors);
      
      case EXPENSE_ALLOCATION_METHODS.USAGE_BASED:
        return this.allocateUsageBased(expense.amount, contributors, expense.allocationRules);
      
      case EXPENSE_ALLOCATION_METHODS.TIME_BASED:
        return this.allocateTimeBased(expense.amount, contributors, expense.allocationRules);
      
      case EXPENSE_ALLOCATION_METHODS.DIRECT:
        return this.allocateDirect(expense.amount, contributors, expense.allocationRules);
      
      default:
        return this.allocateProportional(expense.amount, contributors);
    }
  }

  /**
   * Allocate expense equally among contributors
   */
  allocateEqualSplit(amount, contributors) {
    if (contributors.length === 0) return [];
    
    const perContributor = amount / contributors.length;
    
    return contributors.map(contributor => ({
      contributorId: contributor.id,
      contributorName: contributor.name,
      allocatedAmount: perContributor,
      allocationPercentage: (100 / contributors.length),
      allocationMethod: EXPENSE_ALLOCATION_METHODS.EQUAL_SPLIT
    }));
  }

  /**
   * Allocate expense proportionally based on contribution/revenue share
   */
  allocateProportional(amount, contributors) {
    const totalContribution = contributors.reduce((sum, c) => sum + (c.contributionShare || 0), 0);
    
    if (totalContribution === 0) {
      return this.allocateEqualSplit(amount, contributors);
    }
    
    return contributors.map(contributor => {
      const share = (contributor.contributionShare || 0) / totalContribution;
      return {
        contributorId: contributor.id,
        contributorName: contributor.name,
        allocatedAmount: amount * share,
        allocationPercentage: share * 100,
        allocationMethod: EXPENSE_ALLOCATION_METHODS.PROPORTIONAL,
        contributionShare: contributor.contributionShare
      };
    });
  }

  /**
   * Allocate expense based on usage metrics
   */
  allocateUsageBased(amount, contributors, allocationRules) {
    const usageMetric = allocationRules.usageMetric || 'usage';
    const totalUsage = contributors.reduce((sum, c) => sum + (c[usageMetric] || 0), 0);
    
    if (totalUsage === 0) {
      return this.allocateEqualSplit(amount, contributors);
    }
    
    return contributors.map(contributor => {
      const usage = contributor[usageMetric] || 0;
      const share = usage / totalUsage;
      return {
        contributorId: contributor.id,
        contributorName: contributor.name,
        allocatedAmount: amount * share,
        allocationPercentage: share * 100,
        allocationMethod: EXPENSE_ALLOCATION_METHODS.USAGE_BASED,
        usageAmount: usage,
        usageMetric
      };
    });
  }

  /**
   * Allocate expense based on time spent
   */
  allocateTimeBased(amount, contributors, allocationRules) {
    const timeMetric = allocationRules.timeMetric || 'hoursWorked';
    const totalTime = contributors.reduce((sum, c) => sum + (c[timeMetric] || 0), 0);
    
    if (totalTime === 0) {
      return this.allocateEqualSplit(amount, contributors);
    }
    
    return contributors.map(contributor => {
      const time = contributor[timeMetric] || 0;
      const share = time / totalTime;
      return {
        contributorId: contributor.id,
        contributorName: contributor.name,
        allocatedAmount: amount * share,
        allocationPercentage: share * 100,
        allocationMethod: EXPENSE_ALLOCATION_METHODS.TIME_BASED,
        timeAmount: time,
        timeMetric
      };
    });
  }

  /**
   * Allocate expense directly to specific contributors
   */
  allocateDirect(amount, contributors, allocationRules) {
    const directAllocations = allocationRules.directAllocations || {};
    const allocations = [];
    
    for (const contributor of contributors) {
      const allocation = directAllocations[contributor.id];
      if (allocation) {
        allocations.push({
          contributorId: contributor.id,
          contributorName: contributor.name,
          allocatedAmount: allocation.amount || 0,
          allocationPercentage: allocation.percentage || 0,
          allocationMethod: EXPENSE_ALLOCATION_METHODS.DIRECT,
          reason: allocation.reason
        });
      }
    }
    
    return allocations;
  }

  /**
   * Calculate net revenue after expense deductions
   */
  calculateNetRevenue(grossRevenue, projectId, options = {}) {
    const projectExpenses = this.getProjectExpenses(projectId, options);
    
    let netRevenue = grossRevenue;
    const deductionBreakdown = [];
    
    // Sort expenses by deduction priority
    const sortedExpenses = projectExpenses.sort((a, b) => {
      const categoryA = this.expenseCategories.get(a.categoryId);
      const categoryB = this.expenseCategories.get(b.categoryId);
      return (categoryA?.deductionPriority || 1) - (categoryB?.deductionPriority || 1);
    });
    
    for (const expense of sortedExpenses) {
      const category = this.expenseCategories.get(expense.categoryId);
      
      if (!category?.deductibleFromRevenue) continue;
      
      let deductionAmount = expense.amount;
      
      // Apply deduction cap if specified
      if (category.deductionCap) {
        const maxDeduction = grossRevenue * (category.deductionCap / 100);
        deductionAmount = Math.min(deductionAmount, maxDeduction);
      }
      
      // Don't deduct more than remaining revenue
      deductionAmount = Math.min(deductionAmount, netRevenue);
      
      if (deductionAmount > 0) {
        netRevenue -= deductionAmount;
        deductionBreakdown.push({
          expenseId: expense.id,
          categoryName: category.name,
          originalAmount: expense.amount,
          deductedAmount: deductionAmount,
          cappedByLimit: deductionAmount < expense.amount,
          remainingRevenue: netRevenue
        });
      }
    }
    
    return {
      grossRevenue,
      netRevenue: Math.max(0, netRevenue),
      totalDeductions: grossRevenue - netRevenue,
      deductionBreakdown,
      expensesSummary: this.summarizeExpenses(projectExpenses)
    };
  }

  /**
   * Get expenses for a specific project
   */
  getProjectExpenses(projectId, options = {}) {
    const { startDate, endDate, categoryIds, approvedOnly = true } = options;
    
    let expenses = Array.from(this.expenses.values()).filter(expense => 
      expense.projectId === projectId
    );
    
    // Filter by approval status
    if (approvedOnly) {
      expenses = expenses.filter(expense => expense.approvalStatus === 'approved');
    }
    
    // Filter by date range
    if (startDate || endDate) {
      expenses = expenses.filter(expense => {
        const expenseDate = new Date(expense.date);
        if (startDate && expenseDate < new Date(startDate)) return false;
        if (endDate && expenseDate > new Date(endDate)) return false;
        return true;
      });
    }
    
    // Filter by categories
    if (categoryIds && categoryIds.length > 0) {
      expenses = expenses.filter(expense => categoryIds.includes(expense.categoryId));
    }
    
    return expenses;
  }

  /**
   * Summarize expenses by category
   */
  summarizeExpenses(expenses) {
    const summary = {};
    
    expenses.forEach(expense => {
      const category = this.expenseCategories.get(expense.categoryId);
      const categoryName = category?.name || 'Unknown';
      
      if (!summary[categoryName]) {
        summary[categoryName] = {
          count: 0,
          totalAmount: 0,
          averageAmount: 0,
          deductible: category?.deductibleFromRevenue || false
        };
      }
      
      summary[categoryName].count++;
      summary[categoryName].totalAmount += expense.amount;
      summary[categoryName].averageAmount = summary[categoryName].totalAmount / summary[categoryName].count;
    });
    
    return summary;
  }

  /**
   * Process expense recoupment
   */
  processRecoupment(projectId, availableRevenue, options = {}) {
    const recoupableExpenses = this.getRecoupableExpenses(projectId);
    
    let remainingRevenue = availableRevenue;
    const recoupmentResults = [];
    
    // Sort by recoupment priority (oldest first, or by priority rules)
    const sortedExpenses = recoupableExpenses.sort((a, b) => {
      return new Date(a.date) - new Date(b.date);
    });
    
    for (const expense of sortedExpenses) {
      if (remainingRevenue <= 0) break;
      
      const outstandingAmount = expense.amount - expense.recoupedAmount;
      const recoupAmount = Math.min(outstandingAmount, remainingRevenue);
      
      if (recoupAmount > 0) {
        expense.recoupedAmount += recoupAmount;
        expense.recoupmentStatus = expense.recoupedAmount >= expense.amount ? 'complete' : 'partial';
        expense.updatedAt = new Date().toISOString();
        
        remainingRevenue -= recoupAmount;
        
        recoupmentResults.push({
          expenseId: expense.id,
          description: expense.description,
          totalAmount: expense.amount,
          previouslyRecouped: expense.recoupedAmount - recoupAmount,
          currentRecoupment: recoupAmount,
          totalRecouped: expense.recoupedAmount,
          remainingToRecoup: expense.amount - expense.recoupedAmount,
          status: expense.recoupmentStatus
        });
        
        // Update expense in storage
        this.expenses.set(expense.id, expense);
      }
    }
    
    return {
      totalAvailableRevenue: availableRevenue,
      totalRecouped: availableRevenue - remainingRevenue,
      remainingRevenue,
      recoupmentResults,
      outstandingRecoupableAmount: this.getOutstandingRecoupableAmount(projectId)
    };
  }

  /**
   * Get recoupable expenses for a project
   */
  getRecoupableExpenses(projectId) {
    return Array.from(this.expenses.values()).filter(expense => 
      expense.projectId === projectId && 
      expense.recoupable && 
      expense.recoupmentStatus !== 'complete' &&
      expense.approvalStatus === 'approved'
    );
  }

  /**
   * Get outstanding recoupable amount
   */
  getOutstandingRecoupableAmount(projectId) {
    const recoupableExpenses = this.getRecoupableExpenses(projectId);
    return recoupableExpenses.reduce((sum, expense) => 
      sum + (expense.amount - expense.recoupedAmount), 0
    );
  }

  /**
   * Approve expense
   */
  approveExpense(expenseId, approvedBy, notes = '') {
    const expense = this.expenses.get(expenseId);
    if (!expense) {
      throw new Error(`Expense ${expenseId} not found`);
    }
    
    expense.approvalStatus = 'approved';
    expense.approvedBy = approvedBy;
    expense.approvedAt = new Date().toISOString();
    expense.updatedAt = new Date().toISOString();
    
    if (notes) {
      expense.approvalNotes = notes;
    }
    
    this.expenses.set(expenseId, expense);
    
    this.expenseHistory.push({
      action: 'approved',
      expenseId,
      approvedBy,
      timestamp: new Date().toISOString(),
      notes
    });
    
    return expense;
  }

  /**
   * Reject expense
   */
  rejectExpense(expenseId, rejectedBy, reason) {
    const expense = this.expenses.get(expenseId);
    if (!expense) {
      throw new Error(`Expense ${expenseId} not found`);
    }
    
    expense.approvalStatus = 'rejected';
    expense.rejectedBy = rejectedBy;
    expense.rejectedAt = new Date().toISOString();
    expense.rejectionReason = reason;
    expense.updatedAt = new Date().toISOString();
    
    this.expenses.set(expenseId, expense);
    
    this.expenseHistory.push({
      action: 'rejected',
      expenseId,
      rejectedBy,
      reason,
      timestamp: new Date().toISOString()
    });
    
    return expense;
  }

  /**
   * Generate expense analytics
   */
  generateExpenseAnalytics(projectId, options = {}) {
    const expenses = this.getProjectExpenses(projectId, { ...options, approvedOnly: false });
    
    const analytics = {
      totalExpenses: expenses.length,
      totalAmount: expenses.reduce((sum, e) => sum + e.amount, 0),
      approvedAmount: expenses.filter(e => e.approvalStatus === 'approved').reduce((sum, e) => sum + e.amount, 0),
      pendingAmount: expenses.filter(e => e.approvalStatus === 'pending').reduce((sum, e) => sum + e.amount, 0),
      rejectedAmount: expenses.filter(e => e.approvalStatus === 'rejected').reduce((sum, e) => sum + e.amount, 0),
      
      categoryBreakdown: this.summarizeExpenses(expenses),
      
      recoupmentSummary: {
        totalRecoupable: expenses.filter(e => e.recoupable).reduce((sum, e) => sum + e.amount, 0),
        totalRecouped: expenses.filter(e => e.recoupable).reduce((sum, e) => sum + e.recoupedAmount, 0),
        outstandingRecoupable: this.getOutstandingRecoupableAmount(projectId)
      },
      
      monthlyTrend: this.calculateMonthlyExpenseTrend(expenses),
      topExpenseCategories: this.getTopExpenseCategories(expenses, 5)
    };
    
    return analytics;
  }

  /**
   * Calculate monthly expense trend
   */
  calculateMonthlyExpenseTrend(expenses) {
    const monthlyData = {};
    
    expenses.forEach(expense => {
      const month = new Date(expense.date).toISOString().substring(0, 7); // YYYY-MM
      if (!monthlyData[month]) {
        monthlyData[month] = { count: 0, amount: 0 };
      }
      monthlyData[month].count++;
      monthlyData[month].amount += expense.amount;
    });
    
    return Object.entries(monthlyData)
      .sort(([a], [b]) => a.localeCompare(b))
      .map(([month, data]) => ({ month, ...data }));
  }

  /**
   * Get top expense categories by amount
   */
  getTopExpenseCategories(expenses, limit = 5) {
    const categoryTotals = {};
    
    expenses.forEach(expense => {
      const category = this.expenseCategories.get(expense.categoryId);
      const categoryName = category?.name || 'Unknown';
      
      if (!categoryTotals[categoryName]) {
        categoryTotals[categoryName] = 0;
      }
      categoryTotals[categoryName] += expense.amount;
    });
    
    return Object.entries(categoryTotals)
      .sort(([, a], [, b]) => b - a)
      .slice(0, limit)
      .map(([category, amount]) => ({ category, amount }));
  }

  /**
   * Generate unique category ID
   */
  generateCategoryId() {
    return 'category_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  /**
   * Generate unique expense ID
   */
  generateExpenseId() {
    return 'expense_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  /**
   * Get all expense categories
   */
  getAllCategories() {
    return Array.from(this.expenseCategories.values());
  }

  /**
   * Get all expenses
   */
  getAllExpenses() {
    return Array.from(this.expenses.values());
  }

  /**
   * Export expense data
   */
  exportExpenseData(projectId, format = 'json') {
    const expenses = this.getProjectExpenses(projectId, { approvedOnly: false });
    const analytics = this.generateExpenseAnalytics(projectId);
    
    const data = {
      projectId,
      expenses,
      analytics,
      categories: this.getAllCategories(),
      exportedAt: new Date().toISOString()
    };
    
    if (format === 'csv') {
      return this.convertExpenseDataToCSV(data);
    }
    
    return data;
  }

  /**
   * Convert expense data to CSV format
   */
  convertExpenseDataToCSV(data) {
    // Implementation for CSV conversion
    return 'CSV conversion not implemented yet';
  }
}

// Export the expense management system
export const expenseManagementSystem = new ExpenseManagementSystem();
