/**
 * Agreement Document Comparison Test Suite
 * 
 * This test suite focuses specifically on comparing generated agreements
 * against the lawyer-approved template to ensure format compliance and
 * structural accuracy.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { NewAgreementGenerator } from '../utils/agreement/newAgreementGenerator.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('📋 AGREEMENT DOCUMENT COMPARISON TESTS');
console.log('=====================================\n');

// Configuration
const config = {
  templatePath: path.join(__dirname, '../../public/example-cog-contributor-agreement.md'),
  outputDir: path.join(__dirname, 'output', 'document-comparison'),
  saveComparisons: true
};

// Ensure output directory exists
if (!fs.existsSync(config.outputDir)) {
  fs.mkdirSync(config.outputDir, { recursive: true });
}

// Load lawyer-approved template
let lawyerTemplate = '';
try {
  lawyerTemplate = fs.readFileSync(config.templatePath, 'utf8');
  console.log('✅ Lawyer-approved template loaded');
  console.log(`   Template length: ${lawyerTemplate.length} characters`);
  console.log(`   Template lines: ${lawyerTemplate.split('\n').length}`);
} catch (error) {
  console.error('❌ Failed to load template:', error.message);
  process.exit(1);
}

// Document comparison class
class AgreementDocumentComparator {
  constructor(templateDocument) {
    this.template = templateDocument;
    this.templateSections = this.extractSections(templateDocument);
  }
  
  extractSections(document) {
    const sections = {};
    const lines = document.split('\n');
    let currentSection = null;
    let currentContent = [];
    
    for (const line of lines) {
      // Check for section headers (## format)
      const sectionMatch = line.match(/^##\s+(\d+\.?\s*.+)/);
      if (sectionMatch) {
        // Save previous section
        if (currentSection) {
          sections[currentSection] = currentContent.join('\n').trim();
        }
        // Start new section
        currentSection = sectionMatch[1];
        currentContent = [line];
      } else if (currentSection) {
        currentContent.push(line);
      }
    }
    
    // Save last section
    if (currentSection) {
      sections[currentSection] = currentContent.join('\n').trim();
    }
    
    return sections;
  }
  
  compareDocuments(generatedDocument, testName = 'Unknown') {
    const comparison = {
      testName,
      timestamp: new Date().toISOString(),
      
      // Structure analysis
      structure: {
        templateSections: Object.keys(this.templateSections).length,
        generatedSections: 0,
        matchingSections: [],
        missingSections: [],
        extraSections: []
      },
      
      // Content analysis
      content: {
        totalLines: generatedDocument.split('\n').length,
        templateLines: this.template.split('\n').length,
        unreplacedVariables: [],
        unfilledPlaceholders: [],
        formatIssues: []
      },
      
      // Key elements check
      keyElements: {
        present: [],
        missing: [],
        score: 0
      },
      
      // Overall assessment
      overall: {
        score: 0,
        grade: 'F',
        ready: false
      }
    };
    
    // Extract sections from generated document
    const generatedSections = this.extractSections(generatedDocument);
    comparison.structure.generatedSections = Object.keys(generatedSections).length;
    
    // Compare sections
    for (const templateSection of Object.keys(this.templateSections)) {
      if (generatedSections[templateSection]) {
        comparison.structure.matchingSections.push(templateSection);
      } else {
        comparison.structure.missingSections.push(templateSection);
      }
    }
    
    for (const generatedSection of Object.keys(generatedSections)) {
      if (!this.templateSections[generatedSection]) {
        comparison.structure.extraSections.push(generatedSection);
      }
    }
    
    // Check for key elements that must be present
    const keyElements = [
      'CONTRIBUTOR AGREEMENT',
      'This Contributor Agreement',
      'WHEREAS',
      'NOW THEREFORE',
      'Definitions',
      'Treatment of Confidential Information',
      'Ownership of Work Product',
      'Termination',
      'SCHEDULE A',
      'EXHIBIT I',
      'EXHIBIT II',
      'IN WITNESS WHEREOF'
    ];
    
    for (const element of keyElements) {
      if (generatedDocument.includes(element)) {
        comparison.keyElements.present.push(element);
      } else {
        comparison.keyElements.missing.push(element);
      }
    }
    
    comparison.keyElements.score = Math.round(
      (comparison.keyElements.present.length / keyElements.length) * 100
    );
    
    // Check for unreplaced variables and placeholders
    comparison.content.unreplacedVariables = (generatedDocument.match(/\{\{[^}]+\}\}/g) || []);
    comparison.content.unfilledPlaceholders = (generatedDocument.match(/\[[^\]]*\]/g) || [])
      .filter(placeholder => 
        placeholder.includes('_') || 
        placeholder.includes('TO BE FILLED') ||
        placeholder.includes('__')
      );
    
    // Check for format issues
    if (comparison.content.unreplacedVariables.length > 0) {
      comparison.content.formatIssues.push(`${comparison.content.unreplacedVariables.length} unreplaced variables`);
    }
    
    if (comparison.content.unfilledPlaceholders.length > 0) {
      comparison.content.formatIssues.push(`${comparison.content.unfilledPlaceholders.length} unfilled placeholders`);
    }
    
    // Calculate overall score
    const structureScore = (comparison.structure.matchingSections.length / comparison.structure.templateSections) * 100;
    const keyElementsScore = comparison.keyElements.score;
    const formatScore = Math.max(0, 100 - (comparison.content.formatIssues.length * 20));
    
    comparison.overall.score = Math.round((structureScore + keyElementsScore + formatScore) / 3);
    
    // Assign grade
    if (comparison.overall.score >= 95) {
      comparison.overall.grade = 'A+';
      comparison.overall.ready = true;
    } else if (comparison.overall.score >= 90) {
      comparison.overall.grade = 'A';
      comparison.overall.ready = true;
    } else if (comparison.overall.score >= 85) {
      comparison.overall.grade = 'B+';
      comparison.overall.ready = true;
    } else if (comparison.overall.score >= 80) {
      comparison.overall.grade = 'B';
      comparison.overall.ready = false;
    } else if (comparison.overall.score >= 70) {
      comparison.overall.grade = 'C';
      comparison.overall.ready = false;
    } else {
      comparison.overall.grade = 'F';
      comparison.overall.ready = false;
    }
    
    return comparison;
  }
  
  generateComparisonReport(comparison) {
    return `# Agreement Document Comparison Report

## Test Information
- **Test Name**: ${comparison.testName}
- **Generated**: ${comparison.timestamp}
- **Overall Score**: ${comparison.overall.score}%
- **Grade**: ${comparison.overall.grade}
- **Production Ready**: ${comparison.overall.ready ? '✅ YES' : '❌ NO'}

## Structure Analysis
- **Template Sections**: ${comparison.structure.templateSections}
- **Generated Sections**: ${comparison.structure.generatedSections}
- **Matching Sections**: ${comparison.structure.matchingSections.length}
- **Missing Sections**: ${comparison.structure.missingSections.length}
- **Extra Sections**: ${comparison.structure.extraSections.length}

### Matching Sections
${comparison.structure.matchingSections.map(s => `- ✅ ${s}`).join('\n')}

### Missing Sections
${comparison.structure.missingSections.map(s => `- ❌ ${s}`).join('\n')}

### Extra Sections
${comparison.structure.extraSections.map(s => `- ➕ ${s}`).join('\n')}

## Key Elements Analysis
- **Score**: ${comparison.keyElements.score}%
- **Present**: ${comparison.keyElements.present.length}
- **Missing**: ${comparison.keyElements.missing.length}

### Present Elements
${comparison.keyElements.present.map(e => `- ✅ ${e}`).join('\n')}

### Missing Elements
${comparison.keyElements.missing.map(e => `- ❌ ${e}`).join('\n')}

## Content Analysis
- **Total Lines**: ${comparison.content.totalLines}
- **Template Lines**: ${comparison.content.templateLines}
- **Unreplaced Variables**: ${comparison.content.unreplacedVariables.length}
- **Unfilled Placeholders**: ${comparison.content.unfilledPlaceholders.length}
- **Format Issues**: ${comparison.content.formatIssues.length}

### Format Issues
${comparison.content.formatIssues.map(i => `- ⚠️ ${i}`).join('\n')}

### Unreplaced Variables (First 10)
${comparison.content.unreplacedVariables.slice(0, 10).map(v => `- \`${v}\``).join('\n')}

### Unfilled Placeholders (First 10)
${comparison.content.unfilledPlaceholders.slice(0, 10).map(p => `- \`${p}\``).join('\n')}

## Recommendations
${comparison.overall.ready ? 
  '✅ Document meets production standards and is ready for use.' : 
  `❌ Document requires improvements before production use:
${comparison.structure.missingSections.length > 0 ? '- Add missing sections' : ''}
${comparison.keyElements.missing.length > 0 ? '- Include missing key elements' : ''}
${comparison.content.formatIssues.length > 0 ? '- Fix format issues' : ''}
${comparison.content.unreplacedVariables.length > 0 ? '- Replace all template variables' : ''}
${comparison.content.unfilledPlaceholders.length > 0 ? '- Fill all placeholders' : ''}`
}
`;
  }
}

// Initialize comparator
const comparator = new AgreementDocumentComparator(lawyerTemplate);

console.log('🔍 Document Comparator Initialized');
console.log(`   Template sections identified: ${Object.keys(comparator.templateSections).length}`);

// Test scenarios for document comparison
const testScenarios = [
  {
    name: 'CoG VOTA Lead Developer',
    project: {
      id: 'vota_001',
      name: 'Village of The Ages',
      title: 'Village of The Ages',
      description: 'A village simulation game where players guide communities through historical progressions',
      project_type: 'software',
      team_id: 'cog_alliance_001',
      alliance_id: 'cog_alliance_001'
    },
    contributor: {
      id: 'dev_001',
      name: 'Lead Developer',
      email: '<EMAIL>',
      role: 'Technical Lead'
    }
  },
  {
    name: 'CoG VOTA Game Designer',
    project: {
      id: 'vota_001',
      name: 'Village of The Ages',
      title: 'Village of The Ages',
      description: 'A village simulation game where players guide communities through historical progressions',
      project_type: 'software',
      team_id: 'cog_alliance_001',
      alliance_id: 'cog_alliance_001'
    },
    contributor: {
      id: 'designer_001',
      name: 'Game Designer',
      email: '<EMAIL>',
      role: 'Creative Director'
    }
  }
];

console.log('\n🧪 RUNNING DOCUMENT COMPARISON TESTS');
console.log('====================================');

const agreementGenerator = new NewAgreementGenerator();
const comparisonResults = [];

// Run comparison tests
for (const scenario of testScenarios) {
  console.log(`\n📋 Testing: ${scenario.name}`);
  
  try {
    // Generate agreement
    const agreement = await agreementGenerator.generateAgreement(
      lawyerTemplate,
      scenario.project,
      {
        contributors: [scenario.contributor],
        currentUser: {
          id: scenario.contributor.id,
          email: scenario.contributor.email,
          user_metadata: {
            full_name: scenario.contributor.name
          }
        },
        fullName: scenario.contributor.name
      }
    );
    
    if (agreement) {
      console.log('   ✅ Agreement generated successfully');
      
      // Perform comparison
      const comparison = comparator.compareDocuments(agreement, scenario.name);
      comparisonResults.push(comparison);
      
      console.log(`   📊 Overall Score: ${comparison.overall.score}% (${comparison.overall.grade})`);
      console.log(`   🏗️  Structure: ${comparison.structure.matchingSections.length}/${comparison.structure.templateSections} sections`);
      console.log(`   🔑 Key Elements: ${comparison.keyElements.score}%`);
      console.log(`   📝 Format Issues: ${comparison.content.formatIssues.length}`);
      console.log(`   🚀 Production Ready: ${comparison.overall.ready ? 'YES' : 'NO'}`);
      
      // Save agreement and comparison report
      if (config.saveComparisons) {
        const agreementFilename = `${scenario.name.toLowerCase().replace(/\s+/g, '-')}-agreement.md`;
        const reportFilename = `${scenario.name.toLowerCase().replace(/\s+/g, '-')}-comparison-report.md`;
        
        fs.writeFileSync(path.join(config.outputDir, agreementFilename), agreement);
        fs.writeFileSync(path.join(config.outputDir, reportFilename), comparator.generateComparisonReport(comparison));
        
        console.log(`   💾 Saved: ${agreementFilename} & ${reportFilename}`);
      }
      
    } else {
      console.log('   ❌ Agreement generation failed');
      comparisonResults.push({
        testName: scenario.name,
        error: 'Agreement generation failed',
        overall: { score: 0, grade: 'F', ready: false }
      });
    }
    
  } catch (error) {
    console.error(`   ❌ Error: ${error.message}`);
    comparisonResults.push({
      testName: scenario.name,
      error: error.message,
      overall: { score: 0, grade: 'F', ready: false }
    });
  }
}

console.log('\n📊 COMPARISON SUMMARY');
console.log('====================');

const summary = {
  totalTests: comparisonResults.length,
  successfulTests: comparisonResults.filter(r => !r.error).length,
  productionReady: comparisonResults.filter(r => r.overall?.ready).length,
  averageScore: comparisonResults.reduce((sum, r) => sum + (r.overall?.score || 0), 0) / comparisonResults.length,
  gradeDistribution: {}
};

// Calculate grade distribution
comparisonResults.forEach(result => {
  const grade = result.overall?.grade || 'F';
  summary.gradeDistribution[grade] = (summary.gradeDistribution[grade] || 0) + 1;
});

console.log(`📈 Results Summary:`);
console.log(`   Total Tests: ${summary.totalTests}`);
console.log(`   Successful: ${summary.successfulTests}/${summary.totalTests}`);
console.log(`   Production Ready: ${summary.productionReady}/${summary.totalTests}`);
console.log(`   Average Score: ${summary.averageScore.toFixed(1)}%`);

console.log(`\n📊 Grade Distribution:`);
Object.entries(summary.gradeDistribution).forEach(([grade, count]) => {
  console.log(`   ${grade}: ${count}`);
});

console.log(`\n📋 Individual Results:`);
comparisonResults.forEach(result => {
  const status = result.error ? '❌ ERROR' : (result.overall.ready ? '✅ READY' : '⚠️ NEEDS WORK');
  console.log(`   ${result.testName}: ${result.overall?.score || 0}% (${result.overall?.grade || 'F'}) ${status}`);
});

// Save summary report
if (config.saveComparisons) {
  const summaryReport = `# Document Comparison Summary Report
## Generated: ${new Date().toISOString()}

### Summary Statistics
- **Total Tests**: ${summary.totalTests}
- **Successful Tests**: ${summary.successfulTests}/${summary.totalTests}
- **Production Ready**: ${summary.productionReady}/${summary.totalTests}
- **Average Score**: ${summary.averageScore.toFixed(1)}%

### Grade Distribution
${Object.entries(summary.gradeDistribution).map(([grade, count]) => `- **${grade}**: ${count}`).join('\n')}

### Individual Test Results
${comparisonResults.map(result => `
#### ${result.testName}
- **Score**: ${result.overall?.score || 0}%
- **Grade**: ${result.overall?.grade || 'F'}
- **Production Ready**: ${result.overall?.ready ? '✅ YES' : '❌ NO'}
${result.error ? `- **Error**: ${result.error}` : ''}
`).join('')}

### Recommendations
${summary.productionReady === summary.totalTests ? 
  '🎉 All agreements meet production standards!' : 
  `⚠️ ${summary.totalTests - summary.productionReady} agreements need improvement before production use.`
}
`;

  const summaryPath = path.join(config.outputDir, `comparison-summary-${new Date().toISOString().split('T')[0]}.md`);
  fs.writeFileSync(summaryPath, summaryReport);
  console.log(`\n💾 Summary report saved: ${path.basename(summaryPath)}`);
}

console.log(`\n📁 All results saved to: ${config.outputDir}`);
console.log('🎯 Document comparison tests completed!');

export { comparisonResults, summary, comparator };
