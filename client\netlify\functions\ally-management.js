// Ally Management API
// Backend Specialist: Complete ally connection and relationship management
const { createClient } = require('@supabase/supabase-js');

// Supabase client
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_KEY
);

// Helper function to get user from request
const getUserFromRequest = (event) => {
  const authHeader = event.headers.authorization;
  if (!authHeader) return null;
  
  try {
    const token = authHeader.replace('Bearer ', '');
    const payload = JSON.parse(Buffer.from(token.split('.')[1], 'base64').toString());
    return payload.sub;
  } catch {
    return null;
  }
};

// Get User's Allies
const getUserAllies = async (event) => {
  try {
    const userId = getUserFromRequest(event);
    if (!userId) {
      return {
        statusCode: 401,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    const queryParams = new URLSearchParams(event.queryStringParameters || {});
    const status = queryParams.get('status') || 'accepted';
    const connectionType = queryParams.get('connection_type');
    const search = queryParams.get('search');

    let query = supabase
      .from('user_allies')
      .select(`
        id,
        status,
        connection_type,
        accepted_at,
        notes,
        ally:users!user_allies_ally_id_fkey(
          id,
          display_name,
          avatar_url,
          is_premium
        ),
        user:users!user_allies_user_id_fkey(
          id,
          display_name,
          avatar_url,
          is_premium
        )
      `)
      .or(`user_id.eq.${userId},ally_id.eq.${userId}`)
      .eq('status', status);

    if (connectionType) {
      query = query.eq('connection_type', connectionType);
    }

    const { data: allies, error: alliesError } = await query
      .order('accepted_at', { ascending: false });

    if (alliesError) {
      throw new Error(`Failed to fetch allies: ${alliesError.message}`);
    }

    // Transform allies to show the other user in the connection
    let transformedAllies = allies.map(ally => {
      const isUserInitiator = ally.user.id === userId;
      const allyUser = isUserInitiator ? ally.ally : ally.user;
      
      return {
        id: ally.id,
        ally: allyUser,
        connection_type: ally.connection_type,
        status: ally.status,
        accepted_at: ally.accepted_at,
        notes: ally.notes,
        is_mutual: true // All accepted connections are mutual
      };
    });

    // Apply search filter if provided
    if (search) {
      const searchLower = search.toLowerCase();
      transformedAllies = transformedAllies.filter(ally => 
        ally.ally.display_name?.toLowerCase().includes(searchLower)
      );
    }

    // Get mutual connections count for each ally
    for (let ally of transformedAllies) {
      const { data: mutualCount } = await supabase
        .rpc('get_mutual_allies', { 
          user1_id: userId, 
          user2_id: ally.ally.id 
        });
      
      ally.mutual_connections_count = mutualCount?.length || 0;
    }

    return {
      statusCode: 200,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ 
        allies: transformedAllies,
        total: transformedAllies.length
      })
    };

  } catch (error) {
    console.error('Get user allies error:', error);
    return {
      statusCode: 500,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ error: 'Failed to fetch allies' })
    };
  }
};

// Get Ally Details
const getAllyDetails = async (event) => {
  try {
    const userId = getUserFromRequest(event);
    if (!userId) {
      return {
        statusCode: 401,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    const allyId = event.path.split('/').pop();

    // Get ally connection details
    const { data: connection, error: connectionError } = await supabase
      .from('user_allies')
      .select(`
        *,
        ally:users!user_allies_ally_id_fkey(
          id,
          display_name,
          avatar_url,
          is_premium
        ),
        user:users!user_allies_user_id_fkey(
          id,
          display_name,
          avatar_url,
          is_premium
        )
      `)
      .or(`and(user_id.eq.${userId},ally_id.eq.${allyId}),and(user_id.eq.${allyId},ally_id.eq.${userId})`)
      .eq('status', 'accepted')
      .single();

    if (connectionError) {
      return {
        statusCode: 404,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Ally connection not found' })
      };
    }

    // Get the ally user (the other person in the connection)
    const allyUser = connection.user.id === userId ? connection.ally : connection.user;

    // Get mutual connections
    const { data: mutualConnections } = await supabase
      .rpc('get_mutual_allies', { 
        user1_id: userId, 
        user2_id: allyUser.id 
      });

    // Get collaboration history (projects they've worked on together)
    const { data: collaborations } = await supabase
      .from('project_contributors')
      .select(`
        project:projects(
          id,
          name,
          title,
          created_at
        )
      `)
      .eq('user_id', userId)
      .in('project_id', 
        supabase
          .from('project_contributors')
          .select('project_id')
          .eq('user_id', allyUser.id)
      );

    // Get skill endorsements between users
    const { data: endorsements } = await supabase
      .from('skill_endorsements')
      .select(`
        id,
        skill_name,
        endorsement_level,
        endorsement_message,
        project_context,
        created_at
      `)
      .or(`and(endorser_id.eq.${userId},endorsed_user_id.eq.${allyUser.id}),and(endorser_id.eq.${allyUser.id},endorsed_user_id.eq.${userId})`);

    const response = {
      connection: {
        id: connection.id,
        connection_type: connection.connection_type,
        status: connection.status,
        accepted_at: connection.accepted_at,
        notes: connection.notes
      },
      ally: allyUser,
      mutual_connections: mutualConnections || [],
      mutual_connections_count: mutualConnections?.length || 0,
      collaborations: collaborations?.map(c => c.project) || [],
      collaboration_count: collaborations?.length || 0,
      endorsements: endorsements || [],
      endorsement_count: endorsements?.length || 0
    };

    return {
      statusCode: 200,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(response)
    };

  } catch (error) {
    console.error('Get ally details error:', error);
    return {
      statusCode: 500,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ error: 'Failed to get ally details' })
    };
  }
};

// Update Ally Connection
const updateAllyConnection = async (event) => {
  try {
    const userId = getUserFromRequest(event);
    if (!userId) {
      return {
        statusCode: 401,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    const allyId = event.path.split('/').pop();
    const data = JSON.parse(event.body);

    // Find the connection
    const { data: connection, error: findError } = await supabase
      .from('user_allies')
      .select('id')
      .or(`and(user_id.eq.${userId},ally_id.eq.${allyId}),and(user_id.eq.${allyId},ally_id.eq.${userId})`)
      .single();

    if (findError) {
      return {
        statusCode: 404,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Ally connection not found' })
      };
    }

    // Update allowed fields
    const updateData = {};
    if (data.connection_type) updateData.connection_type = data.connection_type;
    if (data.notes !== undefined) updateData.notes = data.notes;
    updateData.updated_at = new Date().toISOString();

    const { data: updatedConnection, error: updateError } = await supabase
      .from('user_allies')
      .update(updateData)
      .eq('id', connection.id)
      .select()
      .single();

    if (updateError) {
      throw new Error(`Failed to update ally connection: ${updateError.message}`);
    }

    return {
      statusCode: 200,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        connection: {
          id: updatedConnection.id,
          connection_type: updatedConnection.connection_type,
          notes: updatedConnection.notes,
          updated_at: updatedConnection.updated_at
        }
      })
    };

  } catch (error) {
    console.error('Update ally connection error:', error);
    return {
      statusCode: 500,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ error: 'Failed to update ally connection' })
    };
  }
};

// Remove Ally Connection
const removeAllyConnection = async (event) => {
  try {
    const userId = getUserFromRequest(event);
    if (!userId) {
      return {
        statusCode: 401,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    const allyId = event.path.split('/').pop();

    // Find and delete the connection
    const { data: connection, error: deleteError } = await supabase
      .from('user_allies')
      .delete()
      .or(`and(user_id.eq.${userId},ally_id.eq.${allyId}),and(user_id.eq.${allyId},ally_id.eq.${userId})`)
      .select()
      .single();

    if (deleteError) {
      return {
        statusCode: 404,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Ally connection not found' })
      };
    }

    // Log the disconnection for analytics
    await supabase
      .from('social_interactions')
      .insert([{
        user_id: userId,
        target_user_id: allyId,
        interaction_type: 'ally_disconnection',
        interaction_data: { connection_id: connection.id }
      }]);

    return {
      statusCode: 200,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        message: 'Ally connection removed successfully',
        connection_id: connection.id
      })
    };

  } catch (error) {
    console.error('Remove ally connection error:', error);
    return {
      statusCode: 500,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ error: 'Failed to remove ally connection' })
    };
  }
};

// Block User
const blockUser = async (event) => {
  try {
    const userId = getUserFromRequest(event);
    if (!userId) {
      return {
        statusCode: 401,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    const targetUserId = event.path.split('/').pop();
    const data = JSON.parse(event.body);

    // Check if connection exists
    const { data: existingConnection } = await supabase
      .from('user_allies')
      .select('id')
      .or(`and(user_id.eq.${userId},ally_id.eq.${targetUserId}),and(user_id.eq.${targetUserId},ally_id.eq.${userId})`)
      .single();

    if (existingConnection) {
      // Update existing connection to blocked
      await supabase
        .from('user_allies')
        .update({ 
          status: 'blocked',
          blocked_at: new Date().toISOString(),
          notes: data.reason || 'User blocked'
        })
        .eq('id', existingConnection.id);
    } else {
      // Create new blocked connection
      await supabase
        .from('user_allies')
        .insert([{
          user_id: userId,
          ally_id: targetUserId,
          status: 'blocked',
          created_by: userId,
          blocked_at: new Date().toISOString(),
          notes: data.reason || 'User blocked'
        }]);
    }

    // Cancel any pending friend requests between users
    await supabase
      .from('friend_requests')
      .update({ status: 'cancelled' })
      .or(`and(sender_id.eq.${userId},recipient_id.eq.${targetUserId}),and(sender_id.eq.${targetUserId},recipient_id.eq.${userId})`)
      .eq('status', 'pending');

    return {
      statusCode: 200,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        message: 'User blocked successfully',
        blocked_user_id: targetUserId
      })
    };

  } catch (error) {
    console.error('Block user error:', error);
    return {
      statusCode: 500,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ error: 'Failed to block user' })
    };
  }
};

// Get Ally Statistics
const getAllyStatistics = async (event) => {
  try {
    const userId = getUserFromRequest(event);
    if (!userId) {
      return {
        statusCode: 401,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    // Get ally counts by type
    const { data: allyCounts } = await supabase
      .from('user_allies')
      .select('connection_type, status')
      .or(`user_id.eq.${userId},ally_id.eq.${userId}`)
      .eq('status', 'accepted');

    // Get pending request counts
    const { data: pendingRequests } = await supabase
      .from('friend_requests')
      .select('id')
      .eq('recipient_id', userId)
      .eq('status', 'pending');

    // Get sent request counts
    const { data: sentRequests } = await supabase
      .from('friend_requests')
      .select('id')
      .eq('sender_id', userId)
      .eq('status', 'pending');

    // Calculate statistics
    const connectionTypes = {};
    allyCounts?.forEach(ally => {
      connectionTypes[ally.connection_type] = (connectionTypes[ally.connection_type] || 0) + 1;
    });

    const statistics = {
      total_allies: allyCounts?.length || 0,
      connection_types: connectionTypes,
      pending_incoming_requests: pendingRequests?.length || 0,
      pending_outgoing_requests: sentRequests?.length || 0,
      network_growth: {
        // TODO: Calculate growth metrics over time
        this_month: 0,
        last_month: 0
      }
    };

    return {
      statusCode: 200,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(statistics)
    };

  } catch (error) {
    console.error('Get ally statistics error:', error);
    return {
      statusCode: 500,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ error: 'Failed to get ally statistics' })
    };
  }
};

// Search and Discover Allies
const searchAllies = async (event) => {
  try {
    const userId = getUserFromRequest(event);
    if (!userId) {
      return {
        statusCode: 401,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    const queryParams = new URLSearchParams(event.queryStringParameters || {});
    const query = queryParams.get('q') || '';
    const skills = queryParams.get('skills') || '';
    const location = queryParams.get('location') || '';
    const limit = parseInt(queryParams.get('limit') || '20');

    // Get user's privacy settings to respect discovery preferences
    let searchQuery = supabase
      .from('users')
      .select(`
        id,
        display_name,
        avatar_url,
        is_premium,
        user_privacy_settings(
          discoverable_by_name,
          discoverable_by_skills,
          profile_visibility
        )
      `)
      .neq('id', userId) // Exclude current user
      .limit(limit);

    // Apply text search if provided
    if (query.trim()) {
      searchQuery = searchQuery.ilike('display_name', `%${query}%`);
    }

    const { data: searchResults, error: searchError } = await searchQuery;

    if (searchError) {
      throw new Error(`Search failed: ${searchError.message}`);
    }

    // Filter results based on privacy settings
    const filteredResults = searchResults.filter(user => {
      const privacy = user.user_privacy_settings?.[0];
      if (!privacy) return true; // Default to discoverable if no settings

      return privacy.profile_visibility === 'public' &&
             privacy.discoverable_by_name;
    });

    // Get existing connections to exclude from results
    const { data: existingConnections } = await supabase
      .from('user_allies')
      .select('ally_id, user_id')
      .or(`user_id.eq.${userId},ally_id.eq.${userId}`)
      .in('status', ['accepted', 'pending', 'blocked']);

    const connectedUserIds = new Set();
    existingConnections?.forEach(conn => {
      connectedUserIds.add(conn.user_id === userId ? conn.ally_id : conn.user_id);
    });

    // Filter out already connected users
    const availableUsers = filteredResults.filter(user =>
      !connectedUserIds.has(user.id)
    );

    // Enhance results with additional data
    const enhancedResults = await Promise.all(
      availableUsers.map(async (user) => {
        // Get mutual connections count
        const { data: mutualConnections } = await supabase
          .rpc('get_mutual_allies', {
            user1_id: userId,
            user2_id: user.id
          });

        // Get user's skills if discoverable
        const { data: userSkills } = await supabase
          .from('user_skills')
          .select(`
            skill:skills(name),
            proficiency_score
          `)
          .eq('user_id', user.id)
          .gte('proficiency_score', 50) // Only show decent skills
          .limit(5);

        return {
          id: user.id,
          display_name: user.display_name,
          avatar_url: user.avatar_url,
          is_premium: user.is_premium,
          mutual_connections_count: mutualConnections?.length || 0,
          top_skills: userSkills?.map(us => us.skill?.name).filter(Boolean) || [],
          can_connect: true
        };
      })
    );

    return {
      statusCode: 200,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        users: enhancedResults,
        total: enhancedResults.length,
        query: query
      })
    };

  } catch (error) {
    console.error('Search allies error:', error);
    return {
      statusCode: 500,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ error: 'Failed to search allies' })
    };
  }
};

// Get Ally Recommendations
const getAllyRecommendations = async (event) => {
  try {
    const userId = getUserFromRequest(event);
    if (!userId) {
      return {
        statusCode: 401,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    const queryParams = new URLSearchParams(event.queryStringParameters || {});
    const type = queryParams.get('type') || 'all';
    const limit = parseInt(queryParams.get('limit') || '10');

    // Get existing recommendations
    let query = supabase
      .from('ally_recommendations')
      .select(`
        id,
        recommendation_type,
        score,
        reason_data,
        recommended_user:users!ally_recommendations_recommended_user_id_fkey(
          id,
          display_name,
          avatar_url,
          is_premium
        )
      `)
      .eq('user_id', userId)
      .is('dismissed_at', null)
      .order('score', { ascending: false })
      .limit(limit);

    if (type !== 'all') {
      query = query.eq('recommendation_type', type);
    }

    const { data: recommendations, error: recError } = await query;

    if (recError) {
      throw new Error(`Failed to get recommendations: ${recError.message}`);
    }

    // If no recommendations exist, generate some basic ones
    if (!recommendations || recommendations.length === 0) {
      await generateBasicRecommendations(userId);

      // Retry the query
      const { data: newRecommendations } = await query;

      return {
        statusCode: 200,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          recommendations: newRecommendations || [],
          total: newRecommendations?.length || 0,
          generated: true
        })
      };
    }

    // Enhance recommendations with additional data
    const enhancedRecommendations = await Promise.all(
      recommendations.map(async (rec) => {
        // Get mutual connections
        const { data: mutualConnections } = await supabase
          .rpc('get_mutual_allies', {
            user1_id: userId,
            user2_id: rec.recommended_user.id
          });

        return {
          id: rec.id,
          user: rec.recommended_user,
          recommendation_type: rec.recommendation_type,
          score: rec.score,
          reason: rec.reason_data,
          mutual_connections_count: mutualConnections?.length || 0
        };
      })
    );

    return {
      statusCode: 200,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        recommendations: enhancedRecommendations,
        total: enhancedRecommendations.length
      })
    };

  } catch (error) {
    console.error('Get ally recommendations error:', error);
    return {
      statusCode: 500,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ error: 'Failed to get recommendations' })
    };
  }
};

// Helper function to generate basic recommendations
const generateBasicRecommendations = async (userId) => {
  try {
    // Get users with mutual connections
    const { data: potentialAllies } = await supabase
      .from('users')
      .select('id, display_name')
      .neq('id', userId)
      .limit(50);

    if (!potentialAllies) return;

    const recommendations = [];

    for (const user of potentialAllies) {
      // Check if already connected
      const { data: existingConnection } = await supabase
        .from('user_allies')
        .select('id')
        .or(`and(user_id.eq.${userId},ally_id.eq.${user.id}),and(user_id.eq.${user.id},ally_id.eq.${userId})`)
        .single();

      if (existingConnection) continue;

      // Get mutual connections
      const { data: mutualConnections } = await supabase
        .rpc('get_mutual_allies', {
          user1_id: userId,
          user2_id: user.id
        });

      if (mutualConnections && mutualConnections.length > 0) {
        recommendations.push({
          user_id: userId,
          recommended_user_id: user.id,
          recommendation_type: 'mutual_connections',
          score: Math.min(0.9, mutualConnections.length * 0.2),
          reason_data: {
            mutual_count: mutualConnections.length,
            mutual_names: mutualConnections.slice(0, 3).map(m => m.ally_name)
          }
        });
      }
    }

    // Insert recommendations
    if (recommendations.length > 0) {
      await supabase
        .from('ally_recommendations')
        .insert(recommendations.slice(0, 10)); // Limit to 10 recommendations
    }

  } catch (error) {
    console.error('Generate recommendations error:', error);
  }
};

// Route requests to appropriate handlers
exports.handler = async (event, context) => {
  // Add CORS headers
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS'
  };

  // Handle preflight requests
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers,
      body: ''
    };
  }

  const path = event.path.replace('/.netlify/functions/ally-management', '');

  try {
    let response;

    if (event.httpMethod === 'GET') {
      if (path === '' || path === '/') {
        response = await getUserAllies(event);
      } else if (path === '/statistics') {
        response = await getAllyStatistics(event);
      } else if (path === '/search') {
        response = await searchAllies(event);
      } else if (path === '/recommendations') {
        response = await getAllyRecommendations(event);
      } else {
        response = await getAllyDetails(event);
      }
    } else if (event.httpMethod === 'PUT') {
      if (path.includes('/block')) {
        response = await blockUser(event);
      } else {
        response = await updateAllyConnection(event);
      }
    } else if (event.httpMethod === 'DELETE') {
      response = await removeAllyConnection(event);
    } else {
      response = {
        statusCode: 405,
        body: JSON.stringify({ error: 'Method not allowed' })
      };
    }

    // Add CORS headers to response
    response.headers = { ...response.headers, ...headers };
    return response;

  } catch (error) {
    console.error('Ally Management API error:', error);
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ error: 'Internal server error' })
    };
  }
};
