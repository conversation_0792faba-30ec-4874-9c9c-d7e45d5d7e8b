/**
 * Technology Industry Agreement Templates
 * 
 * Comprehensive agreement templates for technology industry collaborations:
 * - Software Development
 * - SaaS Platforms
 * - API Partnerships
 * - Tech Consulting
 * - Mobile App Development
 * - DevOps & Infrastructure
 * - Data Science & AI
 */

// ============================================================================
// SOFTWARE DEVELOPMENT AGREEMENT TEMPLATE
// ============================================================================

export const SOFTWARE_DEVELOPMENT_TEMPLATE = {
  name: 'Software Development Collaboration Agreement',
  industry: 'technology',
  collaboration_type: 'software_development',
  template_type: 'detailed',
  
  template_content: `
# {{COMPANY_NAME}} SOFTWARE DEVELOPMENT AGREEMENT

**EFFECTIVE DATE:** {{EFFECTIVE_DATE}}

This Software Development Agreement ("Agreement") is entered into on {{CURRENT_DATE}}, by and between {{COMPANY_LEGAL_NAME}}, a {{COMPANY_STATE}} corporation with its principal place of business at {{COMPANY_ADDRESS}} (the "Company"), and {{CONTRIBUTOR_NAME}}, an individual with an address at {{CONTRIBUTOR_ADDRESS}} (the "Developer").

## RECITALS

WHEREAS, the Company is developing {{PROJECT_NAME}}, {{PROJECT_DESCRIPTION}} (the "Software");

WHEREAS, the Developer possesses specialized software development skills and expertise;

WHEREAS, the Company desires to engage the Developer to provide software development services; and

WHEREAS, the Developer is willing to provide such services subject to the terms herein;

NOW, THEREFORE, the parties agree as follows:

## 1. DEVELOPMENT SERVICES

The Developer shall provide software development services as specified in **Schedule A**, including but not limited to:
- Software architecture and design
- Code development and implementation
- Testing and quality assurance
- Documentation and technical specifications
- Code review and optimization
- Bug fixes and maintenance

## 2. COMPENSATION AND REVENUE SHARING

The Developer shall receive compensation as detailed in **Schedule B**, including:
- Revenue sharing: {{REVENUE_SHARE_PERCENTAGE}}% of net software revenue
- Minimum payout threshold: {{MINIMUM_PAYOUT}}
- Payment frequency: {{PAYMENT_FREQUENCY}}
- Performance bonuses as specified

## 3. INTELLECTUAL PROPERTY RIGHTS

### 3.1 Work Product Ownership
All software code, algorithms, documentation, and related materials created by the Developer specifically for the Software ("Work Product") shall be owned by the Company as works made for hire under U.S. copyright law.

### 3.2 Pre-Existing IP
The Developer retains ownership of any pre-existing intellectual property, development tools, frameworks, or general programming knowledge. The Company receives a perpetual, royalty-free license to use such pre-existing IP solely in connection with the Software.

### 3.3 Open Source Components
The Developer shall:
- Disclose all open source components used in the Work Product
- Ensure compliance with all applicable open source licenses
- Obtain Company approval before incorporating GPL or copyleft licensed code
- Maintain a software bill of materials (SBOM) for all dependencies

### 3.4 Third-Party Components
Any third-party software, libraries, or APIs integrated into the Software must be approved by the Company and properly licensed for commercial use.

## 4. TECHNICAL SPECIFICATIONS

The Software shall be developed according to the technical specifications in **Exhibit I**, including:
- Programming languages and frameworks
- Architecture and design patterns
- Performance requirements
- Security standards
- Scalability requirements
- Integration specifications

## 5. DEVELOPMENT PROCESS

### 5.1 Methodology
Development shall follow agile/scrum methodology with:
- Sprint planning and retrospectives
- Daily standups and progress updates
- Code reviews and pair programming
- Continuous integration and deployment
- Automated testing and quality gates

### 5.2 Version Control
All code shall be managed through Git version control with:
- Feature branch workflow
- Pull request reviews
- Commit message standards
- Release tagging and versioning

### 5.3 Quality Standards
All code must meet the following standards:
- Pass automated unit and integration tests
- Achieve minimum 80% code coverage
- Pass security vulnerability scans
- Follow established coding standards and style guides
- Include comprehensive documentation

## 6. CONFIDENTIALITY AND NON-DISCLOSURE

The Developer acknowledges access to confidential information including:
- Source code and algorithms
- Business logic and processes
- Customer data and analytics
- Technical architecture and designs
- Product roadmaps and strategies

The Developer agrees to maintain strict confidentiality and not disclose such information to any third party.

## 7. DATA PROTECTION AND SECURITY

### 7.1 Data Handling
The Developer shall:
- Follow all data protection policies and procedures
- Implement appropriate security measures for data access
- Report any security incidents immediately
- Comply with GDPR, CCPA, and other applicable privacy laws

### 7.2 Security Requirements
- Use secure coding practices
- Implement proper authentication and authorization
- Follow OWASP security guidelines
- Conduct regular security testing

## 8. PERFORMANCE METRICS

Developer performance shall be measured by:
- Code quality and review scores
- Sprint velocity and story completion
- Bug rates and resolution time
- Customer satisfaction metrics
- Technical debt reduction

## 9. TERMINATION

Either party may terminate this Agreement with thirty (30) days written notice. Upon termination:
- All Work Product remains with the Company
- Developer shall return all confidential information
- Outstanding compensation shall be calculated and paid
- Non-disclosure obligations continue indefinitely

## 10. GOVERNING LAW

This Agreement shall be governed by the laws of {{GOVERNING_LAW_STATE}} and any disputes shall be resolved in {{JURISDICTION}}.

## SIGNATURES

**COMPANY:**
{{COMPANY_LEGAL_NAME}}

By: ______________________
Name: {{COMPANY_SIGNER_NAME}}
Title: {{COMPANY_SIGNER_TITLE}}
Date: _______________

**DEVELOPER:**
______________________
{{CONTRIBUTOR_NAME}}
Date: _______________

---

## SCHEDULE A - DEVELOPMENT SERVICES

### Project Scope
{{PROJECT_NAME}} is a {{PROJECT_TYPE}} application that {{PROJECT_DESCRIPTION}}.

### Technical Requirements
- **Frontend:** {{FRONTEND_TECH}}
- **Backend:** {{BACKEND_TECH}}
- **Database:** {{DATABASE_TECH}}
- **Deployment:** {{DEPLOYMENT_TECH}}
- **APIs:** {{API_TECH}}

### Development Responsibilities
The Developer shall be responsible for:
1. **Architecture Design**
   - System architecture planning
   - Database schema design
   - API design and documentation
   - Security architecture

2. **Implementation**
   - Feature development per specifications
   - User interface implementation
   - Backend service development
   - Database integration

3. **Testing**
   - Unit test development
   - Integration testing
   - Performance testing
   - Security testing

4. **Documentation**
   - Code documentation
   - API documentation
   - Deployment guides
   - User documentation

### Deliverables
- Functional software components
- Source code with documentation
- Test suites and coverage reports
- Deployment scripts and configurations
- Technical documentation

### Timeline
Development shall proceed according to the project roadmap in **Exhibit II**.

---

## SCHEDULE B - COMPENSATION STRUCTURE

### Revenue Sharing Model
The Developer shall receive {{REVENUE_SHARE_PERCENTAGE}}% of net software revenue, calculated as follows:

**Gross Revenue Sources:**
- Software license fees
- Subscription revenue
- Professional services revenue
- API usage fees
- Third-party integration revenue

**Allowable Deductions:**
- Platform fees (app stores, payment processors)
- Hosting and infrastructure costs
- Third-party license fees
- Customer support costs (up to 10% of gross revenue)

### Payment Terms
- **Minimum Payout:** {{MINIMUM_PAYOUT}}
- **Payment Frequency:** {{PAYMENT_FREQUENCY}}
- **Payment Method:** ACH transfer or wire transfer
- **Reporting:** Detailed revenue reports provided with each payment
- **Payment Timing:** Within 45 days of period end

### Performance Bonuses
Additional compensation may be earned through:
- **Quality Bonus:** 5% bonus for zero critical bugs in production
- **Performance Bonus:** 3% bonus for meeting performance benchmarks
- **Innovation Bonus:** 10% bonus for significant technical innovations
- **Timeline Bonus:** 5% bonus for early delivery of major milestones

### Equity Participation (Optional)
If applicable, equity participation terms are detailed in a separate equity agreement.

---

## EXHIBIT I - TECHNICAL SPECIFICATIONS

### {{PROJECT_NAME}} Technical Architecture

**System Overview:**
{{PROJECT_DESCRIPTION}}

**Technology Stack:**
- **Frontend Framework:** {{FRONTEND_TECH}}
- **Backend Framework:** {{BACKEND_TECH}}
- **Database:** {{DATABASE_TECH}}
- **Cloud Platform:** {{DEPLOYMENT_TECH}}
- **API Technology:** {{API_TECH}}

**Performance Requirements:**
- Response time: < 200ms for API calls
- Uptime: 99.9% availability
- Scalability: Support 10,000+ concurrent users
- Security: SOC 2 Type II compliance

**Integration Requirements:**
- Third-party API integrations
- Payment gateway integration
- Authentication providers
- Analytics and monitoring tools

**Development Standards:**
- Code style: ESLint/Prettier configuration
- Testing: Jest for unit tests, Cypress for E2E
- Documentation: JSDoc for code, OpenAPI for APIs
- Version control: Git with conventional commits

---

## EXHIBIT II - DEVELOPMENT ROADMAP

### {{PROJECT_NAME}} Development Timeline

**Phase 1: Foundation (Months 1-2)**
- Project setup and configuration
- Core architecture implementation
- Basic user authentication
- Database schema and migrations
- CI/CD pipeline setup

**Phase 2: Core Features (Months 3-4)**
- Primary feature development
- API implementation
- Frontend user interface
- Basic testing suite
- Security implementation

**Phase 3: Advanced Features (Months 5-6)**
- Advanced functionality
- Third-party integrations
- Performance optimization
- Comprehensive testing
- Documentation completion

**Phase 4: Launch Preparation (Month 7)**
- Production deployment
- Security auditing
- Performance testing
- User acceptance testing
- Launch preparation

**Key Milestones:**
- **Month 2:** MVP Demo
- **Month 4:** Beta Release
- **Month 6:** Feature Complete
- **Month 7:** Production Launch

**Success Criteria:**
- All features implemented per specifications
- Performance benchmarks met
- Security requirements satisfied
- User acceptance criteria achieved
  `,
  
  variables: {
    FRONTEND_TECH: 'React with TypeScript',
    BACKEND_TECH: 'Node.js with Express',
    DATABASE_TECH: 'PostgreSQL with Redis',
    DEPLOYMENT_TECH: 'AWS with Docker',
    API_TECH: 'RESTful APIs with GraphQL'
  }
};

// ============================================================================
// SAAS PLATFORM AGREEMENT TEMPLATE
// ============================================================================

export const SAAS_PLATFORM_TEMPLATE = {
  name: 'SaaS Platform Development Agreement',
  industry: 'technology',
  collaboration_type: 'saas_development',
  template_type: 'detailed',
  
  template_content: `
# {{COMPANY_NAME}} SAAS PLATFORM DEVELOPMENT AGREEMENT

This SaaS Platform Development Agreement addresses the unique requirements of software-as-a-service platforms, including subscription revenue models, multi-tenancy, and ongoing maintenance responsibilities.

[Content continues with SaaS-specific terms...]
  `,
  
  customizations: {
    revenue_model: 'subscription_based',
    ip_ownership: 'company_owned_with_contributor_attribution',
    maintenance_requirements: 'ongoing_support_included'
  }
};

// ============================================================================
// API PARTNERSHIP AGREEMENT TEMPLATE
// ============================================================================

export const API_PARTNERSHIP_TEMPLATE = {
  name: 'API Partnership Agreement',
  industry: 'technology',
  collaboration_type: 'api_partnership',
  template_type: 'standard',
  
  template_content: `
# {{COMPANY_NAME}} API PARTNERSHIP AGREEMENT

This API Partnership Agreement governs the development, integration, and revenue sharing for API-based collaborations.

[Content continues with API-specific terms...]
  `,
  
  customizations: {
    revenue_model: 'usage_based',
    ip_ownership: 'shared_with_licensing',
    performance_requirements: 'sla_based'
  }
};

// ============================================================================
// TECH CONSULTING AGREEMENT TEMPLATE
// ============================================================================

export const TECH_CONSULTING_TEMPLATE = {
  name: 'Technology Consulting Agreement',
  industry: 'technology',
  collaboration_type: 'tech_consulting',
  template_type: 'standard',
  
  template_content: `
# {{COMPANY_NAME}} TECHNOLOGY CONSULTING AGREEMENT

This Technology Consulting Agreement covers advisory services, technical consulting, and knowledge transfer arrangements.

[Content continues with consulting-specific terms...]
  `,
  
  customizations: {
    revenue_model: 'hourly_plus_success_fee',
    ip_ownership: 'client_owned_deliverables',
    knowledge_retention: 'consultant_retains_methodologies'
  }
};

// ============================================================================
// TEMPLATE REGISTRY
// ============================================================================

export const TECH_INDUSTRY_TEMPLATES = {
  software_development: SOFTWARE_DEVELOPMENT_TEMPLATE,
  saas_development: SAAS_PLATFORM_TEMPLATE,
  api_partnership: API_PARTNERSHIP_TEMPLATE,
  tech_consulting: TECH_CONSULTING_TEMPLATE
};

// ============================================================================
// TEMPLATE SELECTION HELPER
// ============================================================================

export const getTechTemplate = (collaborationType) => {
  const template = TECH_INDUSTRY_TEMPLATES[collaborationType];
  if (!template) {
    throw new Error(`No tech template found for collaboration type: ${collaborationType}`);
  }
  return template;
};

export const getAvailableTechTemplates = () => {
  return Object.keys(TECH_INDUSTRY_TEMPLATES).map(key => ({
    key,
    name: TECH_INDUSTRY_TEMPLATES[key].name,
    type: TECH_INDUSTRY_TEMPLATES[key].template_type
  }));
};
