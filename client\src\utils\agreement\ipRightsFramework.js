/**
 * Intellectual Property Rights Framework
 * 
 * Comprehensive system for managing intellectual property rights in collaborations:
 * - IP ownership models (work-for-hire, retained rights, co-ownership, licensing)
 * - Rights management and tracking
 * - Licensing frameworks and terms
 * - Attribution and moral rights
 * - IP registration and protection
 */

// ============================================================================
// IP OWNERSHIP MODELS
// ============================================================================

export const IP_OWNERSHIP_MODELS = {
  WORK_FOR_HIRE: 'work_for_hire',
  RETAINED_RIGHTS: 'retained_rights',
  CO_OWNERSHIP: 'co_ownership',
  LICENSING: 'licensing',
  ASSIGNMENT: 'assignment',
  JOINT_OWNERSHIP: 'joint_ownership',
  EXCLUSIVE_LICENSE: 'exclusive_license',
  NON_EXCLUSIVE_LICENSE: 'non_exclusive_license'
};

export const IP_TYPES = {
  COPYRIGHT: 'copyright',
  TRADEMARK: 'trademark',
  PATENT: 'patent',
  TRADE_SECRET: 'trade_secret',
  KNOW_HOW: 'know_how',
  DESIGN_RIGHTS: 'design_rights',
  MORAL_RIGHTS: 'moral_rights',
  PUBLICITY_RIGHTS: 'publicity_rights'
};

export const USAGE_RIGHTS = {
  REPRODUCE: 'reproduce',
  DISTRIBUTE: 'distribute',
  DISPLAY: 'display',
  PERFORM: 'perform',
  CREATE_DERIVATIVES: 'create_derivatives',
  MODIFY: 'modify',
  SUBLICENSE: 'sublicense',
  COMMERCIAL_USE: 'commercial_use',
  NON_COMMERCIAL_USE: 'non_commercial_use'
};

export const TERRITORIAL_SCOPE = {
  WORLDWIDE: 'worldwide',
  COUNTRY_SPECIFIC: 'country_specific',
  REGIONAL: 'regional',
  JURISDICTION_SPECIFIC: 'jurisdiction_specific'
};

// ============================================================================
// IP RIGHTS FRAMEWORK CLASS
// ============================================================================

export class IPRightsFramework {
  constructor() {
    this.ipAssets = new Map();
    this.ownershipRecords = new Map();
    this.licenseAgreements = new Map();
    this.rightsRegistry = new Map();
    this.attributionRequirements = new Map();
  }

  /**
   * Define IP asset and ownership structure
   */
  defineIPAsset(assetDefinition) {
    const ipAsset = {
      id: assetDefinition.id || this.generateAssetId(),
      
      // Basic asset information
      title: assetDefinition.title,
      description: assetDefinition.description,
      type: assetDefinition.type, // copyright, trademark, patent, etc.
      category: assetDefinition.category, // software, music, artwork, etc.
      
      // Creation details
      createdBy: assetDefinition.createdBy,
      creationDate: assetDefinition.creationDate || new Date().toISOString(),
      collaborators: assetDefinition.collaborators || [],
      
      // Ownership structure
      ownershipModel: assetDefinition.ownershipModel,
      ownershipDetails: this.processOwnershipDetails(assetDefinition.ownershipModel, assetDefinition.ownershipDetails),
      
      // Rights and permissions
      rights: assetDefinition.rights || [],
      restrictions: assetDefinition.restrictions || [],
      
      // Registration information
      registrationStatus: assetDefinition.registrationStatus || 'unregistered',
      registrationNumber: assetDefinition.registrationNumber,
      registrationDate: assetDefinition.registrationDate,
      registrationJurisdiction: assetDefinition.registrationJurisdiction,
      
      // Attribution requirements
      attributionRequired: assetDefinition.attributionRequired !== false,
      attributionFormat: assetDefinition.attributionFormat,
      moralRights: assetDefinition.moralRights || {},
      
      // Commercial terms
      commercialUseAllowed: assetDefinition.commercialUseAllowed !== false,
      exclusivityLevel: assetDefinition.exclusivityLevel || 'non_exclusive',
      territorialScope: assetDefinition.territorialScope || TERRITORIAL_SCOPE.WORLDWIDE,
      
      // Metadata
      industry: assetDefinition.industry,
      tags: assetDefinition.tags || [],
      isActive: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    this.ipAssets.set(ipAsset.id, ipAsset);
    this.createOwnershipRecord(ipAsset);
    
    return ipAsset;
  }

  /**
   * Process ownership details based on ownership model
   */
  processOwnershipDetails(ownershipModel, ownershipDetails) {
    switch (ownershipModel) {
      case IP_OWNERSHIP_MODELS.WORK_FOR_HIRE:
        return this.processWorkForHireOwnership(ownershipDetails);
      
      case IP_OWNERSHIP_MODELS.RETAINED_RIGHTS:
        return this.processRetainedRightsOwnership(ownershipDetails);
      
      case IP_OWNERSHIP_MODELS.CO_OWNERSHIP:
        return this.processCoOwnership(ownershipDetails);
      
      case IP_OWNERSHIP_MODELS.LICENSING:
        return this.processLicensingOwnership(ownershipDetails);
      
      case IP_OWNERSHIP_MODELS.ASSIGNMENT:
        return this.processAssignmentOwnership(ownershipDetails);
      
      default:
        return ownershipDetails;
    }
  }

  /**
   * Process work-for-hire ownership structure
   */
  processWorkForHireOwnership(details) {
    return {
      model: IP_OWNERSHIP_MODELS.WORK_FOR_HIRE,
      employer: details.employer,
      employee: details.employee,
      scopeOfWork: details.scopeOfWork,
      
      // Rights allocation
      employerRights: [
        USAGE_RIGHTS.REPRODUCE,
        USAGE_RIGHTS.DISTRIBUTE,
        USAGE_RIGHTS.DISPLAY,
        USAGE_RIGHTS.PERFORM,
        USAGE_RIGHTS.CREATE_DERIVATIVES,
        USAGE_RIGHTS.MODIFY,
        USAGE_RIGHTS.COMMERCIAL_USE
      ],
      
      employeeRights: details.employeeRights || [
        // Employee typically retains limited rights
      ],
      
      // Attribution
      attributionToEmployee: details.attributionToEmployee !== false,
      portfolioRights: details.portfolioRights !== false,
      
      // Exceptions
      preExistingIP: details.preExistingIP || [],
      inventionsPolicy: details.inventionsPolicy,
      
      // Terms
      effectiveDate: details.effectiveDate,
      jurisdiction: details.jurisdiction
    };
  }

  /**
   * Process retained rights ownership structure
   */
  processRetainedRightsOwnership(details) {
    return {
      model: IP_OWNERSHIP_MODELS.RETAINED_RIGHTS,
      originalOwner: details.originalOwner,
      licensee: details.licensee,
      
      // Rights retained by original owner
      retainedRights: details.retainedRights || [
        USAGE_RIGHTS.REPRODUCE,
        USAGE_RIGHTS.DISTRIBUTE,
        USAGE_RIGHTS.CREATE_DERIVATIVES
      ],
      
      // Rights granted to licensee
      grantedRights: details.grantedRights || [
        USAGE_RIGHTS.COMMERCIAL_USE
      ],
      
      // License terms
      licenseScope: details.licenseScope,
      exclusivity: details.exclusivity || 'non_exclusive',
      territory: details.territory || TERRITORIAL_SCOPE.WORLDWIDE,
      duration: details.duration,
      
      // Reversion rights
      reversionRights: details.reversionRights || {},
      terminationConditions: details.terminationConditions || [],
      
      // Financial terms
      royaltyRate: details.royaltyRate,
      minimumGuarantee: details.minimumGuarantee,
      advancePayment: details.advancePayment
    };
  }

  /**
   * Process co-ownership structure
   */
  processCoOwnership(details) {
    return {
      model: IP_OWNERSHIP_MODELS.CO_OWNERSHIP,
      coOwners: details.coOwners || [],
      
      // Ownership percentages
      ownershipShares: details.ownershipShares || {},
      
      // Decision making
      decisionMaking: details.decisionMaking || 'unanimous_consent',
      votingRights: details.votingRights || {},
      
      // Usage rights
      independentUseRights: details.independentUseRights !== false,
      licensingRights: details.licensingRights || 'joint_approval_required',
      
      // Revenue sharing
      revenueSharing: details.revenueSharing || 'proportional_to_ownership',
      expenseSharing: details.expenseSharing || 'proportional_to_ownership',
      
      // Transfer restrictions
      transferRestrictions: details.transferRestrictions || {},
      rightOfFirstRefusal: details.rightOfFirstRefusal !== false,
      
      // Dispute resolution
      disputeResolution: details.disputeResolution || 'arbitration',
      
      // Termination
      partitionRights: details.partitionRights || {},
      buyoutProvisions: details.buyoutProvisions || {}
    };
  }

  /**
   * Process licensing ownership structure
   */
  processLicensingOwnership(details) {
    return {
      model: IP_OWNERSHIP_MODELS.LICENSING,
      licensor: details.licensor,
      licensee: details.licensee,
      
      // License grant
      grantedRights: details.grantedRights || [],
      reservedRights: details.reservedRights || [],
      
      // Scope and limitations
      fieldOfUse: details.fieldOfUse,
      territory: details.territory || TERRITORIAL_SCOPE.WORLDWIDE,
      exclusivity: details.exclusivity || 'non_exclusive',
      
      // Duration and termination
      duration: details.duration,
      renewalOptions: details.renewalOptions || [],
      terminationConditions: details.terminationConditions || [],
      
      // Financial terms
      licenseType: details.licenseType, // royalty, flat_fee, revenue_share
      royaltyRate: details.royaltyRate,
      minimumRoyalty: details.minimumRoyalty,
      advancePayment: details.advancePayment,
      
      // Performance requirements
      performanceStandards: details.performanceStandards || [],
      minimumSalesRequirements: details.minimumSalesRequirements,
      
      // Quality control
      qualityStandards: details.qualityStandards || [],
      approvalRights: details.approvalRights || {},
      
      // Sublicensing
      sublicensingRights: details.sublicensingRights || 'prohibited',
      sublicenseTerms: details.sublicenseTerms || {}
    };
  }

  /**
   * Process assignment ownership structure
   */
  processAssignmentOwnership(details) {
    return {
      model: IP_OWNERSHIP_MODELS.ASSIGNMENT,
      assignor: details.assignor,
      assignee: details.assignee,
      
      // Assignment scope
      assignedRights: details.assignedRights || 'all_rights',
      retainedRights: details.retainedRights || [],
      
      // Consideration
      consideration: details.consideration,
      paymentTerms: details.paymentTerms,
      
      // Warranties and representations
      warranties: details.warranties || [],
      indemnification: details.indemnification || {},
      
      // Moral rights
      moralRightsWaiver: details.moralRightsWaiver || false,
      attributionRights: details.attributionRights || {},
      
      // Effective date and recording
      effectiveDate: details.effectiveDate,
      recordingRequirements: details.recordingRequirements || {}
    };
  }

  /**
   * Create ownership record for tracking
   */
  createOwnershipRecord(ipAsset) {
    const ownershipRecord = {
      id: this.generateOwnershipRecordId(),
      assetId: ipAsset.id,
      assetTitle: ipAsset.title,
      
      // Current ownership
      currentOwners: this.extractCurrentOwners(ipAsset.ownershipDetails),
      ownershipModel: ipAsset.ownershipModel,
      
      // Ownership history
      ownershipHistory: [{
        date: ipAsset.creationDate,
        event: 'creation',
        details: ipAsset.ownershipDetails,
        recordedBy: ipAsset.createdBy
      }],
      
      // Rights summary
      rightsSummary: this.generateRightsSummary(ipAsset),
      
      // Status
      status: 'active',
      lastUpdated: new Date().toISOString()
    };

    this.ownershipRecords.set(ownershipRecord.id, ownershipRecord);
    return ownershipRecord;
  }

  /**
   * Create license agreement
   */
  createLicenseAgreement(licenseDetails) {
    const licenseAgreement = {
      id: licenseDetails.id || this.generateLicenseId(),
      
      // Parties
      licensor: licenseDetails.licensor,
      licensee: licenseDetails.licensee,
      
      // Licensed IP
      licensedAssets: licenseDetails.licensedAssets || [],
      
      // Grant details
      grantedRights: licenseDetails.grantedRights || [],
      exclusivity: licenseDetails.exclusivity || 'non_exclusive',
      territory: licenseDetails.territory || TERRITORIAL_SCOPE.WORLDWIDE,
      fieldOfUse: licenseDetails.fieldOfUse,
      
      // Duration
      effectiveDate: licenseDetails.effectiveDate || new Date().toISOString(),
      expirationDate: licenseDetails.expirationDate,
      renewalOptions: licenseDetails.renewalOptions || [],
      
      // Financial terms
      licenseType: licenseDetails.licenseType, // royalty, flat_fee, revenue_share
      financialTerms: licenseDetails.financialTerms || {},
      
      // Performance requirements
      performanceStandards: licenseDetails.performanceStandards || [],
      reportingRequirements: licenseDetails.reportingRequirements || [],
      
      // Restrictions and limitations
      restrictions: licenseDetails.restrictions || [],
      qualityStandards: licenseDetails.qualityStandards || [],
      
      // Termination
      terminationConditions: licenseDetails.terminationConditions || [],
      postTerminationObligations: licenseDetails.postTerminationObligations || [],
      
      // Legal terms
      governingLaw: licenseDetails.governingLaw,
      disputeResolution: licenseDetails.disputeResolution || 'arbitration',
      
      // Status
      status: 'active',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    this.licenseAgreements.set(licenseAgreement.id, licenseAgreement);
    
    // Update ownership records
    this.updateOwnershipForLicense(licenseAgreement);
    
    return licenseAgreement;
  }

  /**
   * Transfer IP rights
   */
  transferIPRights(transferDetails) {
    const transfer = {
      id: this.generateTransferId(),
      
      // Transfer details
      assetId: transferDetails.assetId,
      transferor: transferDetails.transferor,
      transferee: transferDetails.transferee,
      transferType: transferDetails.transferType, // assignment, license, etc.
      
      // Rights being transferred
      transferredRights: transferDetails.transferredRights || [],
      retainedRights: transferDetails.retainedRights || [],
      
      // Terms
      consideration: transferDetails.consideration,
      effectiveDate: transferDetails.effectiveDate || new Date().toISOString(),
      
      // Documentation
      transferDocument: transferDetails.transferDocument,
      recordingRequirements: transferDetails.recordingRequirements || {},
      
      // Status
      status: 'pending',
      createdAt: new Date().toISOString()
    };

    // Update ownership records
    this.updateOwnershipForTransfer(transfer);
    
    return transfer;
  }

  /**
   * Check IP ownership and rights
   */
  checkIPRights(assetId, requestedRights, requestingParty) {
    const asset = this.ipAssets.get(assetId);
    if (!asset) {
      return {
        authorized: false,
        reason: 'Asset not found'
      };
    }

    const ownershipRecord = this.getOwnershipRecord(assetId);
    if (!ownershipRecord) {
      return {
        authorized: false,
        reason: 'Ownership record not found'
      };
    }

    // Check if requesting party has the requested rights
    const partyRights = this.getPartyRights(assetId, requestingParty);
    const hasAllRights = requestedRights.every(right => partyRights.includes(right));

    return {
      authorized: hasAllRights,
      grantedRights: partyRights,
      requestedRights,
      missingRights: requestedRights.filter(right => !partyRights.includes(right)),
      ownershipModel: asset.ownershipModel,
      restrictions: this.getApplicableRestrictions(assetId, requestingParty)
    };
  }

  /**
   * Generate attribution text
   */
  generateAttributionText(assetId, usageContext = {}) {
    const asset = this.ipAssets.get(assetId);
    if (!asset || !asset.attributionRequired) {
      return null;
    }

    const attributionFormat = asset.attributionFormat || this.getDefaultAttributionFormat(asset.type);
    
    // Replace placeholders in attribution format
    let attributionText = attributionFormat;
    
    attributionText = attributionText.replace('{title}', asset.title);
    attributionText = attributionText.replace('{creator}', this.getCreatorNames(asset));
    attributionText = attributionText.replace('{year}', new Date(asset.creationDate).getFullYear());
    attributionText = attributionText.replace('{license}', this.getLicenseInfo(assetId));
    
    return attributionText;
  }

  /**
   * Validate IP compliance
   */
  validateIPCompliance(assetId, usageDetails) {
    const asset = this.ipAssets.get(assetId);
    if (!asset) {
      return {
        compliant: false,
        violations: ['Asset not found']
      };
    }

    const violations = [];
    const warnings = [];

    // Check attribution requirements
    if (asset.attributionRequired && !usageDetails.attributionProvided) {
      violations.push('Attribution required but not provided');
    }

    // Check commercial use restrictions
    if (!asset.commercialUseAllowed && usageDetails.commercialUse) {
      violations.push('Commercial use not permitted');
    }

    // Check territorial restrictions
    if (asset.territorialScope !== TERRITORIAL_SCOPE.WORLDWIDE) {
      const allowedTerritories = this.getAllowedTerritories(assetId);
      if (!allowedTerritories.includes(usageDetails.territory)) {
        violations.push(`Usage not permitted in territory: ${usageDetails.territory}`);
      }
    }

    // Check license compliance
    const licenseCompliance = this.checkLicenseCompliance(assetId, usageDetails);
    violations.push(...licenseCompliance.violations);
    warnings.push(...licenseCompliance.warnings);

    return {
      compliant: violations.length === 0,
      violations,
      warnings,
      recommendations: this.generateComplianceRecommendations(violations, warnings)
    };
  }

  // ============================================================================
  // HELPER METHODS
  // ============================================================================

  /**
   * Extract current owners from ownership details
   */
  extractCurrentOwners(ownershipDetails) {
    switch (ownershipDetails.model) {
      case IP_OWNERSHIP_MODELS.WORK_FOR_HIRE:
        return [ownershipDetails.employer];
      
      case IP_OWNERSHIP_MODELS.RETAINED_RIGHTS:
        return [ownershipDetails.originalOwner];
      
      case IP_OWNERSHIP_MODELS.CO_OWNERSHIP:
        return ownershipDetails.coOwners || [];
      
      case IP_OWNERSHIP_MODELS.LICENSING:
        return [ownershipDetails.licensor];
      
      case IP_OWNERSHIP_MODELS.ASSIGNMENT:
        return [ownershipDetails.assignee];
      
      default:
        return [];
    }
  }

  /**
   * Generate rights summary
   */
  generateRightsSummary(ipAsset) {
    const summary = {
      ownershipModel: ipAsset.ownershipModel,
      primaryRights: [],
      restrictions: ipAsset.restrictions || [],
      attributionRequired: ipAsset.attributionRequired,
      commercialUseAllowed: ipAsset.commercialUseAllowed
    };

    // Extract primary rights based on ownership model
    switch (ipAsset.ownershipModel) {
      case IP_OWNERSHIP_MODELS.WORK_FOR_HIRE:
        summary.primaryRights = ipAsset.ownershipDetails.employerRights || [];
        break;
      
      case IP_OWNERSHIP_MODELS.RETAINED_RIGHTS:
        summary.primaryRights = ipAsset.ownershipDetails.retainedRights || [];
        break;
      
      case IP_OWNERSHIP_MODELS.LICENSING:
        summary.primaryRights = ipAsset.ownershipDetails.grantedRights || [];
        break;
      
      default:
        summary.primaryRights = ipAsset.rights || [];
    }

    return summary;
  }

  /**
   * Update ownership for license
   */
  updateOwnershipForLicense(licenseAgreement) {
    licenseAgreement.licensedAssets.forEach(assetId => {
      const ownershipRecord = this.getOwnershipRecord(assetId);
      if (ownershipRecord) {
        ownershipRecord.ownershipHistory.push({
          date: licenseAgreement.effectiveDate,
          event: 'license_granted',
          details: {
            licensee: licenseAgreement.licensee,
            grantedRights: licenseAgreement.grantedRights,
            exclusivity: licenseAgreement.exclusivity
          },
          licenseId: licenseAgreement.id
        });
        ownershipRecord.lastUpdated = new Date().toISOString();
      }
    });
  }

  /**
   * Update ownership for transfer
   */
  updateOwnershipForTransfer(transfer) {
    const ownershipRecord = this.getOwnershipRecord(transfer.assetId);
    if (ownershipRecord) {
      ownershipRecord.ownershipHistory.push({
        date: transfer.effectiveDate,
        event: 'rights_transferred',
        details: {
          transferor: transfer.transferor,
          transferee: transfer.transferee,
          transferredRights: transfer.transferredRights,
          transferType: transfer.transferType
        },
        transferId: transfer.id
      });
      
      // Update current owners if it's a full assignment
      if (transfer.transferType === 'assignment') {
        ownershipRecord.currentOwners = [transfer.transferee];
      }
      
      ownershipRecord.lastUpdated = new Date().toISOString();
    }
  }

  /**
   * Get ownership record for asset
   */
  getOwnershipRecord(assetId) {
    return Array.from(this.ownershipRecords.values()).find(record => record.assetId === assetId);
  }

  /**
   * Get party rights for asset
   */
  getPartyRights(assetId, party) {
    const asset = this.ipAssets.get(assetId);
    if (!asset) return [];

    const ownershipDetails = asset.ownershipDetails;
    
    // Check if party is an owner
    const currentOwners = this.extractCurrentOwners(ownershipDetails);
    if (currentOwners.includes(party)) {
      return this.getOwnerRights(asset);
    }

    // Check license agreements
    const licenseRights = this.getLicenseRights(assetId, party);
    
    return licenseRights;
  }

  /**
   * Get owner rights
   */
  getOwnerRights(asset) {
    // Owners typically have all rights unless specifically restricted
    return [
      USAGE_RIGHTS.REPRODUCE,
      USAGE_RIGHTS.DISTRIBUTE,
      USAGE_RIGHTS.DISPLAY,
      USAGE_RIGHTS.PERFORM,
      USAGE_RIGHTS.CREATE_DERIVATIVES,
      USAGE_RIGHTS.MODIFY,
      USAGE_RIGHTS.SUBLICENSE,
      USAGE_RIGHTS.COMMERCIAL_USE
    ];
  }

  /**
   * Get license rights for party
   */
  getLicenseRights(assetId, party) {
    const relevantLicenses = Array.from(this.licenseAgreements.values()).filter(license =>
      license.licensedAssets.includes(assetId) && 
      license.licensee === party &&
      license.status === 'active'
    );

    const allRights = [];
    relevantLicenses.forEach(license => {
      allRights.push(...license.grantedRights);
    });

    return [...new Set(allRights)]; // Remove duplicates
  }

  /**
   * Get applicable restrictions
   */
  getApplicableRestrictions(assetId, party) {
    const asset = this.ipAssets.get(assetId);
    if (!asset) return [];

    // Get general restrictions
    const restrictions = [...asset.restrictions];

    // Add license-specific restrictions
    const licenseRestrictions = this.getLicenseRestrictions(assetId, party);
    restrictions.push(...licenseRestrictions);

    return restrictions;
  }

  /**
   * Get license restrictions
   */
  getLicenseRestrictions(assetId, party) {
    const relevantLicenses = Array.from(this.licenseAgreements.values()).filter(license =>
      license.licensedAssets.includes(assetId) && 
      license.licensee === party &&
      license.status === 'active'
    );

    const allRestrictions = [];
    relevantLicenses.forEach(license => {
      allRestrictions.push(...license.restrictions);
    });

    return allRestrictions;
  }

  /**
   * Get default attribution format
   */
  getDefaultAttributionFormat(ipType) {
    const formats = {
      [IP_TYPES.COPYRIGHT]: '{title} © {year} {creator}',
      [IP_TYPES.TRADEMARK]: '{title}™ {creator}',
      [IP_TYPES.PATENT]: '{title} - Patent by {creator} ({year})',
      default: '{title} by {creator} ({year})'
    };

    return formats[ipType] || formats.default;
  }

  /**
   * Get creator names
   */
  getCreatorNames(asset) {
    if (asset.collaborators && asset.collaborators.length > 0) {
      return asset.collaborators.map(c => c.name).join(', ');
    }
    return asset.createdBy || 'Unknown';
  }

  /**
   * Get license info
   */
  getLicenseInfo(assetId) {
    // This would return license information for the asset
    return 'All rights reserved';
  }

  /**
   * Get allowed territories
   */
  getAllowedTerritories(assetId) {
    const asset = this.ipAssets.get(assetId);
    if (!asset) return [];

    // Extract territories from ownership details and licenses
    return ['US', 'CA', 'EU']; // Simplified
  }

  /**
   * Check license compliance
   */
  checkLicenseCompliance(assetId, usageDetails) {
    return {
      violations: [],
      warnings: []
    };
  }

  /**
   * Generate compliance recommendations
   */
  generateComplianceRecommendations(violations, warnings) {
    const recommendations = [];
    
    violations.forEach(violation => {
      if (violation.includes('Attribution')) {
        recommendations.push('Add proper attribution text to your usage');
      }
      if (violation.includes('Commercial use')) {
        recommendations.push('Obtain commercial license or use for non-commercial purposes only');
      }
      if (violation.includes('territory')) {
        recommendations.push('Obtain rights for the specific territory or limit usage to permitted territories');
      }
    });

    return recommendations;
  }

  /**
   * Generate unique IDs
   */
  generateAssetId() {
    return 'ip_asset_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  generateOwnershipRecordId() {
    return 'ownership_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  generateLicenseId() {
    return 'license_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  generateTransferId() {
    return 'transfer_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  /**
   * Get all IP assets
   */
  getAllIPAssets() {
    return Array.from(this.ipAssets.values());
  }

  /**
   * Get IP assets by owner
   */
  getIPAssetsByOwner(ownerId) {
    return Array.from(this.ipAssets.values()).filter(asset => {
      const owners = this.extractCurrentOwners(asset.ownershipDetails);
      return owners.includes(ownerId);
    });
  }

  /**
   * Export IP data
   */
  exportIPData(format = 'json') {
    const data = {
      ipAssets: Array.from(this.ipAssets.values()),
      ownershipRecords: Array.from(this.ownershipRecords.values()),
      licenseAgreements: Array.from(this.licenseAgreements.values()),
      exportedAt: new Date().toISOString()
    };

    if (format === 'json') {
      return JSON.stringify(data, null, 2);
    }

    return data;
  }
}

// Export the IP rights framework
export const ipRightsFramework = new IPRightsFramework();
