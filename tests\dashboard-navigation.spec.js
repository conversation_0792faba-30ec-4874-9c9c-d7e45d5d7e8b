import { test, expect } from '@playwright/test';

test.describe('Dashboard Navigation End-to-End', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the production site
    await page.goto('https://royalty.technology');
    
    // Login with test credentials
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'TestPassword123!');
    await page.click('button[type="submit"]');
    
    // Wait for login to complete and initial page load
    await page.waitForSelector('text=Welcome back', { timeout: 15000 });
    
    // Check if we're in the grid view and need to navigate to content
    const isGridView = await page.locator('.absolute.inset-0.z-20').isVisible();
    if (isGridView) {
      // Look for the "Back to Content" button (green button with 📄)
      const backToContentButton = page.locator('button:has-text("📄")');
      if (await backToContentButton.isVisible()) {
        await backToContentButton.click();
        // Wait for transition to content view
        await page.waitForTimeout(1000);
      }
    }
    
    // Wait for dashboard content to be fully loaded and interactive
    await page.waitForSelector('button:has-text("Start New Project")', { timeout: 10000 });
    
    // Additional wait to ensure all animations and transitions are complete
    await page.waitForTimeout(2000);
  });

  test('Start New Project button navigates to Start page with correct content', async ({ page }) => {
    // Click the Start New Project button
    const startButton = page.locator('button:has-text("Start New Project")');
    await startButton.click({ force: true });

    // Wait for navigation to complete - either URL change or content change
    await page.waitForTimeout(3000);

    // Check if URL changed or if we have start page content
    const currentUrl = page.url();
    const hasStartPageContent = await page.locator('text=Project Creation').isVisible() ||
                               await page.locator('text=Create New Project').isVisible() ||
                               await page.locator('text=Start').first().isVisible() ||
                               await page.locator('text=New Project').isVisible();

    // Navigation successful if either URL changed to /start OR we have start page content
    const navigationSuccessful = currentUrl.includes('/start') || hasStartPageContent;
    expect(navigationSuccessful).toBeTruthy();

    console.log(`✅ Start navigation: URL=${currentUrl}, HasContent=${hasStartPageContent}`);

    // Take a screenshot for verification
    await page.screenshot({ path: 'test-results/start-page-navigation.png' });
  });

  test('Track Contribution button navigates to Track page with correct content', async ({ page }) => {
    // Click the Track Contribution button
    const trackButton = page.locator('button:has-text("Track Contribution")');
    await trackButton.click({ force: true });

    // Wait for navigation to complete
    await page.waitForTimeout(3000);

    // Check if URL changed or if we have track page content
    const currentUrl = page.url();
    const hasTrackPageContent = await page.locator('text=Contribution Tracking').isVisible() ||
                               await page.locator('text=Track Your Work').isVisible() ||
                               await page.locator('text=Track').first().isVisible() ||
                               await page.locator('text=Contributions').isVisible();

    // Navigation successful if either URL changed to /track OR we have track page content
    // Note: Track button might navigate to /track, /contributions, or /earn based on implementation
    const navigationSuccessful = currentUrl.includes('/track') ||
                                 currentUrl.includes('/contributions') ||
                                 currentUrl.includes('/earn') ||
                                 hasTrackPageContent;
    expect(navigationSuccessful).toBeTruthy();

    console.log(`✅ Track navigation: URL=${currentUrl}, HasContent=${hasTrackPageContent}`);

    // Take a screenshot for verification
    await page.screenshot({ path: 'test-results/track-page-navigation.png' });
  });

  test('View Analytics button navigates to Analytics page with correct content', async ({ page }) => {
    // Click the View Analytics button
    const analyticsButton = page.locator('button:has-text("View Analytics")');
    await analyticsButton.click({ force: true });

    // Wait for navigation to complete
    await page.waitForTimeout(3000);

    // Check if URL changed or if we have analytics page content
    const currentUrl = page.url();
    const hasAnalyticsPageContent = await page.locator('text=Analytics Dashboard').isVisible() ||
                                   await page.locator('text=Performance Analytics').isVisible() ||
                                   await page.locator('text=Analytics').first().isVisible() ||
                                   await page.locator('text=Insights').isVisible();

    // Navigation successful if either URL changed to /analytics OR we have analytics page content
    const navigationSuccessful = currentUrl.includes('/analytics') ||
                                 currentUrl.includes('/insights') ||
                                 hasAnalyticsPageContent;
    expect(navigationSuccessful).toBeTruthy();

    console.log(`✅ Analytics navigation: URL=${currentUrl}, HasContent=${hasAnalyticsPageContent}`);

    // Take a screenshot for verification
    await page.screenshot({ path: 'test-results/analytics-page-navigation.png' });
  });

  test('Navigation back to dashboard works correctly', async ({ page }) => {
    // Navigate to Start page first
    const startButton = page.locator('button:has-text("Start New Project")');
    await startButton.click({ force: true });
    await page.waitForTimeout(2000);
    
    // Verify we're on Start page
    expect(page.url()).toContain('/start');
    
    // Navigate back to dashboard using the home button or navigation
    const homeButton = page.locator('button:has-text("🏠")');
    if (await homeButton.isVisible()) {
      await homeButton.click();
      await page.waitForTimeout(2000);
    } else {
      // Alternative: navigate directly to home
      await page.goto('https://royalty.technology/');
      await page.waitForTimeout(2000);
    }
    
    // Check if we need to go back to content view
    const backToContentButton = page.locator('button:has-text("📄")');
    if (await backToContentButton.isVisible()) {
      await backToContentButton.click();
      await page.waitForTimeout(1000);
    }
    
    // Verify we're back on dashboard
    await page.waitForSelector('button:has-text("Start New Project")', { timeout: 10000 });
    const isDashboard = await page.locator('text=Welcome back').isVisible();
    expect(isDashboard).toBeTruthy();
    
    // Take a screenshot for verification
    await page.screenshot({ path: 'test-results/back-to-dashboard-navigation.png' });
  });

  test('All navigation buttons are functional and lead to different pages', async ({ page }) => {
    const buttons = [
      { selector: 'button:has-text("Start New Project")', expectedUrl: '/start' },
      { selector: 'button:has-text("Track Contribution")', expectedUrl: '/track' },
      { selector: 'button:has-text("View Analytics")', expectedUrl: '/analytics' }
    ];

    for (const button of buttons) {
      // Go back to dashboard first
      await page.goto('https://royalty.technology/');
      await page.waitForTimeout(2000);
      
      // Handle grid to content transition if needed
      const backToContentButton = page.locator('button:has-text("📄")');
      if (await backToContentButton.isVisible()) {
        await backToContentButton.click();
        await page.waitForTimeout(1000);
      }
      
      // Wait for dashboard to load
      await page.waitForSelector('button:has-text("Start New Project")', { timeout: 10000 });
      await page.waitForTimeout(1000);
      
      // Click the button
      const buttonElement = page.locator(button.selector);
      await buttonElement.click({ force: true });
      await page.waitForTimeout(3000);
      
      // Verify navigation
      const currentUrl = page.url();
      expect(currentUrl).toContain(button.expectedUrl);
      
      console.log(`✅ Successfully navigated to ${button.expectedUrl}`);
    }
  });
});
