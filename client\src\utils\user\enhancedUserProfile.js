/**
 * Enhanced User Profile System
 * 
 * Extends user profiles to capture industry, role, collaboration preferences,
 * and agreement-related information for better agreement generation
 */

// ============================================================================
// USER PROFILE ENHANCEMENT STRUCTURE
// ============================================================================

export const ENHANCED_USER_PROFILE = {
  // Basic user information (existing)
  id: null,
  email: '',
  full_name: '',
  avatar_url: '',
  
  // Enhanced profile information
  professional_info: {
    // Industry and role
    primary_industry: '', // 'technology', 'creative', 'service', 'commission'
    secondary_industries: [], // Additional industries of interest
    primary_role: '', // 'developer', 'designer', 'consultant', 'artist', 'marketer', etc.
    experience_level: '', // 'beginner', 'intermediate', 'advanced', 'expert'
    years_experience: 0,
    
    // Skills and expertise
    technical_skills: [], // Array of technical skills
    creative_skills: [], // Array of creative skills
    business_skills: [], // Array of business skills
    certifications: [], // Professional certifications
    
    // Professional details
    company: '',
    job_title: '',
    linkedin_url: '',
    portfolio_url: '',
    github_url: '',
    website_url: ''
  },
  
  collaboration_preferences: {
    // Preferred collaboration types
    preferred_collaboration_types: [], // 'development', 'design', 'consulting', 'creative', 'sales'
    preferred_project_types: [], // 'software', 'game', 'film', 'music', 'art', 'business'
    preferred_project_sizes: [], // 'solo', 'small_team', 'large_team', 'enterprise'
    
    // Working preferences
    availability: '', // 'full_time', 'part_time', 'contract', 'freelance'
    time_zone: '',
    preferred_communication: [], // 'email', 'slack', 'discord', 'zoom', 'in_person'
    remote_work_preference: '', // 'remote_only', 'hybrid', 'in_person', 'flexible'
    
    // Collaboration style
    leadership_style: '', // 'leader', 'collaborator', 'follower', 'flexible'
    decision_making_style: '', // 'consensus', 'democratic', 'autocratic', 'flexible'
    conflict_resolution_style: '', // 'direct', 'diplomatic', 'mediator', 'avoider'
  },
  
  agreement_preferences: {
    // Revenue sharing preferences
    preferred_revenue_models: [], // 'percentage', 'tiered', 'waterfall', 'commission', 'equity'
    minimum_revenue_share: 0, // Minimum percentage willing to accept
    preferred_payment_frequency: '', // 'monthly', 'quarterly', 'annually'
    minimum_payout_threshold: 0, // Minimum dollar amount before payout
    
    // IP rights preferences
    preferred_ip_ownership: '', // 'work_for_hire', 'retained', 'co_ownership', 'licensing'
    attribution_requirements: '', // 'required', 'preferred', 'not_required'
    portfolio_rights: true, // Right to use work in portfolio
    
    // Legal preferences
    preferred_governing_law: '', // State/country preference
    arbitration_preference: '', // 'binding', 'non_binding', 'court_only'
    non_compete_acceptable: false, // Willing to accept non-compete clauses
    
    // Risk tolerance
    equity_participation_interest: false, // Interested in equity-based compensation
    advance_recoupment_acceptable: false, // Willing to accept advance recoupment terms
    performance_based_compensation: false // Interested in performance-based pay
  },
  
  financial_info: {
    // Rate and compensation preferences
    hourly_rate_range: { min: 0, max: 0 },
    project_rate_range: { min: 0, max: 0 },
    currency_preference: 'USD',
    
    // Payment preferences
    preferred_payment_methods: [], // 'ach', 'wire', 'paypal', 'crypto'
    tax_status: '', // 'individual', 'llc', 'corporation', 'international'
    tax_id_provided: false,
    
    // Financial goals
    annual_income_target: 0,
    passive_income_interest: false,
    investment_interest: false
  },
  
  verification_status: {
    // Identity verification
    identity_verified: false,
    identity_verification_date: null,
    identity_verification_method: '', // 'government_id', 'passport', 'drivers_license'
    
    // Professional verification
    professional_verified: false,
    professional_verification_date: null,
    professional_verification_method: '', // 'linkedin', 'portfolio', 'references'
    
    // Financial verification
    financial_verified: false,
    financial_verification_date: null,
    financial_verification_method: '', // 'bank_account', 'tax_documents', 'credit_check'
    
    // Background checks
    background_check_completed: false,
    background_check_date: null,
    background_check_status: '' // 'passed', 'failed', 'pending'
  },
  
  privacy_settings: {
    // Profile visibility
    profile_visibility: 'public', // 'public', 'alliance_members', 'private'
    show_contact_info: false,
    show_financial_info: false,
    show_verification_status: true,
    
    // Communication preferences
    allow_direct_messages: true,
    allow_collaboration_invites: true,
    allow_marketing_emails: false,
    
    // Data sharing
    share_with_alliances: true,
    share_with_partners: false,
    share_for_matching: true
  },
  
  // Metadata
  profile_completion_percentage: 0,
  last_updated: null,
  created_at: null
};

// ============================================================================
// INDUSTRY-SPECIFIC PROFILE TEMPLATES
// ============================================================================

export const INDUSTRY_PROFILE_TEMPLATES = {
  technology: {
    primary_role_options: [
      'software_developer', 'frontend_developer', 'backend_developer', 'fullstack_developer',
      'mobile_developer', 'devops_engineer', 'data_scientist', 'ai_engineer',
      'product_manager', 'ui_ux_designer', 'qa_engineer', 'security_engineer'
    ],
    technical_skills_options: [
      'JavaScript', 'Python', 'React', 'Node.js', 'TypeScript', 'Java', 'C++',
      'AWS', 'Docker', 'Kubernetes', 'PostgreSQL', 'MongoDB', 'GraphQL', 'REST APIs'
    ],
    preferred_collaboration_types: [
      'software_development', 'api_integration', 'database_design', 'ui_ux_design',
      'code_review', 'architecture_planning', 'testing', 'deployment'
    ]
  },
  
  creative: {
    primary_role_options: [
      'graphic_designer', 'video_editor', 'animator', 'photographer', 'illustrator',
      'musician', 'composer', 'sound_engineer', 'writer', 'content_creator',
      'film_director', 'producer', 'voice_actor', 'game_designer'
    ],
    creative_skills_options: [
      'Adobe Creative Suite', 'Figma', 'Sketch', 'Final Cut Pro', 'After Effects',
      'Logic Pro', 'Pro Tools', 'Blender', 'Maya', 'Unity', 'Unreal Engine',
      'Photography', 'Videography', 'Animation', 'Illustration', 'Music Production'
    ],
    preferred_collaboration_types: [
      'creative_direction', 'content_creation', 'brand_design', 'video_production',
      'music_production', 'writing', 'editing', 'post_production'
    ]
  },
  
  service: {
    primary_role_options: [
      'business_consultant', 'marketing_consultant', 'financial_advisor', 'legal_advisor',
      'project_manager', 'business_analyst', 'sales_representative', 'account_manager',
      'hr_consultant', 'operations_consultant', 'strategy_consultant'
    ],
    business_skills_options: [
      'Strategic Planning', 'Project Management', 'Business Analysis', 'Financial Modeling',
      'Marketing Strategy', 'Sales', 'Operations', 'HR Management', 'Legal Compliance',
      'Risk Management', 'Process Improvement', 'Change Management'
    ],
    preferred_collaboration_types: [
      'business_consulting', 'strategic_planning', 'project_management', 'marketing',
      'sales_support', 'operations_improvement', 'financial_planning'
    ]
  }
};

// ============================================================================
// PROFILE ENHANCEMENT FUNCTIONS
// ============================================================================

export class EnhancedUserProfileManager {
  constructor() {
    this.profileTemplate = ENHANCED_USER_PROFILE;
    this.industryTemplates = INDUSTRY_PROFILE_TEMPLATES;
  }
  
  /**
   * Create enhanced profile from basic user data
   */
  createEnhancedProfile(basicUserData, industryPreferences = {}) {
    const enhancedProfile = {
      ...this.profileTemplate,
      ...basicUserData,
      professional_info: {
        ...this.profileTemplate.professional_info,
        primary_industry: industryPreferences.primary_industry || '',
        secondary_industries: industryPreferences.secondary_industries || []
      }
    };
    
    // Calculate initial completion percentage
    enhancedProfile.profile_completion_percentage = this.calculateCompletionPercentage(enhancedProfile);
    
    return enhancedProfile;
  }
  
  /**
   * Update profile with new information
   */
  updateProfile(currentProfile, updates) {
    const updatedProfile = {
      ...currentProfile,
      ...updates,
      last_updated: new Date().toISOString()
    };
    
    // Recalculate completion percentage
    updatedProfile.profile_completion_percentage = this.calculateCompletionPercentage(updatedProfile);
    
    return updatedProfile;
  }
  
  /**
   * Calculate profile completion percentage
   */
  calculateCompletionPercentage(profile) {
    const requiredFields = [
      'full_name', 'email',
      'professional_info.primary_industry',
      'professional_info.primary_role',
      'professional_info.experience_level',
      'collaboration_preferences.preferred_collaboration_types',
      'agreement_preferences.preferred_revenue_models'
    ];
    
    const optionalFields = [
      'professional_info.technical_skills',
      'professional_info.company',
      'collaboration_preferences.availability',
      'collaboration_preferences.time_zone',
      'agreement_preferences.minimum_revenue_share',
      'financial_info.hourly_rate_range'
    ];
    
    let completedRequired = 0;
    let completedOptional = 0;
    
    // Check required fields (70% weight)
    for (const field of requiredFields) {
      if (this.getNestedValue(profile, field)) {
        completedRequired++;
      }
    }
    
    // Check optional fields (30% weight)
    for (const field of optionalFields) {
      const value = this.getNestedValue(profile, field);
      if (value && (Array.isArray(value) ? value.length > 0 : true)) {
        completedOptional++;
      }
    }
    
    const requiredPercentage = (completedRequired / requiredFields.length) * 70;
    const optionalPercentage = (completedOptional / optionalFields.length) * 30;
    
    return Math.round(requiredPercentage + optionalPercentage);
  }
  
  /**
   * Get nested object value by dot notation
   */
  getNestedValue(obj, path) {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  }
  
  /**
   * Get industry-specific options for profile fields
   */
  getIndustryOptions(industry) {
    return this.industryTemplates[industry] || {};
  }
  
  /**
   * Validate profile data
   */
  validateProfile(profile) {
    const errors = [];
    
    // Required field validation
    if (!profile.full_name) errors.push('Full name is required');
    if (!profile.email) errors.push('Email is required');
    if (!profile.professional_info?.primary_industry) errors.push('Primary industry is required');
    if (!profile.professional_info?.primary_role) errors.push('Primary role is required');
    
    // Email format validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (profile.email && !emailRegex.test(profile.email)) {
      errors.push('Invalid email format');
    }
    
    // Rate range validation
    const hourlyRange = profile.financial_info?.hourly_rate_range;
    if (hourlyRange && hourlyRange.min > hourlyRange.max) {
      errors.push('Minimum hourly rate cannot be greater than maximum');
    }
    
    return {
      isValid: errors.length === 0,
      errors,
      completionPercentage: this.calculateCompletionPercentage(profile)
    };
  }
  
  /**
   * Generate profile summary for agreement generation
   */
  generateProfileSummary(profile) {
    return {
      name: profile.full_name,
      email: profile.email,
      industry: profile.professional_info?.primary_industry,
      role: profile.professional_info?.primary_role,
      experience: profile.professional_info?.experience_level,
      skills: [
        ...(profile.professional_info?.technical_skills || []),
        ...(profile.professional_info?.creative_skills || []),
        ...(profile.professional_info?.business_skills || [])
      ],
      collaboration_types: profile.collaboration_preferences?.preferred_collaboration_types || [],
      revenue_preferences: {
        models: profile.agreement_preferences?.preferred_revenue_models || [],
        minimum_share: profile.agreement_preferences?.minimum_revenue_share || 0,
        payment_frequency: profile.agreement_preferences?.preferred_payment_frequency
      },
      ip_preferences: {
        ownership: profile.agreement_preferences?.preferred_ip_ownership,
        attribution: profile.agreement_preferences?.attribution_requirements,
        portfolio_rights: profile.agreement_preferences?.portfolio_rights
      },
      verification_status: {
        identity: profile.verification_status?.identity_verified || false,
        professional: profile.verification_status?.professional_verified || false,
        financial: profile.verification_status?.financial_verified || false
      }
    };
  }
  
  /**
   * Match users based on collaboration preferences
   */
  findCollaborationMatches(userProfile, potentialCollaborators) {
    const matches = [];
    
    for (const collaborator of potentialCollaborators) {
      const matchScore = this.calculateMatchScore(userProfile, collaborator);
      if (matchScore > 0.3) { // 30% minimum match threshold
        matches.push({
          collaborator,
          matchScore,
          matchReasons: this.getMatchReasons(userProfile, collaborator)
        });
      }
    }
    
    // Sort by match score (highest first)
    return matches.sort((a, b) => b.matchScore - a.matchScore);
  }
  
  /**
   * Calculate collaboration match score between two profiles
   */
  calculateMatchScore(profile1, profile2) {
    let score = 0;
    let factors = 0;
    
    // Industry compatibility (25% weight)
    if (this.hasIndustryOverlap(profile1, profile2)) {
      score += 0.25;
    }
    factors++;
    
    // Collaboration type compatibility (25% weight)
    if (this.hasCollaborationTypeOverlap(profile1, profile2)) {
      score += 0.25;
    }
    factors++;
    
    // Skills complementarity (20% weight)
    const skillsScore = this.calculateSkillsCompatibility(profile1, profile2);
    score += skillsScore * 0.2;
    factors++;
    
    // Agreement preferences compatibility (15% weight)
    if (this.hasAgreementCompatibility(profile1, profile2)) {
      score += 0.15;
    }
    factors++;
    
    // Working style compatibility (15% weight)
    if (this.hasWorkingStyleCompatibility(profile1, profile2)) {
      score += 0.15;
    }
    factors++;
    
    return score;
  }
  
  // Helper methods for match calculation
  hasIndustryOverlap(profile1, profile2) {
    const industries1 = [
      profile1.professional_info?.primary_industry,
      ...(profile1.professional_info?.secondary_industries || [])
    ].filter(Boolean);
    
    const industries2 = [
      profile2.professional_info?.primary_industry,
      ...(profile2.professional_info?.secondary_industries || [])
    ].filter(Boolean);
    
    return industries1.some(industry => industries2.includes(industry));
  }
  
  hasCollaborationTypeOverlap(profile1, profile2) {
    const types1 = profile1.collaboration_preferences?.preferred_collaboration_types || [];
    const types2 = profile2.collaboration_preferences?.preferred_collaboration_types || [];
    
    return types1.some(type => types2.includes(type));
  }
  
  calculateSkillsCompatibility(profile1, profile2) {
    const skills1 = this.getAllSkills(profile1);
    const skills2 = this.getAllSkills(profile2);
    
    const overlap = skills1.filter(skill => skills2.includes(skill)).length;
    const total = new Set([...skills1, ...skills2]).size;
    
    return total > 0 ? overlap / total : 0;
  }
  
  getAllSkills(profile) {
    return [
      ...(profile.professional_info?.technical_skills || []),
      ...(profile.professional_info?.creative_skills || []),
      ...(profile.professional_info?.business_skills || [])
    ];
  }
  
  hasAgreementCompatibility(profile1, profile2) {
    const prefs1 = profile1.agreement_preferences || {};
    const prefs2 = profile2.agreement_preferences || {};
    
    // Check if revenue models are compatible
    const models1 = prefs1.preferred_revenue_models || [];
    const models2 = prefs2.preferred_revenue_models || [];
    
    return models1.some(model => models2.includes(model));
  }
  
  hasWorkingStyleCompatibility(profile1, profile2) {
    const collab1 = profile1.collaboration_preferences || {};
    const collab2 = profile2.collaboration_preferences || {};
    
    // Check availability compatibility
    if (collab1.availability && collab2.availability) {
      const compatibleAvailability = [
        ['full_time', 'part_time'],
        ['contract', 'freelance'],
        ['part_time', 'contract']
      ];
      
      for (const [type1, type2] of compatibleAvailability) {
        if ((collab1.availability === type1 && collab2.availability === type2) ||
            (collab1.availability === type2 && collab2.availability === type1)) {
          return true;
        }
      }
    }
    
    return false;
  }
  
  getMatchReasons(profile1, profile2) {
    const reasons = [];
    
    if (this.hasIndustryOverlap(profile1, profile2)) {
      reasons.push('Shared industry experience');
    }
    
    if (this.hasCollaborationTypeOverlap(profile1, profile2)) {
      reasons.push('Compatible collaboration preferences');
    }
    
    if (this.calculateSkillsCompatibility(profile1, profile2) > 0.3) {
      reasons.push('Complementary skills');
    }
    
    if (this.hasAgreementCompatibility(profile1, profile2)) {
      reasons.push('Compatible agreement preferences');
    }
    
    return reasons;
  }
}

// Export the profile manager instance
export const profileManager = new EnhancedUserProfileManager();
