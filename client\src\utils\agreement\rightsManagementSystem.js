/**
 * Rights Management System
 *
 * Comprehensive system for managing derivative works, moral rights, reversion rights, and attribution requirements:
 * - Derivative works tracking and permissions
 * - Moral rights management and enforcement
 * - Reversion rights and termination handling
 * - Attribution requirements and compliance
 * - Rights succession and inheritance
 */

import { ipRightsFramework } from './ipRightsFramework.js';

// ============================================================================
// RIGHTS TYPES AND CATEGORIES
// ============================================================================

export const RIGHTS_CATEGORIES = {
  // Economic Rights
  REPRODUCTION_RIGHTS: 'reproduction_rights',
  DISTRIBUTION_RIGHTS: 'distribution_rights',
  PUBLIC_PERFORMANCE_RIGHTS: 'public_performance_rights',
  PUBLIC_DISPLAY_RIGHTS: 'public_display_rights',
  DERIVATIVE_WORK_RIGHTS: 'derivative_work_rights',
  DIGITAL_TRANSMISSION_RIGHTS: 'digital_transmission_rights',

  // Moral Rights
  ATTRIBUTION_RIGHTS: 'attribution_rights',
  INTEGRITY_RIGHTS: 'integrity_rights',
  DISCLOSURE_RIGHTS: 'disclosure_rights',
  WITHDRAWAL_RIGHTS: 'withdrawal_rights',

  // Neighboring Rights
  PERFORMERS_RIGHTS: 'performers_rights',
  PRODUCERS_RIGHTS: 'producers_rights',
  BROADCASTERS_RIGHTS: 'broadcasters_rights',

  // Special Rights
  RESALE_RIGHTS: 'resale_rights',
  RENTAL_RIGHTS: 'rental_rights',
  LENDING_RIGHTS: 'lending_rights',
  SYNCHRONIZATION_RIGHTS: 'synchronization_rights'
};

export const DERIVATIVE_WORK_TYPES = {
  ADAPTATION: 'adaptation',
  TRANSLATION: 'translation',
  ARRANGEMENT: 'arrangement',
  COMPILATION: 'compilation',
  REMIX: 'remix',
  MODIFICATION: 'modification',
  ENHANCEMENT: 'enhancement',
  INTEGRATION: 'integration'
};

export const REVERSION_TRIGGERS = {
  TIME_BASED: 'time_based',
  PERFORMANCE_BASED: 'performance_based',
  BREACH_BASED: 'breach_based',
  TERMINATION_BASED: 'termination_based',
  STATUTORY: 'statutory',
  VOLUNTARY: 'voluntary'
};

export const ATTRIBUTION_LEVELS = {
  FULL_ATTRIBUTION: 'full_attribution',
  CREDIT_ONLY: 'credit_only',
  MINIMAL_ATTRIBUTION: 'minimal_attribution',
  NO_ATTRIBUTION: 'no_attribution',
  PSEUDONYMOUS: 'pseudonymous',
  ANONYMOUS: 'anonymous'
};

// ============================================================================
// RIGHTS MANAGEMENT SYSTEM CLASS
// ============================================================================

export class RightsManagementSystem {
  constructor() {
    this.rightsRegistry = new Map();
    this.derivativeWorks = new Map();
    this.moralRightsRecords = new Map();
    this.reversionSchedule = new Map();
    this.attributionRequirements = new Map();
    this.rightsSuccession = new Map();
  }

  /**
   * Register rights for an IP asset
   */
  registerRights(rightsDefinition) {
    const rightsRecord = {
      id: rightsDefinition.id || this.generateRightsId(),

      // Asset information
      assetId: rightsDefinition.assetId,
      assetTitle: rightsDefinition.assetTitle,
      assetType: rightsDefinition.assetType,

      // Rights holder information
      rightsHolder: rightsDefinition.rightsHolder,
      originalCreator: rightsDefinition.originalCreator,

      // Economic rights
      economicRights: {
        reproduction: rightsDefinition.economicRights?.reproduction || this.getDefaultEconomicRights(),
        distribution: rightsDefinition.economicRights?.distribution || this.getDefaultEconomicRights(),
        publicPerformance: rightsDefinition.economicRights?.publicPerformance || this.getDefaultEconomicRights(),
        publicDisplay: rightsDefinition.economicRights?.publicDisplay || this.getDefaultEconomicRights(),
        derivativeWorks: rightsDefinition.economicRights?.derivativeWorks || this.getDefaultEconomicRights(),
        digitalTransmission: rightsDefinition.economicRights?.digitalTransmission || this.getDefaultEconomicRights()
      },

      // Moral rights
      moralRights: {
        attribution: rightsDefinition.moralRights?.attribution || this.getDefaultMoralRights(),
        integrity: rightsDefinition.moralRights?.integrity || this.getDefaultMoralRights(),
        disclosure: rightsDefinition.moralRights?.disclosure || this.getDefaultMoralRights(),
        withdrawal: rightsDefinition.moralRights?.withdrawal || this.getDefaultMoralRights()
      },

      // Duration and expiration
      duration: rightsDefinition.duration || this.calculateDefaultDuration(rightsDefinition.assetType),
      expirationDate: rightsDefinition.expirationDate,
      renewalOptions: rightsDefinition.renewalOptions || [],

      // Territorial scope
      territory: rightsDefinition.territory || 'worldwide',
      jurisdictions: rightsDefinition.jurisdictions || [],

      // Transfer and licensing
      transferable: rightsDefinition.transferable !== false,
      licensable: rightsDefinition.licensable !== false,
      sublicensable: rightsDefinition.sublicensable || false,

      // Reversion rights
      reversionRights: rightsDefinition.reversionRights || {},

      // Attribution requirements
      attributionRequirements: rightsDefinition.attributionRequirements || {},

      // Status and metadata
      status: 'active',
      registrationDate: new Date().toISOString(),
      lastUpdated: new Date().toISOString()
    };

    this.rightsRegistry.set(rightsRecord.id, rightsRecord);

    // Set up reversion schedule if applicable
    if (rightsRecord.reversionRights && Object.keys(rightsRecord.reversionRights).length > 0) {
      this.scheduleReversion(rightsRecord);
    }

    // Set up attribution requirements
    if (rightsRecord.attributionRequirements && Object.keys(rightsRecord.attributionRequirements).length > 0) {
      this.registerAttributionRequirements(rightsRecord);
    }

    return rightsRecord;
  }

  /**
   * Create derivative work record
   */
  createDerivativeWork(derivativeDefinition) {
    const derivativeWork = {
      id: derivativeDefinition.id || this.generateDerivativeId(),

      // Original work information
      originalAssetId: derivativeDefinition.originalAssetId,
      originalTitle: derivativeDefinition.originalTitle,
      originalCreator: derivativeDefinition.originalCreator,

      // Derivative work information
      derivativeTitle: derivativeDefinition.derivativeTitle,
      derivativeCreator: derivativeDefinition.derivativeCreator,
      derivativeType: derivativeDefinition.derivativeType,

      // Transformation details
      transformationDescription: derivativeDefinition.transformationDescription,
      originalElements: derivativeDefinition.originalElements || [],
      newElements: derivativeDefinition.newElements || [],

      // Rights and permissions
      permissionGranted: derivativeDefinition.permissionGranted || false,
      permissionSource: derivativeDefinition.permissionSource,
      permissionDocument: derivativeDefinition.permissionDocument,

      // Usage rights for derivative
      derivativeRights: {
        reproduction: derivativeDefinition.derivativeRights?.reproduction || false,
        distribution: derivativeDefinition.derivativeRights?.distribution || false,
        publicPerformance: derivativeDefinition.derivativeRights?.publicPerformance || false,
        commercialUse: derivativeDefinition.derivativeRights?.commercialUse || false,
        furtherDerivatives: derivativeDefinition.derivativeRights?.furtherDerivatives || false
      },

      // Attribution requirements
      originalAttribution: derivativeDefinition.originalAttribution || {},
      derivativeAttribution: derivativeDefinition.derivativeAttribution || {},

      // Revenue sharing (if applicable)
      revenueSharing: derivativeDefinition.revenueSharing || {},

      // Legal compliance
      fairUseAnalysis: derivativeDefinition.fairUseAnalysis || {},
      copyrightNotice: derivativeDefinition.copyrightNotice,

      // Status and metadata
      status: 'active',
      creationDate: derivativeDefinition.creationDate || new Date().toISOString(),
      registrationDate: new Date().toISOString()
    };

    this.derivativeWorks.set(derivativeWork.id, derivativeWork);

    // Update original work's derivative tracking
    this.updateOriginalWorkDerivatives(derivativeWork.originalAssetId, derivativeWork.id);

    return derivativeWork;
  }

  /**
   * Manage moral rights
   */
  manageMoralRights(moralRightsDefinition) {
    const moralRightsRecord = {
      id: moralRightsDefinition.id || this.generateMoralRightsId(),

      // Asset and creator information
      assetId: moralRightsDefinition.assetId,
      creatorId: moralRightsDefinition.creatorId,
      creatorName: moralRightsDefinition.creatorName,

      // Attribution rights
      attributionRights: {
        required: moralRightsDefinition.attributionRights?.required !== false,
        format: moralRightsDefinition.attributionRights?.format || 'standard',
        placement: moralRightsDefinition.attributionRights?.placement || 'prominent',
        duration: moralRightsDefinition.attributionRights?.duration || 'perpetual',
        waivable: moralRightsDefinition.attributionRights?.waivable || false
      },

      // Integrity rights
      integrityRights: {
        protected: moralRightsDefinition.integrityRights?.protected !== false,
        modificationConsent: moralRightsDefinition.integrityRights?.modificationConsent || 'required',
        derogationProtection: moralRightsDefinition.integrityRights?.derogationProtection !== false,
        contextualIntegrity: moralRightsDefinition.integrityRights?.contextualIntegrity !== false,
        waivable: moralRightsDefinition.integrityRights?.waivable || false
      },

      // Disclosure rights
      disclosureRights: {
        publicationControl: moralRightsDefinition.disclosureRights?.publicationControl !== false,
        anonymityOption: moralRightsDefinition.disclosureRights?.anonymityOption !== false,
        pseudonymOption: moralRightsDefinition.disclosureRights?.pseudonymOption !== false,
        revealIdentityRight: moralRightsDefinition.disclosureRights?.revealIdentityRight !== false
      },

      // Withdrawal rights
      withdrawalRights: {
        available: moralRightsDefinition.withdrawalRights?.available || false,
        conditions: moralRightsDefinition.withdrawalRights?.conditions || [],
        compensationRequired: moralRightsDefinition.withdrawalRights?.compensationRequired || false,
        noticeRequirements: moralRightsDefinition.withdrawalRights?.noticeRequirements || {}
      },

      // Jurisdiction-specific provisions
      jurisdiction: moralRightsDefinition.jurisdiction || 'US',
      jurisdictionSpecificRights: moralRightsDefinition.jurisdictionSpecificRights || {},

      // Enforcement and remedies
      enforcementMechanisms: moralRightsDefinition.enforcementMechanisms || [],
      remedies: moralRightsDefinition.remedies || [],

      // Status
      status: 'active',
      effectiveDate: moralRightsDefinition.effectiveDate || new Date().toISOString(),
      lastUpdated: new Date().toISOString()
    };

    this.moralRightsRecords.set(moralRightsRecord.id, moralRightsRecord);
    return moralRightsRecord;
  }

  /**
   * Schedule reversion of rights
   */
  scheduleReversion(rightsRecord) {
    const reversionSchedule = {
      id: this.generateReversionId(),
      rightsRecordId: rightsRecord.id,
      assetId: rightsRecord.assetId,

      // Reversion triggers
      triggers: this.processReversionTriggers(rightsRecord.reversionRights),

      // Rights to revert
      reversingRights: rightsRecord.reversionRights.reversingRights || 'all',
      revertingTo: rightsRecord.reversionRights.revertingTo || rightsRecord.originalCreator,

      // Conditions and requirements
      conditions: rightsRecord.reversionRights.conditions || [],
      noticeRequirements: rightsRecord.reversionRights.noticeRequirements || {},

      // Compensation and settlement
      compensationRequired: rightsRecord.reversionRights.compensationRequired || false,
      settlementTerms: rightsRecord.reversionRights.settlementTerms || {},

      // Status
      status: 'scheduled',
      scheduledDate: new Date().toISOString(),
      lastChecked: new Date().toISOString()
    };

    this.reversionSchedule.set(reversionSchedule.id, reversionSchedule);
    return reversionSchedule;
  }

  /**
   * Register attribution requirements
   */
  registerAttributionRequirements(rightsRecord) {
    const attributionReq = {
      id: this.generateAttributionId(),
      assetId: rightsRecord.assetId,
      rightsRecordId: rightsRecord.id,

      // Attribution details
      attributionLevel: rightsRecord.attributionRequirements.level || ATTRIBUTION_LEVELS.FULL_ATTRIBUTION,
      attributionText: rightsRecord.attributionRequirements.text || this.generateDefaultAttribution(rightsRecord),
      attributionFormat: rightsRecord.attributionRequirements.format || 'text',

      // Placement requirements
      placement: {
        location: rightsRecord.attributionRequirements.placement?.location || 'prominent',
        size: rightsRecord.attributionRequirements.placement?.size || 'readable',
        duration: rightsRecord.attributionRequirements.placement?.duration || 'permanent',
        proximity: rightsRecord.attributionRequirements.placement?.proximity || 'adjacent'
      },

      // Context-specific requirements
      contextualRequirements: {
        digital: rightsRecord.attributionRequirements.digital || {},
        print: rightsRecord.attributionRequirements.print || {},
        broadcast: rightsRecord.attributionRequirements.broadcast || {},
        performance: rightsRecord.attributionRequirements.performance || {}
      },

      // Compliance tracking
      complianceTracking: {
        monitoringEnabled: rightsRecord.attributionRequirements.monitoring !== false,
        reportingRequired: rightsRecord.attributionRequirements.reporting || false,
        violationRemedies: rightsRecord.attributionRequirements.remedies || []
      },

      // Status
      status: 'active',
      effectiveDate: new Date().toISOString()
    };

    this.attributionRequirements.set(attributionReq.id, attributionReq);
    return attributionReq;
  }

  /**
   * Check rights permissions for specific usage
   */
  checkRightsPermissions(assetId, requestedUsage, requestingParty) {
    const rightsRecord = this.getRightsRecord(assetId);
    if (!rightsRecord) {
      return {
        permitted: false,
        reason: 'No rights record found for asset'
      };
    }

    const permissions = {
      permitted: true,
      restrictions: [],
      requirements: [],
      attributionRequired: false,
      moralRightsConsiderations: []
    };

    // Check economic rights
    const economicPermissions = this.checkEconomicRights(rightsRecord, requestedUsage, requestingParty);
    if (!economicPermissions.permitted) {
      permissions.permitted = false;
      permissions.restrictions.push(...economicPermissions.restrictions);
    }

    // Check moral rights
    const moralRightsRecord = this.getMoralRightsRecord(assetId);
    if (moralRightsRecord) {
      const moralPermissions = this.checkMoralRights(moralRightsRecord, requestedUsage);
      permissions.requirements.push(...moralPermissions.requirements);
      permissions.moralRightsConsiderations.push(...moralPermissions.considerations);

      if (moralPermissions.attributionRequired) {
        permissions.attributionRequired = true;
        permissions.attributionRequirements = this.getAttributionRequirements(assetId);
      }
    }

    // Check derivative work permissions
    if (requestedUsage.type === 'derivative_work') {
      const derivativePermissions = this.checkDerivativeWorkPermissions(rightsRecord, requestedUsage);
      if (!derivativePermissions.permitted) {
        permissions.permitted = false;
        permissions.restrictions.push(...derivativePermissions.restrictions);
      }
    }

    return permissions;
  }

  /**
   * Process reversion triggers
   */
  processReversionTriggers(reversionRights) {
    const triggers = [];

    if (reversionRights.timeBased) {
      triggers.push({
        type: REVERSION_TRIGGERS.TIME_BASED,
        date: reversionRights.timeBased.date,
        duration: reversionRights.timeBased.duration,
        automatic: reversionRights.timeBased.automatic !== false
      });
    }

    if (reversionRights.performanceBased) {
      triggers.push({
        type: REVERSION_TRIGGERS.PERFORMANCE_BASED,
        metrics: reversionRights.performanceBased.metrics,
        thresholds: reversionRights.performanceBased.thresholds,
        evaluationPeriod: reversionRights.performanceBased.evaluationPeriod
      });
    }

    if (reversionRights.breachBased) {
      triggers.push({
        type: REVERSION_TRIGGERS.BREACH_BASED,
        breachTypes: reversionRights.breachBased.breachTypes,
        curePeriod: reversionRights.breachBased.curePeriod,
        noticeRequirements: reversionRights.breachBased.noticeRequirements
      });
    }

    return triggers;
  }

  /**
   * Monitor reversion triggers
   */
  monitorReversionTriggers() {
    const activeReversions = Array.from(this.reversionSchedule.values()).filter(
      reversion => reversion.status === 'scheduled'
    );

    const triggeredReversions = [];

    for (const reversion of activeReversions) {
      for (const trigger of reversion.triggers) {
        if (this.evaluateReversionTrigger(trigger, reversion)) {
          triggeredReversions.push({
            reversionId: reversion.id,
            trigger,
            triggeredAt: new Date().toISOString()
          });
        }
      }
    }

    return triggeredReversions;
  }

  /**
   * Execute rights reversion
   */
  executeReversion(reversionId, executionDetails = {}) {
    const reversion = this.reversionSchedule.get(reversionId);
    if (!reversion) {
      throw new Error(`Reversion ${reversionId} not found`);
    }

    const rightsRecord = this.rightsRegistry.get(reversion.rightsRecordId);
    if (!rightsRecord) {
      throw new Error(`Rights record ${reversion.rightsRecordId} not found`);
    }

    // Create reversion execution record
    const execution = {
      id: this.generateExecutionId(),
      reversionId,
      rightsRecordId: reversion.rightsRecordId,
      assetId: reversion.assetId,

      // Execution details
      executedAt: new Date().toISOString(),
      executedBy: executionDetails.executedBy,
      trigger: executionDetails.trigger,

      // Rights transfer
      rightsTransferred: reversion.reversingRights,
      transferredFrom: rightsRecord.rightsHolder,
      transferredTo: reversion.revertingTo,

      // Conditions satisfied
      conditionsSatisfied: executionDetails.conditionsSatisfied || [],
      noticeProvided: executionDetails.noticeProvided || false,

      // Compensation
      compensationPaid: executionDetails.compensationPaid || 0,
      settlementCompleted: executionDetails.settlementCompleted || false,

      // Status
      status: 'executed'
    };

    // Update rights record
    rightsRecord.rightsHolder = reversion.revertingTo;
    rightsRecord.lastUpdated = new Date().toISOString();
    rightsRecord.reversionHistory = rightsRecord.reversionHistory || [];
    rightsRecord.reversionHistory.push(execution);

    // Update reversion status
    reversion.status = 'executed';
    reversion.executionRecord = execution.id;

    return execution;
  }

  // ============================================================================
  // HELPER METHODS
  // ============================================================================

  /**
   * Get default economic rights structure
   */
  getDefaultEconomicRights() {
    return {
      granted: true,
      exclusive: false,
      transferable: true,
      duration: 'life_plus_70',
      territory: 'worldwide',
      restrictions: []
    };
  }

  /**
   * Get default moral rights structure
   */
  getDefaultMoralRights() {
    return {
      protected: true,
      waivable: false,
      duration: 'perpetual',
      enforceable: true
    };
  }

  /**
   * Calculate default duration based on asset type
   */
  calculateDefaultDuration(assetType) {
    const durations = {
      'literary_work': 'life_plus_70',
      'musical_work': 'life_plus_70',
      'artistic_work': 'life_plus_70',
      'software': 'life_plus_70',
      'sound_recording': '95_years',
      'audiovisual_work': '95_years',
      'work_for_hire': '95_years'
    };

    return durations[assetType] || 'life_plus_70';
  }

  /**
   * Get rights record for asset
   */
  getRightsRecord(assetId) {
    return Array.from(this.rightsRegistry.values()).find(record => record.assetId === assetId);
  }

  /**
   * Get moral rights record for asset
   */
  getMoralRightsRecord(assetId) {
    return Array.from(this.moralRightsRecords.values()).find(record => record.assetId === assetId);
  }

  /**
   * Check economic rights permissions
   */
  checkEconomicRights(rightsRecord, requestedUsage, requestingParty) {
    const permissions = {
      permitted: true,
      restrictions: []
    };

    // Check if requesting party has the rights
    if (rightsRecord.rightsHolder !== requestingParty) {
      // Check if there's a license
      const hasLicense = this.checkLicensePermissions(rightsRecord.assetId, requestingParty, requestedUsage);
      if (!hasLicense) {
        permissions.permitted = false;
        permissions.restrictions.push('No rights or license for requested usage');
      }
    }

    // Check specific usage rights
    const usageType = requestedUsage.type;
    const relevantRight = rightsRecord.economicRights[usageType];

    if (relevantRight && !relevantRight.granted) {
      permissions.permitted = false;
      permissions.restrictions.push(`${usageType} rights not granted`);
    }

    return permissions;
  }

  /**
   * Check moral rights considerations
   */
  checkMoralRights(moralRightsRecord, requestedUsage) {
    const considerations = {
      requirements: [],
      considerations: [],
      attributionRequired: false
    };

    // Check attribution requirements
    if (moralRightsRecord.attributionRights.required) {
      considerations.attributionRequired = true;
      considerations.requirements.push('Attribution required');
    }

    // Check integrity rights
    if (requestedUsage.modification && moralRightsRecord.integrityRights.protected) {
      if (moralRightsRecord.integrityRights.modificationConsent === 'required') {
        considerations.requirements.push('Creator consent required for modifications');
      }
    }

    return considerations;
  }

  /**
   * Check derivative work permissions
   */
  checkDerivativeWorkPermissions(rightsRecord, requestedUsage) {
    const permissions = {
      permitted: true,
      restrictions: []
    };

    const derivativeRights = rightsRecord.economicRights.derivativeWorks;
    if (!derivativeRights || !derivativeRights.granted) {
      permissions.permitted = false;
      permissions.restrictions.push('Derivative work rights not granted');
    }

    return permissions;
  }

  /**
   * Check license permissions
   */
  checkLicensePermissions(assetId, party, requestedUsage) {
    // This would integrate with the licensing system
    return false; // Simplified for now
  }

  /**
   * Get attribution requirements for asset
   */
  getAttributionRequirements(assetId) {
    return Array.from(this.attributionRequirements.values()).find(req => req.assetId === assetId);
  }

  /**
   * Generate default attribution text
   */
  generateDefaultAttribution(rightsRecord) {
    return `${rightsRecord.assetTitle} © ${new Date().getFullYear()} ${rightsRecord.originalCreator}`;
  }

  /**
   * Update original work derivatives tracking
   */
  updateOriginalWorkDerivatives(originalAssetId, derivativeId) {
    // This would update the original work's record to track derivatives
  }

  /**
   * Evaluate reversion trigger
   */
  evaluateReversionTrigger(trigger, reversion) {
    switch (trigger.type) {
      case REVERSION_TRIGGERS.TIME_BASED:
        return new Date() >= new Date(trigger.date);

      case REVERSION_TRIGGERS.PERFORMANCE_BASED:
        return this.evaluatePerformanceTrigger(trigger, reversion);

      case REVERSION_TRIGGERS.BREACH_BASED:
        return this.evaluateBreachTrigger(trigger, reversion);

      default:
        return false;
    }
  }

  /**
   * Evaluate performance trigger
   */
  evaluatePerformanceTrigger(trigger, reversion) {
    // Implementation for performance-based triggers
    return false; // Simplified
  }

  /**
   * Evaluate breach trigger
   */
  evaluateBreachTrigger(trigger, reversion) {
    // Implementation for breach-based triggers
    return false; // Simplified
  }

  /**
   * Generate unique IDs
   */
  generateRightsId() {
    return 'rights_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  generateDerivativeId() {
    return 'derivative_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  generateMoralRightsId() {
    return 'moral_rights_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  generateReversionId() {
    return 'reversion_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  generateAttributionId() {
    return 'attribution_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  generateExecutionId() {
    return 'execution_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  /**
   * Export rights data
   */
  exportRightsData() {
    return {
      rightsRegistry: Array.from(this.rightsRegistry.values()),
      derivativeWorks: Array.from(this.derivativeWorks.values()),
      moralRightsRecords: Array.from(this.moralRightsRecords.values()),
      reversionSchedule: Array.from(this.reversionSchedule.values()),
      attributionRequirements: Array.from(this.attributionRequirements.values()),
      exportedAt: new Date().toISOString()
    };
  }
}

// Export the rights management system
export const rightsManagementSystem = new RightsManagementSystem();