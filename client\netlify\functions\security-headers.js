// Production Security Headers Configuration
// Authentication & Security Agent: OWASP-compliant security headers
// Created: January 16, 2025

/**
 * Production Security Headers Middleware
 * Implements OWASP-compliant security headers for production deployment
 */

// OWASP-compliant security headers configuration
const SECURITY_HEADERS = {
  // Content Security Policy - Prevents XSS attacks
  'Content-Security-Policy': [
    "default-src 'self'",
    "script-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://unpkg.com",
    "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
    "font-src 'self' https://fonts.gstatic.com",
    "img-src 'self' data: https: blob:",
    "media-src 'self' https:",
    "object-src 'none'",
    "frame-src 'self' https:",
    "connect-src 'self' https://hqqlrrqvjcetoxbdjgzx.supabase.co wss://hqqlrrqvjcetoxbdjgzx.supabase.co https://api.github.com https://accounts.google.com",
    "worker-src 'self' blob:",
    "manifest-src 'self'",
    "base-uri 'self'",
    "form-action 'self'",
    "frame-ancestors 'none'",
    "upgrade-insecure-requests"
  ].join('; '),

  // HTTP Strict Transport Security - Forces HTTPS
  'Strict-Transport-Security': 'max-age=********; includeSubDomains; preload',

  // X-Frame-Options - Prevents clickjacking
  'X-Frame-Options': 'DENY',

  // X-Content-Type-Options - Prevents MIME type sniffing
  'X-Content-Type-Options': 'nosniff',

  // X-XSS-Protection - Enables XSS filtering
  'X-XSS-Protection': '1; mode=block',

  // Referrer Policy - Controls referrer information
  'Referrer-Policy': 'strict-origin-when-cross-origin',

  // Permissions Policy - Controls browser features
  'Permissions-Policy': [
    'geolocation=()',
    'microphone=()',
    'camera=()',
    'payment=()',
    'usb=()',
    'magnetometer=()',
    'gyroscope=()',
    'speaker=()',
    'vibrate=()',
    'fullscreen=(self)',
    'sync-xhr=()'
  ].join(', '),

  // Cross-Origin Embedder Policy
  'Cross-Origin-Embedder-Policy': 'require-corp',

  // Cross-Origin Opener Policy
  'Cross-Origin-Opener-Policy': 'same-origin',

  // Cross-Origin Resource Policy
  'Cross-Origin-Resource-Policy': 'same-origin',

  // Cache Control for security-sensitive responses
  'Cache-Control': 'no-store, no-cache, must-revalidate, proxy-revalidate',

  // Pragma for HTTP/1.0 compatibility
  'Pragma': 'no-cache',

  // Expires header
  'Expires': '0',

  // Server header (hide server information)
  'Server': 'Royaltea-Security',

  // X-Powered-By removal (handled by removing the header)
  'X-Powered-By': null
};

// Environment-specific security configurations
const ENVIRONMENT_CONFIGS = {
  development: {
    'Content-Security-Policy': [
      "default-src 'self'",
      "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://unpkg.com",
      "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
      "font-src 'self' https://fonts.gstatic.com",
      "img-src 'self' data: https: blob:",
      "connect-src 'self' ws: wss: https://hqqlrrqvjcetoxbdjgzx.supabase.co https://api.github.com https://accounts.google.com http://localhost:* ws://localhost:*",
      "frame-ancestors 'self'"
    ].join('; '),
    'Strict-Transport-Security': null // Don't enforce HTTPS in development
  },
  
  staging: {
    ...SECURITY_HEADERS,
    'Strict-Transport-Security': 'max-age=86400; includeSubDomains' // Shorter max-age for staging
  },
  
  production: SECURITY_HEADERS
};

/**
 * Get security headers for current environment
 */
function getSecurityHeaders(environment = 'production') {
  const config = ENVIRONMENT_CONFIGS[environment] || ENVIRONMENT_CONFIGS.production;
  
  // Filter out null values
  return Object.entries(config).reduce((headers, [key, value]) => {
    if (value !== null) {
      headers[key] = value;
    }
    return headers;
  }, {});
}

/**
 * Apply security headers to response
 */
function applySecurityHeaders(response, environment = 'production') {
  const headers = getSecurityHeaders(environment);
  
  // Apply all security headers
  Object.entries(headers).forEach(([key, value]) => {
    response.headers[key] = value;
  });
  
  // Remove potentially dangerous headers
  delete response.headers['X-Powered-By'];
  delete response.headers['Server'];
  
  return response;
}

/**
 * Security headers middleware for Netlify functions
 */
function securityHeadersMiddleware(handler, options = {}) {
  return async (event, context) => {
    const environment = process.env.NODE_ENV || 'production';
    
    try {
      // Call the original handler
      const response = await handler(event, context);
      
      // Apply security headers
      const secureResponse = applySecurityHeaders(response, environment);
      
      // Add CORS headers if needed
      if (options.cors) {
        secureResponse.headers['Access-Control-Allow-Origin'] = options.cors.origin || '*';
        secureResponse.headers['Access-Control-Allow-Methods'] = options.cors.methods || 'GET, POST, PUT, DELETE, OPTIONS';
        secureResponse.headers['Access-Control-Allow-Headers'] = options.cors.headers || 'Content-Type, Authorization, X-Requested-With';
        secureResponse.headers['Access-Control-Max-Age'] = options.cors.maxAge || '86400';
      }
      
      return secureResponse;
    } catch (error) {
      // Ensure security headers are applied even to error responses
      const errorResponse = {
        statusCode: 500,
        headers: {},
        body: JSON.stringify({
          error: 'Internal server error',
          message: 'An unexpected error occurred'
        })
      };
      
      return applySecurityHeaders(errorResponse, environment);
    }
  };
}

/**
 * Validate security headers configuration
 */
function validateSecurityConfig() {
  const issues = [];
  
  // Check CSP configuration
  const csp = SECURITY_HEADERS['Content-Security-Policy'];
  if (!csp.includes("default-src 'self'")) {
    issues.push('CSP should include default-src self');
  }
  if (csp.includes("'unsafe-eval'")) {
    issues.push('CSP should not include unsafe-eval in production');
  }
  
  // Check HSTS configuration
  const hsts = SECURITY_HEADERS['Strict-Transport-Security'];
  if (!hsts.includes('max-age=')) {
    issues.push('HSTS should include max-age directive');
  }
  
  // Check frame options
  const frameOptions = SECURITY_HEADERS['X-Frame-Options'];
  if (!['DENY', 'SAMEORIGIN'].includes(frameOptions)) {
    issues.push('X-Frame-Options should be DENY or SAMEORIGIN');
  }
  
  return {
    valid: issues.length === 0,
    issues: issues
  };
}

/**
 * Generate security report
 */
function generateSecurityReport() {
  const validation = validateSecurityConfig();
  const headers = getSecurityHeaders('production');
  
  return {
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'production',
    validation: validation,
    headers: headers,
    recommendations: [
      'Regularly review and update CSP directives',
      'Monitor security headers with tools like securityheaders.com',
      'Implement security header testing in CI/CD pipeline',
      'Consider implementing Certificate Transparency monitoring',
      'Review and update security headers quarterly'
    ]
  };
}

/**
 * Security headers testing endpoint
 */
exports.handler = async (event, context) => {
  const method = event.httpMethod;
  const path = event.path;
  
  // Handle preflight requests
  if (method === 'OPTIONS') {
    return {
      statusCode: 200,
      headers: getSecurityHeaders(),
      body: ''
    };
  }
  
  // Security headers test endpoint
  if (path.includes('/security-test')) {
    const report = generateSecurityReport();
    
    return {
      statusCode: 200,
      headers: {
        ...getSecurityHeaders(),
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(report)
    };
  }
  
  // Default response with security headers
  return {
    statusCode: 200,
    headers: {
      ...getSecurityHeaders(),
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      message: 'Security headers applied',
      timestamp: new Date().toISOString()
    })
  };
};

// Export utilities for use in other functions
module.exports = {
  getSecurityHeaders,
  applySecurityHeaders,
  securityHeadersMiddleware,
  validateSecurityConfig,
  generateSecurityReport,
  SECURITY_HEADERS,
  ENVIRONMENT_CONFIGS
};
