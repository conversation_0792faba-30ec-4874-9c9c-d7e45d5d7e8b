/**
 * Comprehensive Agreement System Validation Tests
 * 
 * This test suite validates the alliance, venture, and contributor agreement generation systems
 * with focus on:
 * - CoG/VOTA scenario testing
 * - Dynamic contributor management
 * - Unified pool revenue distribution
 * - Tranche-based revenue sharing
 * - Agreement validation against lawyer-approved template
 * - Document comparison and format verification
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { NewAgreementGenerator } from '../utils/agreement/newAgreementGenerator.js';
import { allianceCreationSystem } from '../utils/alliance/allianceCreationSystem.js';

// Get current directory for file operations
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🧪 COMPREHENSIVE AGREEMENT VALIDATION TESTS');
console.log('===========================================\n');

// Test Configuration
const testConfig = {
  outputDir: path.join(__dirname, 'output', 'agreement-validation'),
  templatePath: path.join(__dirname, '../../public/example-cog-contributor-agreement.md'),
  generateFullDocuments: true,
  performValidation: true,
  saveResults: true
};

// Ensure output directory exists
if (!fs.existsSync(testConfig.outputDir)) {
  fs.mkdirSync(testConfig.outputDir, { recursive: true });
}

// Load lawyer-approved template for comparison
let lawyerTemplate = '';
try {
  lawyerTemplate = fs.readFileSync(testConfig.templatePath, 'utf8');
  console.log('✅ Lawyer-approved template loaded successfully');
} catch (error) {
  console.error('❌ Failed to load lawyer-approved template:', error.message);
  process.exit(1);
}

// Test Data: CoG Alliance
const cogAlliance = {
  id: 'alliance_cog_001',
  name: 'City of Gamers',
  description: 'A collaborative alliance for game development and creative projects',
  industry: 'TECHNOLOGY',
  ipOwnershipModel: 'CO_OWNERSHIP',
  jurisdiction: 'Florida',
  currency: 'USD',
  governanceModel: 'DEMOCRATIC',
  votingThreshold: 0.6,
  platformFeePercentage: 10,
  allianceAdminFee: 2,
  disputeResolution: 'ARBITRATION',
  confidentialityPeriod: 24,
  foundingMembers: [
    { name: 'Gynell Journigan', role: 'Founder', email: '<EMAIL>' }
  ],
  status: 'active',
  createdAt: new Date().toISOString()
};

// Test Data: VOTA Venture with Dynamic Contributor Management
const votaVenture = {
  id: 'venture_vota_001',
  allianceId: 'alliance_cog_001',
  name: 'Village of The Ages',
  description: 'A village simulation game where players guide communities through historical progressions',
  venture_type: 'software',
  industry: 'GAMING',
  
  // Dynamic contributor management configuration
  contributorManagement: {
    type: 'dynamic',
    revenueModel: 'unified_pool', // Default unified pool
    trancheBased: true,
    allowLateJoiners: true
  },
  
  // Revenue sharing configuration
  revenueSharing: {
    method: 'contribution_points',
    basePercentage: 70, // 70% to contributors, 30% to platform/alliance
    distributionModel: 'unified_pool',
    trancheSystem: {
      enabled: true,
      trancheSize: 'release_based', // Contributors earn from releases they contributed to
      minimumContribution: 10 // Minimum contribution points to earn from a tranche
    }
  },
  
  contributors: [
    {
      id: 'contributor_001',
      name: 'Lead Developer',
      email: '<EMAIL>',
      role: 'Technical Lead',
      contributionPoints: 100,
      joinedAt: '2024-01-01',
      activeInTranches: ['v1.0', 'v1.1', 'v1.2']
    },
    {
      id: 'contributor_002', 
      name: 'Game Designer',
      email: '<EMAIL>',
      role: 'Creative Director',
      contributionPoints: 80,
      joinedAt: '2024-01-15',
      activeInTranches: ['v1.0', 'v1.1', 'v1.2']
    },
    {
      id: 'contributor_003',
      name: 'Late Joiner Developer',
      email: '<EMAIL>', 
      role: 'Backend Developer',
      contributionPoints: 40,
      joinedAt: '2024-06-01', // Joined later
      activeInTranches: ['v1.2'] // Only active in latest tranche
    }
  ],
  
  milestones: [
    {
      id: 'milestone_001',
      name: 'Alpha Release',
      description: 'Core gameplay mechanics implemented',
      targetDate: '2024-03-01',
      status: 'completed',
      tranche: 'v1.0'
    },
    {
      id: 'milestone_002',
      name: 'Beta Release', 
      description: 'Full feature set with balancing',
      targetDate: '2024-06-01',
      status: 'completed',
      tranche: 'v1.1'
    },
    {
      id: 'milestone_003',
      name: 'Launch Release',
      description: 'Polished game ready for market',
      targetDate: '2024-09-01',
      status: 'in_progress',
      tranche: 'v1.2'
    }
  ],
  
  deliverables: [
    { type: 'software', name: 'Game Engine', status: 'completed' },
    { type: 'software', name: 'User Interface', status: 'completed' },
    { type: 'design', name: 'Art Assets', status: 'in_progress' },
    { type: 'documentation', name: 'User Manual', status: 'planned' }
  ],
  
  status: 'active',
  createdAt: '2024-01-01T00:00:00Z'
};

// Test Contributors for Agreement Generation
const testContributors = [
  {
    id: 'contributor_001',
    name: 'Lead Developer',
    email: '<EMAIL>',
    role: 'Technical Lead'
  },
  {
    id: 'contributor_002',
    name: 'Game Designer', 
    email: '<EMAIL>',
    role: 'Creative Director'
  },
  {
    id: 'contributor_003',
    name: 'Late Joiner Developer',
    email: '<EMAIL>',
    role: 'Backend Developer'
  }
];

console.log('📋 Test Configuration:');
console.log(`   Output Directory: ${testConfig.outputDir}`);
console.log(`   Template Path: ${testConfig.templatePath}`);
console.log(`   Generate Full Documents: ${testConfig.generateFullDocuments}`);
console.log(`   Perform Validation: ${testConfig.performValidation}`);
console.log(`   Save Results: ${testConfig.saveResults}\n`);

// Initialize generators
const agreementGenerator = new NewAgreementGenerator();

console.log('🏗️  INITIALIZING TEST SYSTEMS');
console.log('==============================');

// Create CoG Alliance
const createdCogAlliance = allianceCreationSystem.createAlliance(cogAlliance);
console.log('✅ CoG Alliance Created:', createdCogAlliance.name);

console.log('\n1️⃣ TESTING COG/VOTA AGREEMENT GENERATION');
console.log('=========================================');

// Test function to generate and validate agreements
async function generateAndValidateAgreement(venture, alliance, contributor, testName) {
  console.log(`\n📝 Generating agreement for: ${testName}`);
  console.log(`   Venture: ${venture.name}`);
  console.log(`   Alliance: ${alliance.name}`);
  console.log(`   Contributor: ${contributor.name} (${contributor.role})`);
  
  try {
    // Create project data structure for NewAgreementGenerator
    const projectData = {
      id: venture.id,
      name: venture.name,
      title: venture.name,
      description: venture.description,
      project_type: venture.venture_type,
      team_id: alliance.id,
      alliance_id: alliance.id,
      created_by: contributor.id,
      is_active: true,
      is_public: false
    };
    
    // Generate agreement using NewAgreementGenerator
    const agreement = await agreementGenerator.generateAgreement(
      lawyerTemplate,
      projectData,
      {
        contributors: [contributor],
        currentUser: {
          id: contributor.id,
          email: contributor.email,
          user_metadata: {
            full_name: contributor.name
          }
        },
        fullName: contributor.name,
        royaltyModel: venture.revenueSharing,
        milestones: venture.milestones
      }
    );
    
    if (agreement) {
      console.log('✅ Agreement generated successfully');
      
      // Save the agreement
      if (testConfig.saveResults) {
        const filename = `${testName.toLowerCase().replace(/\s+/g, '-')}-agreement-${new Date().toISOString().split('T')[0]}.md`;
        const filepath = path.join(testConfig.outputDir, filename);
        fs.writeFileSync(filepath, agreement);
        console.log(`   Saved to: ${filepath}`);
      }
      
      return {
        success: true,
        agreement,
        filepath: testConfig.saveResults ? path.join(testConfig.outputDir, `${testName.toLowerCase().replace(/\s+/g, '-')}-agreement-${new Date().toISOString().split('T')[0]}.md`) : null
      };
    } else {
      console.error('❌ Agreement generation failed - no content returned');
      return { success: false, error: 'No content returned' };
    }
    
  } catch (error) {
    console.error('❌ Agreement generation failed:', error.message);
    return { success: false, error: error.message };
  }
}

// Generate agreements for each contributor
const agreementResults = [];

for (const contributor of testContributors) {
  const testName = `CoG VOTA ${contributor.role}`;
  const result = await generateAndValidateAgreement(votaVenture, createdCogAlliance, contributor, testName);
  agreementResults.push({ testName, contributor, result });
}

console.log('\n2️⃣ TESTING DYNAMIC CONTRIBUTOR MANAGEMENT');
console.log('==========================================');

// Test scenario: Adding a new contributor to existing venture
const newContributor = {
  id: 'contributor_004',
  name: 'New UI Designer',
  email: '<EMAIL>',
  role: 'UI/UX Designer'
};

console.log('📝 Dynamic Contributor Scenario:');
console.log(`   Adding: ${newContributor.name} (${newContributor.role})`);
console.log(`   Joining late in project lifecycle`);

// Generate agreement for new contributor
const newContributorResult = await generateAndValidateAgreement(
  votaVenture, 
  createdCogAlliance, 
  newContributor, 
  'CoG VOTA New UI Designer'
);

agreementResults.push({ 
  testName: 'CoG VOTA New UI Designer', 
  contributor: newContributor, 
  result: newContributorResult 
});

console.log('\n3️⃣ TESTING TRANCHE-BASED REVENUE SHARING');
console.log('=========================================');

function validateTrancheRevenue(venture) {
  console.log(`📊 Revenue Analysis for ${venture.name}:`);
  
  const totalContributionPoints = venture.contributors.reduce((sum, c) => sum + c.contributionPoints, 0);
  console.log(`   Total Contribution Points: ${totalContributionPoints}`);
  
  // Analyze by tranche
  const trancheAnalysis = {};
  venture.contributors.forEach(contributor => {
    contributor.activeInTranches.forEach(tranche => {
      if (!trancheAnalysis[tranche]) {
        trancheAnalysis[tranche] = {
          contributors: [],
          totalPoints: 0
        };
      }
      trancheAnalysis[tranche].contributors.push(contributor);
      trancheAnalysis[tranche].totalPoints += contributor.contributionPoints;
    });
  });
  
  console.log('   Tranche Breakdown:');
  Object.entries(trancheAnalysis).forEach(([tranche, data]) => {
    console.log(`     ${tranche}: ${data.contributors.length} contributors, ${data.totalPoints} points`);
    data.contributors.forEach(c => {
      const percentage = ((c.contributionPoints / data.totalPoints) * 100).toFixed(1);
      console.log(`       - ${c.name}: ${c.contributionPoints} points (${percentage}%)`);
    });
  });
  
  return trancheAnalysis;
}

// Validate VOTA tranche revenue
const votaTrancheAnalysis = validateTrancheRevenue(votaVenture);

console.log('\n4️⃣ TESTING UNIFIED POOL REVENUE DISTRIBUTION');
console.log('=============================================');

console.log('📊 Unified Pool Configuration:');
console.log(`   Revenue Model: ${votaVenture.revenueSharing.distributionModel}`);
console.log(`   Base Percentage: ${votaVenture.revenueSharing.basePercentage}%`);
console.log(`   Method: ${votaVenture.revenueSharing.method}`);
console.log(`   Tranche System: ${votaVenture.revenueSharing.trancheSystem.enabled ? 'Enabled' : 'Disabled'}`);
console.log(`   Minimum Contribution: ${votaVenture.revenueSharing.trancheSystem.minimumContribution} points`);

// Validate unified pool ensures all contributors are in same revenue pool
const allContributorsInSamePool = votaVenture.contributors.every(c => 
  votaVenture.revenueSharing.distributionModel === 'unified_pool'
);

console.log(`✅ All contributors in unified pool: ${allContributorsInSamePool}`);

console.log('\n5️⃣ AGREEMENT VALIDATION AGAINST LAWYER TEMPLATE');
console.log('===============================================');

// Function to compare generated agreement with lawyer template
function compareAgreementWithTemplate(generatedAgreement, templateAgreement) {
  console.log('🔍 Performing document comparison...');

  const comparison = {
    structuralMatches: [],
    structuralDifferences: [],
    contentMatches: [],
    contentDifferences: [],
    formatIssues: [],
    missingElements: [],
    score: 0
  };

  // Check for key structural elements
  const keyElements = [
    'CONTRIBUTOR AGREEMENT',
    'Recitals',
    'WHEREAS',
    'NOW THEREFORE',
    '## 1. Definitions',
    '## 2. Treatment of Confidential Information',
    '## 3. Ownership of Work Product',
    '## 4. Non-Disparagement',
    '## 5. Termination',
    '## 6. Equitable Remedies',
    'SCHEDULE A',
    'EXHIBIT I',
    'EXHIBIT II'
  ];

  keyElements.forEach(element => {
    if (generatedAgreement.includes(element) && templateAgreement.includes(element)) {
      comparison.structuralMatches.push(element);
    } else if (!generatedAgreement.includes(element) && templateAgreement.includes(element)) {
      comparison.missingElements.push(element);
    } else if (generatedAgreement.includes(element) && !templateAgreement.includes(element)) {
      comparison.structuralDifferences.push(`Extra element: ${element}`);
    }
  });

  // Check for proper variable replacement
  const unreplacedVariables = generatedAgreement.match(/\{\{[^}]+\}\}/g) || [];
  const placeholderBrackets = generatedAgreement.match(/\[[^\]]*\]/g) || [];

  if (unreplacedVariables.length > 0) {
    comparison.formatIssues.push(`Unreplaced variables: ${unreplacedVariables.join(', ')}`);
  }

  if (placeholderBrackets.length > 0) {
    comparison.formatIssues.push(`Unfilled placeholders: ${placeholderBrackets.slice(0, 5).join(', ')}${placeholderBrackets.length > 5 ? '...' : ''}`);
  }

  // Calculate similarity score
  const totalElements = keyElements.length;
  const matchedElements = comparison.structuralMatches.length;
  comparison.score = Math.round((matchedElements / totalElements) * 100);

  return comparison;
}

// Validate each generated agreement
console.log('📋 Validating Generated Agreements:');

const validationResults = [];

for (const { testName, contributor, result } of agreementResults) {
  if (result.success) {
    console.log(`\n🔍 Validating: ${testName}`);

    const comparison = compareAgreementWithTemplate(result.agreement, lawyerTemplate);

    console.log(`   Structural Matches: ${comparison.structuralMatches.length}`);
    console.log(`   Missing Elements: ${comparison.missingElements.length}`);
    console.log(`   Format Issues: ${comparison.formatIssues.length}`);
    console.log(`   Similarity Score: ${comparison.score}%`);

    if (comparison.missingElements.length > 0) {
      console.log('   ⚠️  Missing Elements:');
      comparison.missingElements.forEach(element => {
        console.log(`      - ${element}`);
      });
    }

    if (comparison.formatIssues.length > 0) {
      console.log('   ⚠️  Format Issues:');
      comparison.formatIssues.forEach(issue => {
        console.log(`      - ${issue}`);
      });
    }

    validationResults.push({
      testName,
      contributor: contributor.name,
      score: comparison.score,
      comparison,
      success: comparison.score >= 80 // 80% threshold for success
    });

    // Save detailed comparison report
    if (testConfig.saveResults) {
      const comparisonReport = `# Agreement Validation Report
## Test: ${testName}
## Contributor: ${contributor.name} (${contributor.role})
## Generated: ${new Date().toISOString()}

### Validation Results
- **Similarity Score**: ${comparison.score}%
- **Structural Matches**: ${comparison.structuralMatches.length}
- **Missing Elements**: ${comparison.missingElements.length}
- **Format Issues**: ${comparison.formatIssues.length}

### Structural Matches
${comparison.structuralMatches.map(match => `- ✅ ${match}`).join('\n')}

### Missing Elements
${comparison.missingElements.map(missing => `- ❌ ${missing}`).join('\n')}

### Format Issues
${comparison.formatIssues.map(issue => `- ⚠️  ${issue}`).join('\n')}

### Structural Differences
${comparison.structuralDifferences.map(diff => `- 🔄 ${diff}`).join('\n')}
`;

      const reportFilename = `validation-report-${testName.toLowerCase().replace(/\s+/g, '-')}-${new Date().toISOString().split('T')[0]}.md`;
      const reportFilepath = path.join(testConfig.outputDir, reportFilename);
      fs.writeFileSync(reportFilepath, comparisonReport);
      console.log(`   📄 Validation report saved: ${reportFilename}`);
    }
  } else {
    console.log(`\n❌ Skipping validation for ${testName} - generation failed`);
    validationResults.push({
      testName,
      contributor: contributor.name,
      score: 0,
      success: false,
      error: result.error
    });
  }
}

console.log('\n6️⃣ TESTING VARIABLE TEAM COMPOSITION SCENARIOS');
console.log('==============================================');

// Test different team composition scenarios
const teamCompositionScenarios = [
  {
    name: 'Small Team (2 contributors)',
    contributors: testContributors.slice(0, 2)
  },
  {
    name: 'Medium Team (3 contributors)',
    contributors: testContributors
  },
  {
    name: 'Large Team (4+ contributors)',
    contributors: [...testContributors, newContributor]
  }
];

console.log('📊 Team Composition Analysis:');

teamCompositionScenarios.forEach(scenario => {
  console.log(`\n   ${scenario.name}:`);
  console.log(`     Contributors: ${scenario.contributors.length}`);
  console.log(`     Roles: ${scenario.contributors.map(c => c.role).join(', ')}`);

  // Calculate theoretical revenue distribution for unified pool
  const equalShare = (100 / scenario.contributors.length).toFixed(1);
  console.log(`     Equal Share: ${equalShare}% each`);

  // Validate that agreements can be generated for all team sizes
  console.log(`     ✅ Agreement generation supported for ${scenario.contributors.length} contributors`);
});

console.log('\n7️⃣ INTEGRATION FLOW VALIDATION');
console.log('==============================');

console.log('🔄 Testing End-to-End Integration Flow:');
console.log('   Alliance Creation → Venture Setup → Agreement Generation → Validation');

// Validate the complete flow worked
const flowValidation = {
  allianceCreated: !!createdCogAlliance && createdCogAlliance.name === 'City of Gamers',
  ventureConfigured: !!votaVenture && votaVenture.name === 'Village of The Ages',
  agreementsGenerated: agreementResults.filter(r => r.result.success).length,
  validationsPassed: validationResults.filter(r => r.success).length,
  overallSuccess: false
};

flowValidation.overallSuccess = flowValidation.allianceCreated &&
                                flowValidation.ventureConfigured &&
                                flowValidation.agreementsGenerated > 0 &&
                                flowValidation.validationsPassed > 0;

console.log('📊 Integration Flow Results:');
console.log(`   ✅ Alliance Created: ${flowValidation.allianceCreated}`);
console.log(`   ✅ Venture Configured: ${flowValidation.ventureConfigured}`);
console.log(`   ✅ Agreements Generated: ${flowValidation.agreementsGenerated}/${agreementResults.length}`);
console.log(`   ✅ Validations Passed: ${flowValidation.validationsPassed}/${validationResults.length}`);
console.log(`   🎯 Overall Success: ${flowValidation.overallSuccess}`);

console.log('\n8️⃣ FINAL SUMMARY AND RECOMMENDATIONS');
console.log('====================================');

// Generate final summary report
const summary = {
  totalTests: agreementResults.length,
  successfulGenerations: agreementResults.filter(r => r.result.success).length,
  successfulValidations: validationResults.filter(r => r.success).length,
  averageScore: validationResults.reduce((sum, r) => sum + r.score, 0) / validationResults.length,
  recommendations: []
};

console.log('📊 Test Summary:');
console.log(`   Total Tests: ${summary.totalTests}`);
console.log(`   Successful Generations: ${summary.successfulGenerations}/${summary.totalTests}`);
console.log(`   Successful Validations: ${summary.successfulValidations}/${summary.totalTests}`);
console.log(`   Average Similarity Score: ${summary.averageScore.toFixed(1)}%`);

// Generate recommendations based on results
if (summary.averageScore < 80) {
  summary.recommendations.push('Improve template variable replacement accuracy');
}

if (summary.successfulGenerations < summary.totalTests) {
  summary.recommendations.push('Fix agreement generation failures');
}

const lowScoreTests = validationResults.filter(r => r.score < 70);
if (lowScoreTests.length > 0) {
  summary.recommendations.push(`Review low-scoring tests: ${lowScoreTests.map(t => t.testName).join(', ')}`);
}

if (summary.recommendations.length > 0) {
  console.log('\n💡 Recommendations:');
  summary.recommendations.forEach(rec => {
    console.log(`   - ${rec}`);
  });
} else {
  console.log('\n🎉 All tests passed successfully! System is ready for production.');
}

// Save comprehensive summary report
if (testConfig.saveResults) {
  const summaryReport = `# Comprehensive Agreement System Test Summary
## Generated: ${new Date().toISOString()}

### Test Overview
- **Total Tests**: ${summary.totalTests}
- **Successful Generations**: ${summary.successfulGenerations}/${summary.totalTests}
- **Successful Validations**: ${summary.successfulValidations}/${summary.totalTests}
- **Average Similarity Score**: ${summary.averageScore.toFixed(1)}%

### Individual Test Results
${validationResults.map(result => `
#### ${result.testName}
- **Contributor**: ${result.contributor}
- **Score**: ${result.score}%
- **Status**: ${result.success ? '✅ PASSED' : '❌ FAILED'}
${result.error ? `- **Error**: ${result.error}` : ''}
`).join('')}

### Integration Flow Validation
- **Alliance Created**: ${flowValidation.allianceCreated ? '✅' : '❌'}
- **Venture Configured**: ${flowValidation.ventureConfigured ? '✅' : '❌'}
- **Agreements Generated**: ${flowValidation.agreementsGenerated}/${agreementResults.length}
- **Validations Passed**: ${flowValidation.validationsPassed}/${validationResults.length}
- **Overall Success**: ${flowValidation.overallSuccess ? '✅' : '❌'}

### Revenue Sharing Analysis
- **Model**: ${votaVenture.revenueSharing.distributionModel}
- **Base Percentage**: ${votaVenture.revenueSharing.basePercentage}%
- **Tranche System**: ${votaVenture.revenueSharing.trancheSystem.enabled ? 'Enabled' : 'Disabled'}
- **Unified Pool**: All contributors in same revenue pool

### Recommendations
${summary.recommendations.length > 0 ? summary.recommendations.map(rec => `- ${rec}`).join('\n') : '- System is ready for production use'}

### Files Generated
${agreementResults.filter(r => r.result.success && r.result.filepath).map(r => `- ${path.basename(r.result.filepath)}`).join('\n')}
`;

  const summaryFilepath = path.join(testConfig.outputDir, `comprehensive-test-summary-${new Date().toISOString().split('T')[0]}.md`);
  fs.writeFileSync(summaryFilepath, summaryReport);
  console.log(`\n📄 Comprehensive summary saved: ${path.basename(summaryFilepath)}`);
}

console.log('\n🎯 COMPREHENSIVE AGREEMENT VALIDATION COMPLETE');
console.log('==============================================');
console.log(`📁 All results saved to: ${testConfig.outputDir}`);
console.log('🔍 Review generated agreements and validation reports for detailed analysis');

// Export results for potential use in other tests
export {
  agreementResults,
  validationResults,
  summary,
  flowValidation,
  votaTrancheAnalysis
};
