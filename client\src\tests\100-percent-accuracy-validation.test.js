/**
 * 100% Legal Accuracy Validation Test Suite
 * 
 * This test suite validates that the agreement generation system achieves
 * 100% accuracy in all legal fields with zero tolerance for errors.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { NewAgreementGenerator } from '../utils/agreement/newAgreementGenerator.js';
import { LegalAccuracyValidator } from '../utils/agreement/legalAccuracyValidator.js';
import { allianceCreationSystem } from '../utils/alliance/allianceCreationSystem.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🎯 100% LEGAL ACCURACY VALIDATION TESTS');
console.log('======================================\n');

// Configuration
const config = {
  templatePath: path.join(__dirname, '../../public/example-cog-contributor-agreement.md'),
  outputDir: path.join(__dirname, 'output', '100-percent-accuracy'),
  saveResults: true,
  requirePerfectScore: true // Require 100% accuracy
};

// Ensure output directory exists
if (!fs.existsSync(config.outputDir)) {
  fs.mkdirSync(config.outputDir, { recursive: true });
}

// Load lawyer-approved template
let lawyerTemplate = '';
try {
  lawyerTemplate = fs.readFileSync(config.templatePath, 'utf8');
  console.log('✅ Lawyer-approved template loaded for accuracy validation');
} catch (error) {
  console.error('❌ Failed to load template:', error.message);
  process.exit(1);
}

// Initialize systems
const agreementGenerator = new NewAgreementGenerator();
const accuracyValidator = new LegalAccuracyValidator();

// Test data with complete legal information
const testScenarios = [
  {
    name: 'Perfect CoG Alliance Test',
    alliance: {
      id: 'alliance_cog_001',
      name: 'City of Gamers Inc.',
      business_address: {
        street: '1205 43rd Street, Suite B',
        city: 'Orlando',
        state: 'Florida',
        zipCode: '32839',
        county: 'Orange County',
        full_address: '1205 43rd Street, Suite B, Orlando, Florida 32839'
      },
      legal_entity_info: {
        legalName: 'City of Gamers Inc.',
        incorporationState: 'Florida'
      },
      contact_information: {
        email: '<EMAIL>',
        primaryContact: {
          name: 'Gynell Journigan',
          title: 'President',
          email: '<EMAIL>'
        }
      },
      jurisdiction: 'Florida'
    },
    project: {
      id: 'project_vota_001',
      name: 'Village of The Ages',
      title: 'Village of The Ages',
      description: 'A village simulation game where players guide communities through historical progressions',
      project_type: 'software',
      alliance_id: 'alliance_cog_001',
      company_name: 'City of Gamers Inc.',
      state: 'Florida',
      city: 'Orlando',
      address: '1205 43rd Street, Suite B, Orlando, Florida 32839',
      contact_email: '<EMAIL>'
    },
    contributor: {
      id: 'contributor_001',
      name: 'Test Developer',
      email: '<EMAIL>',
      role: 'Lead Developer'
    },
    expectedData: {
      company: {
        name: 'City of Gamers Inc.',
        address: '1205 43rd Street, Suite B, Orlando, Florida 32839',
        state: 'Florida',
        city: 'Orlando',
        signerName: 'Gynell Journigan',
        signerTitle: 'President',
        billingEmail: '<EMAIL>'
      },
      contributor: {
        name: 'Test Developer',
        email: '<EMAIL>'
      },
      jurisdiction: {
        state: 'Florida'
      }
    }
  },
  {
    name: 'Custom User Alliance Test',
    alliance: {
      id: 'alliance_custom_001',
      name: 'Custom Tech Solutions LLC',
      business_address: {
        street: '123 Innovation Drive',
        city: 'Austin',
        state: 'Texas',
        zipCode: '78701',
        county: 'Travis County',
        full_address: '123 Innovation Drive, Austin, Texas 78701'
      },
      legal_entity_info: {
        legalName: 'Custom Tech Solutions LLC',
        incorporationState: 'Texas'
      },
      contact_information: {
        email: '<EMAIL>',
        primaryContact: {
          name: 'Jane Smith',
          title: 'CEO',
          email: '<EMAIL>'
        }
      },
      jurisdiction: 'Texas'
    },
    project: {
      id: 'project_custom_001',
      name: 'Custom Software Platform',
      title: 'Custom Software Platform',
      description: 'An innovative software platform for business automation',
      project_type: 'software',
      alliance_id: 'alliance_custom_001',
      company_name: 'Custom Tech Solutions LLC',
      state: 'Texas',
      city: 'Austin',
      address: '123 Innovation Drive, Austin, Texas 78701',
      contact_email: '<EMAIL>'
    },
    contributor: {
      id: 'contributor_custom_001',
      name: 'John Developer',
      email: '<EMAIL>',
      role: 'Senior Developer'
    },
    expectedData: {
      company: {
        name: 'Custom Tech Solutions LLC',
        address: '123 Innovation Drive, Austin, Texas 78701',
        state: 'Texas',
        city: 'Austin',
        signerName: 'Jane Smith',
        signerTitle: 'CEO',
        billingEmail: '<EMAIL>'
      },
      contributor: {
        name: 'John Developer',
        email: '<EMAIL>'
      },
      jurisdiction: {
        state: 'Texas'
      }
    }
  }
];

console.log('📋 Test Configuration:');
console.log(`   Test Scenarios: ${testScenarios.length}`);
console.log(`   Require Perfect Score: ${config.requirePerfectScore}`);
console.log(`   Output Directory: ${config.outputDir}`);
console.log(`   Save Results: ${config.saveResults}\n`);

// Test results
const testResults = [];

console.log('🧪 RUNNING 100% ACCURACY VALIDATION TESTS');
console.log('=========================================');

// Run each test scenario
for (const scenario of testScenarios) {
  console.log(`\n📝 Testing: ${scenario.name}`);
  console.log(`   Company: ${scenario.expectedData.company.name}`);
  console.log(`   State: ${scenario.expectedData.jurisdiction.state}`);
  console.log(`   Contributor: ${scenario.expectedData.contributor.name}`);
  
  try {
    // Create alliance
    const alliance = allianceCreationSystem.createAlliance(scenario.alliance);
    console.log('   ✅ Alliance created');

    // Generate agreement with alliance information for 100% accuracy
    const agreement = await agreementGenerator.generateAgreement(
      lawyerTemplate,
      scenario.project,
      {
        contributors: [scenario.contributor],
        currentUser: {
          id: scenario.contributor.id,
          email: scenario.contributor.email,
          user_metadata: {
            full_name: scenario.contributor.name
          }
        },
        fullName: scenario.contributor.name,
        // Pass alliance information directly to avoid Supabase dependency in tests
        allianceInfo: scenario.alliance
      }
    );
    
    if (agreement) {
      console.log('   ✅ Agreement generated');
      
      // Perform 100% accuracy validation
      const validationResult = accuracyValidator.validateAgreement(agreement, scenario.expectedData);
      
      console.log(`   📊 Accuracy Score: ${validationResult.accuracyScore}%`);
      console.log(`   🎯 Production Ready: ${validationResult.productionReady ? 'YES' : 'NO'}`);
      console.log(`   ❌ Critical Errors: ${validationResult.criticalErrors.length}`);
      console.log(`   ⚠️  Validation Errors: ${validationResult.validationErrors.length}`);
      
      // Log critical errors
      if (validationResult.criticalErrors.length > 0) {
        console.log('   🚨 CRITICAL ERRORS:');
        validationResult.criticalErrors.forEach(error => {
          console.log(`      - ${error}`);
        });
      }
      
      // Save agreement and validation report
      if (config.saveResults) {
        const agreementFilename = `${scenario.name.toLowerCase().replace(/\s+/g, '-')}-agreement.md`;
        const reportFilename = `${scenario.name.toLowerCase().replace(/\s+/g, '-')}-validation-report.md`;
        
        // Add validation metadata to agreement
        const agreementWithMetadata = `<!-- 100% Accuracy Validation Test
Scenario: ${scenario.name}
Company: ${scenario.expectedData.company.name}
State: ${scenario.expectedData.jurisdiction.state}
Contributor: ${scenario.expectedData.contributor.name}
Generated: ${new Date().toISOString()}
Accuracy Score: ${validationResult.accuracyScore}%
Production Ready: ${validationResult.productionReady}
Critical Errors: ${validationResult.criticalErrors.length}
-->

${agreement}`;
        
        fs.writeFileSync(path.join(config.outputDir, agreementFilename), agreementWithMetadata);
        
        // Generate and save validation report
        const validationReport = accuracyValidator.generateValidationReport(validationResult, scenario.name);
        fs.writeFileSync(path.join(config.outputDir, reportFilename), validationReport);
        
        console.log(`   💾 Saved: ${agreementFilename} & ${reportFilename}`);
      }
      
      testResults.push({
        scenario: scenario.name,
        success: true,
        validationResult,
        agreement,
        meetsPerfectStandard: validationResult.accuracyScore === 100 && validationResult.criticalErrors.length === 0
      });
      
    } else {
      console.log('   ❌ Agreement generation failed - no content returned');
      testResults.push({
        scenario: scenario.name,
        success: false,
        error: 'No content returned',
        meetsPerfectStandard: false
      });
    }
    
  } catch (error) {
    console.error(`   ❌ Error: ${error.message}`);
    testResults.push({
      scenario: scenario.name,
      success: false,
      error: error.message,
      meetsPerfectStandard: false
    });
  }
}

console.log('\n📊 100% ACCURACY VALIDATION SUMMARY');
console.log('===================================');

// Calculate summary statistics
const summary = {
  totalTests: testResults.length,
  successfulTests: testResults.filter(r => r.success).length,
  perfectAccuracyTests: testResults.filter(r => r.meetsPerfectStandard).length,
  averageAccuracy: 0,
  totalCriticalErrors: 0,
  productionReadyTests: 0
};

// Calculate averages for successful tests
const successfulTests = testResults.filter(r => r.success && r.validationResult);
if (successfulTests.length > 0) {
  summary.averageAccuracy = successfulTests.reduce((sum, r) => sum + r.validationResult.accuracyScore, 0) / successfulTests.length;
  summary.totalCriticalErrors = successfulTests.reduce((sum, r) => sum + r.validationResult.criticalErrors.length, 0);
  summary.productionReadyTests = successfulTests.filter(r => r.validationResult.productionReady).length;
}

console.log('📈 Summary Results:');
console.log(`   Total Tests: ${summary.totalTests}`);
console.log(`   Successful: ${summary.successfulTests}/${summary.totalTests}`);
console.log(`   Perfect Accuracy (100%): ${summary.perfectAccuracyTests}/${summary.totalTests}`);
console.log(`   Average Accuracy: ${summary.averageAccuracy.toFixed(1)}%`);
console.log(`   Total Critical Errors: ${summary.totalCriticalErrors}`);
console.log(`   Production Ready: ${summary.productionReadyTests}/${summary.totalTests}`);

console.log('\n📋 Individual Results:');
testResults.forEach(result => {
  const status = result.success ? 
    (result.meetsPerfectStandard ? '🎯 PERFECT' : 
     result.validationResult?.productionReady ? '✅ READY' : '⚠️ NEEDS WORK') : 
    '❌ FAILED';
  const score = result.validationResult ? `${result.validationResult.accuracyScore}%` : 'N/A';
  const errors = result.validationResult ? `${result.validationResult.criticalErrors.length} errors` : (result.error || 'Unknown error');
  
  console.log(`   ${result.scenario}: ${status} (${score}) - ${errors}`);
});

// Final assessment
const systemReady = summary.perfectAccuracyTests === summary.totalTests && summary.totalCriticalErrors === 0;

console.log('\n🎯 FINAL ASSESSMENT');
console.log('==================');

if (systemReady) {
  console.log('🎉 SYSTEM ACHIEVES 100% ACCURACY - PRODUCTION READY!');
  console.log('✅ All tests passed with perfect accuracy scores');
  console.log('✅ Zero critical errors found');
  console.log('✅ All agreements meet production standards');
} else {
  console.log('❌ SYSTEM DOES NOT MEET 100% ACCURACY REQUIREMENT');
  console.log(`⚠️  ${summary.totalTests - summary.perfectAccuracyTests} tests failed to achieve perfect accuracy`);
  console.log(`🚨 ${summary.totalCriticalErrors} critical errors must be fixed`);
  console.log('🔧 Review validation reports and fix all issues before production');
}

// Save comprehensive summary
if (config.saveResults) {
  const summaryReport = `# 100% Legal Accuracy Validation Summary
## Generated: ${new Date().toISOString()}

### Overall Assessment
**System Ready for Production**: ${systemReady ? '✅ YES' : '❌ NO'}

### Summary Statistics
- **Total Tests**: ${summary.totalTests}
- **Successful Tests**: ${summary.successfulTests}/${summary.totalTests}
- **Perfect Accuracy Tests**: ${summary.perfectAccuracyTests}/${summary.totalTests}
- **Average Accuracy**: ${summary.averageAccuracy.toFixed(1)}%
- **Total Critical Errors**: ${summary.totalCriticalErrors}
- **Production Ready Tests**: ${summary.productionReadyTests}/${summary.totalTests}

### Individual Test Results
${testResults.map(result => `
#### ${result.scenario}
- **Status**: ${result.success ? (result.meetsPerfectStandard ? '🎯 PERFECT' : result.validationResult?.productionReady ? '✅ READY' : '⚠️ NEEDS WORK') : '❌ FAILED'}
- **Accuracy Score**: ${result.validationResult ? `${result.validationResult.accuracyScore}%` : 'N/A'}
- **Critical Errors**: ${result.validationResult ? result.validationResult.criticalErrors.length : 'N/A'}
- **Production Ready**: ${result.validationResult ? (result.validationResult.productionReady ? '✅ YES' : '❌ NO') : 'N/A'}
${result.error ? `- **Error**: ${result.error}` : ''}
`).join('')}

### Conclusion
${systemReady ? 
  '🎉 The agreement generation system achieves 100% legal accuracy and is ready for production deployment.' : 
  '❌ The system requires fixes to achieve 100% accuracy before production deployment. Review individual validation reports for specific issues to address.'
}
`;

  const summaryPath = path.join(config.outputDir, `100-percent-accuracy-summary-${new Date().toISOString().split('T')[0]}.md`);
  fs.writeFileSync(summaryPath, summaryReport);
  console.log(`\n💾 Summary report saved: ${path.basename(summaryPath)}`);
}

console.log(`\n📁 All results saved to: ${config.outputDir}`);
console.log('🎯 100% Legal Accuracy Validation Complete!');

// Exit with appropriate code
process.exit(systemReady ? 0 : 1);
