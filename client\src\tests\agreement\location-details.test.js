/**
 * Location Details Tests
 * 
 * Tests that agreement generation correctly handles location details
 * including addresses, states, jurisdictions, and legal information
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { NewAgreementGenerator } from '../../utils/agreement/newAgreementGenerator.js';

// Mock Supabase
const mockSupabase = {
  from: vi.fn(() => ({
    select: vi.fn(() => ({
      eq: vi.fn(() => ({
        single: vi.fn()
      }))
    }))
  }))
};

vi.mock('../../utils/supabase/supabase.utils', () => ({
  supabase: mockSupabase
}));

describe('Location Details in Agreement Generation', () => {
  let generator;
  let mockTemplate;

  beforeEach(() => {
    generator = new NewAgreementGenerator();
    
    mockTemplate = `
# CITY OF GAMERS INC.
# CONTRIBUTOR AGREEMENT

This Contributor Agreement (this "Agreement") is effective as of [Date], by and between City of Gamers Inc., a Florida LLC with its principal place of business at 1205 43rd Street, Suite B, Orlando, Florida 32839 (the "Company") and [Contributor] (the "Contributor").

## Governing Law
This Agreement shall be governed by and construed in accordance with the laws of the State of Florida, without regard to conflict of law principles.

## Consent to Jurisdiction
The parties consent to the exclusive jurisdiction of the courts of Orange County, Florida for any disputes arising under this Agreement.

**COMPANY:**
City of Gamers Inc.
Address: 1205 43rd Street, Suite B, Orlando, Florida 32839
`;

    vi.clearAllMocks();
  });

  describe('Alliance Location Details', () => {
    it('should use alliance address and jurisdiction', async () => {
      const project = {
        id: 'test-project',
        name: 'Test Venture',
        description: 'Test description',
        alliance_id: 'alliance-123'
      };

      const mockAlliance = {
        id: 'alliance-123',
        name: 'California Tech Alliance',
        team_members: [{
          user_id: 'founder-1',
          role: 'founder',
          users: {
            id: 'founder-1',
            email: '<EMAIL>',
            user_metadata: { full_name: 'Sarah Johnson' }
          }
        }]
      };

      // Mock project with California location
      const projectWithLocation = {
        ...project,
        state: 'California',
        city: 'San Francisco',
        address: '456 Tech Street, San Francisco, CA 94105'
      };

      mockSupabase.from.mockReturnValue({
        select: vi.fn().mockReturnValue({
          eq: vi.fn().mockReturnValue({
            single: vi.fn().mockResolvedValue({
              data: mockAlliance,
              error: null
            })
          })
        })
      });

      const result = await generator.generateAgreement(mockTemplate, projectWithLocation, {
        contributors: [{ permission_level: 'Owner', display_name: 'Owner' }],
        currentUser: { email: '<EMAIL>' },
        fullName: 'Test Contributor'
      });

      // Should use California details instead of Florida
      expect(result).toContain('California Tech Alliance');
      expect(result).toContain('456 Tech Street, San Francisco, CA 94105');
      expect(result).toContain('laws of the State of California');
      expect(result).not.toContain('Florida');
      expect(result).not.toContain('Orlando');
      expect(result).not.toContain('Orange County');
    });

    it('should handle missing location data gracefully', async () => {
      const project = {
        id: 'test-project',
        name: 'Test Venture',
        description: 'Test description',
        alliance_id: 'alliance-123'
      };

      const mockAlliance = {
        id: 'alliance-123',
        name: 'Minimal Alliance',
        team_members: [{
          user_id: 'founder-1',
          role: 'founder',
          users: {
            id: 'founder-1',
            email: '<EMAIL>',
            user_metadata: { full_name: 'Min Owner' }
          }
        }]
      };

      mockSupabase.from.mockReturnValue({
        select: vi.fn().mockReturnValue({
          eq: vi.fn().mockReturnValue({
            single: vi.fn().mockResolvedValue({
              data: mockAlliance,
              error: null
            })
          })
        })
      });

      const result = await generator.generateAgreement(mockTemplate, project, {
        contributors: [{ permission_level: 'Owner', display_name: 'Owner' }],
        currentUser: { email: '<EMAIL>' },
        fullName: 'Test Contributor'
      });

      // Should use defaults when location data is missing
      expect(result).toContain('Minimal Alliance');
      expect(result).toContain('Delaware'); // Default state
      expect(result).toContain('Wilmington'); // Default city
    });
  });

  describe('Fallback Location Details', () => {
    it('should use contributor location when no alliance', async () => {
      const project = {
        id: 'test-project',
        name: 'Solo Venture',
        description: 'Solo project'
        // No alliance_id
      };

      const contributors = [{
        permission_level: 'Owner',
        display_name: 'Solo Developer',
        email: '<EMAIL>',
        state: 'Texas',
        city: 'Austin',
        address: '789 Solo Street, Austin, TX 78701'
      }];

      const result = await generator.generateAgreement(mockTemplate, project, {
        contributors,
        currentUser: { email: '<EMAIL>' },
        fullName: 'Test Contributor'
      });

      expect(result).toContain('Solo Developer');
      expect(result).toContain('Texas');
      expect(result).not.toContain('Florida');
    });
  });

  describe('Address Replacement', () => {
    it('should replace all instances of default address', async () => {
      const project = {
        id: 'test-project',
        name: 'Address Test Venture',
        description: 'Testing address replacement',
        alliance_id: 'alliance-123',
        address: '123 New Address, New City, NC 12345'
      };

      const mockAlliance = {
        id: 'alliance-123',
        name: 'Address Test Alliance',
        team_members: [{
          user_id: 'founder-1',
          role: 'founder',
          users: {
            id: 'founder-1',
            email: '<EMAIL>',
            user_metadata: { full_name: 'Address Owner' }
          }
        }]
      };

      mockSupabase.from.mockReturnValue({
        select: vi.fn().mockReturnValue({
          eq: vi.fn().mockReturnValue({
            single: vi.fn().mockResolvedValue({
              data: mockAlliance,
              error: null
            })
          })
        })
      });

      const result = await generator.generateAgreement(mockTemplate, project, {
        contributors: [{ permission_level: 'Owner', display_name: 'Owner' }],
        currentUser: { email: '<EMAIL>' },
        fullName: 'Test Contributor'
      });

      // Should not contain any of the default Orlando address
      expect(result).not.toContain('1205 43rd Street');
      expect(result).not.toContain('Orlando');
      expect(result).toContain('123 New Address, New City, NC 12345');
    });
  });

  describe('State and Jurisdiction Consistency', () => {
    it('should ensure governing law matches company state', async () => {
      const project = {
        id: 'test-project',
        name: 'Jurisdiction Test',
        description: 'Testing jurisdiction consistency',
        alliance_id: 'alliance-123',
        state: 'New York',
        city: 'New York',
        address: '100 Wall Street, New York, NY 10005'
      };

      const mockAlliance = {
        id: 'alliance-123',
        name: 'NY Alliance',
        team_members: [{
          user_id: 'founder-1',
          role: 'founder',
          users: {
            id: 'founder-1',
            email: '<EMAIL>',
            user_metadata: { full_name: 'NY Owner' }
          }
        }]
      };

      mockSupabase.from.mockReturnValue({
        select: vi.fn().mockReturnValue({
          eq: vi.fn().mockReturnValue({
            single: vi.fn().mockResolvedValue({
              data: mockAlliance,
              error: null
            })
          })
        })
      });

      const result = await generator.generateAgreement(mockTemplate, project, {
        contributors: [{ permission_level: 'Owner', display_name: 'Owner' }],
        currentUser: { email: '<EMAIL>' },
        fullName: 'Test Contributor'
      });

      // Governing law should match the state
      expect(result).toContain('laws of the State of New York');
      expect(result).toContain('New York, NY');
      expect(result).not.toContain('Florida');
    });
  });
});
