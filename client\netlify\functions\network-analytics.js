// Network Analytics API
// Handles social network analytics, growth metrics, and activity feeds
// Based on docs/design-system/systems/social-system.md

const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_ANON_KEY;

const headers = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Content-Type': 'application/json'
};

exports.handler = async (event, context) => {
  // Handle CORS preflight
  if (event.httpMethod === 'OPTIONS') {
    return { statusCode: 200, headers, body: '' };
  }

  try {
    const supabase = createClient(supabaseUrl, supabaseKey);
    
    // Get user from authorization header
    const authHeader = event.headers.authorization;
    if (!authHeader) {
      return {
        statusCode: 401,
        headers,
        body: JSON.stringify({ error: 'Authorization header required' })
      };
    }

    const token = authHeader.replace('Bearer ', '');
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    
    if (authError || !user) {
      return {
        statusCode: 401,
        headers,
        body: JSON.stringify({ error: 'Invalid authentication token' })
      };
    }

    const { httpMethod, path } = event;
    const pathParts = path.split('/').filter(part => part);
    
    switch (httpMethod) {
      case 'GET':
        return await handleGet(supabase, user, pathParts, event.queryStringParameters);
      default:
        return {
          statusCode: 405,
          headers,
          body: JSON.stringify({ error: 'Method not allowed' })
        };
    }
  } catch (error) {
    console.error('API Error:', error);
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ error: 'Internal server error', details: error.message })
    };
  }
};

// GET handlers
async function handleGet(supabase, user, pathParts, queryParams) {
  const endpoint = pathParts[pathParts.length - 1];
  
  switch (endpoint) {
    case 'analytics':
      return await getNetworkAnalytics(supabase, user.id, queryParams);
    case 'growth':
      return await getNetworkGrowth(supabase, user.id, queryParams);
    case 'feed':
      return await getActivityFeed(supabase, user.id, queryParams);
    default:
      return {
        statusCode: 404,
        headers,
        body: JSON.stringify({ error: 'Endpoint not found' })
      };
  }
}

// Get network analytics
async function getNetworkAnalytics(supabase, userId, queryParams) {
  const { period = 'current' } = queryParams || {};
  
  // Get current analytics
  const { data: currentAnalytics, error: analyticsError } = await supabase
    .from('network_analytics')
    .select('*')
    .eq('user_id', userId)
    .order('calculated_at', { ascending: false })
    .limit(1)
    .single();

  if (analyticsError && analyticsError.code !== 'PGRST116') {
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ error: 'Failed to fetch analytics', details: analyticsError.message })
    };
  }

  // Get ally connections breakdown
  const { data: allyConnections, error: alliesError } = await supabase
    .from('user_allies')
    .select(`
      id,
      status,
      connection_reason,
      connection_strength,
      accepted_at,
      ally:ally_id(display_name),
      user:user_id(display_name)
    `)
    .or(`user_id.eq.${userId},ally_id.eq.${userId}`)
    .eq('status', 'accepted');

  if (alliesError) {
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ error: 'Failed to fetch ally connections', details: alliesError.message })
    };
  }

  // Get skill endorsements summary
  const { data: endorsementsReceived, error: endorsementsError } = await supabase
    .from('skill_endorsements')
    .select('skill_name, skill_category, proficiency_level, created_at')
    .eq('endorsed_id', userId);

  if (endorsementsError) {
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ error: 'Failed to fetch endorsements', details: endorsementsError.message })
    };
  }

  // Calculate connection reasons breakdown
  const connectionReasons = {};
  allyConnections?.forEach(connection => {
    const reason = connection.connection_reason || 'networking';
    connectionReasons[reason] = (connectionReasons[reason] || 0) + 1;
  });

  // Calculate skill categories from endorsements
  const skillCategories = {};
  endorsementsReceived?.forEach(endorsement => {
    const category = endorsement.skill_category || 'technical';
    skillCategories[category] = (skillCategories[category] || 0) + 1;
  });

  // Calculate network strength distribution
  const strengthDistribution = { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 };
  allyConnections?.forEach(connection => {
    const strength = Math.min(5, Math.max(1, connection.connection_strength || 1));
    strengthDistribution[strength]++;
  });

  const analytics = {
    current_stats: currentAnalytics || {
      total_allies: allyConnections?.length || 0,
      network_score: 0,
      network_level: 'beginner',
      endorsements_received: endorsementsReceived?.length || 0,
      endorsements_given: 0,
      collaboration_success_rate: 0
    },
    connection_breakdown: {
      by_reason: connectionReasons,
      by_strength: strengthDistribution,
      total_connections: allyConnections?.length || 0
    },
    skill_breakdown: {
      by_category: skillCategories,
      total_endorsements: endorsementsReceived?.length || 0,
      unique_skills: new Set(endorsementsReceived?.map(e => e.skill_name) || []).size
    },
    recent_activity: {
      new_connections_this_month: allyConnections?.filter(c => 
        c.accepted_at && new Date(c.accepted_at) > new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
      ).length || 0,
      endorsements_this_month: endorsementsReceived?.filter(e => 
        new Date(e.created_at) > new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
      ).length || 0
    }
  };

  return {
    statusCode: 200,
    headers,
    body: JSON.stringify({ analytics })
  };
}

// Get network growth metrics
async function getNetworkGrowth(supabase, userId, queryParams) {
  const { months = 6 } = queryParams || {};
  
  // Get historical analytics
  const { data: historicalData, error } = await supabase
    .from('network_analytics')
    .select('*')
    .eq('user_id', userId)
    .gte('calculated_at', new Date(Date.now() - months * 30 * 24 * 60 * 60 * 1000).toISOString())
    .order('calculated_at', { ascending: true });

  if (error) {
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ error: 'Failed to fetch growth data', details: error.message })
    };
  }

  // Get ally connections with dates for growth calculation
  const { data: connections, error: connectionsError } = await supabase
    .from('user_allies')
    .select('accepted_at')
    .or(`user_id.eq.${userId},ally_id.eq.${userId}`)
    .eq('status', 'accepted')
    .not('accepted_at', 'is', null)
    .order('accepted_at', { ascending: true });

  if (connectionsError) {
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ error: 'Failed to fetch connection history', details: connectionsError.message })
    };
  }

  // Calculate monthly growth
  const monthlyGrowth = [];
  const now = new Date();
  
  for (let i = months - 1; i >= 0; i--) {
    const monthStart = new Date(now.getFullYear(), now.getMonth() - i, 1);
    const monthEnd = new Date(now.getFullYear(), now.getMonth() - i + 1, 0);
    
    const connectionsInMonth = connections?.filter(c => {
      const acceptedDate = new Date(c.accepted_at);
      return acceptedDate >= monthStart && acceptedDate <= monthEnd;
    }).length || 0;

    const totalConnectionsUpToMonth = connections?.filter(c => {
      const acceptedDate = new Date(c.accepted_at);
      return acceptedDate <= monthEnd;
    }).length || 0;

    monthlyGrowth.push({
      month: monthStart.toISOString().substring(0, 7), // YYYY-MM format
      new_connections: connectionsInMonth,
      total_connections: totalConnectionsUpToMonth,
      growth_rate: i === months - 1 ? 0 : 
        (connectionsInMonth / Math.max(1, totalConnectionsUpToMonth - connectionsInMonth)) * 100
    });
  }

  // Calculate trends
  const recentMonths = monthlyGrowth.slice(-3);
  const averageGrowthRate = recentMonths.reduce((sum, month) => sum + month.growth_rate, 0) / recentMonths.length;
  
  const growthMetrics = {
    monthly_growth: monthlyGrowth,
    trends: {
      average_growth_rate: Math.round(averageGrowthRate * 100) / 100,
      total_growth_period: monthlyGrowth[monthlyGrowth.length - 1]?.total_connections - 
                          (monthlyGrowth[0]?.total_connections || 0),
      most_active_month: monthlyGrowth.reduce((max, month) => 
        month.new_connections > max.new_connections ? month : max, 
        { new_connections: 0, month: null }
      )
    },
    projections: {
      next_month_estimate: Math.max(0, Math.round(
        (monthlyGrowth[monthlyGrowth.length - 1]?.total_connections || 0) * 
        (1 + averageGrowthRate / 100)
      )),
      growth_trajectory: averageGrowthRate > 5 ? 'accelerating' : 
                        averageGrowthRate > 0 ? 'steady' : 'declining'
    }
  };

  return {
    statusCode: 200,
    headers,
    body: JSON.stringify({ growth_metrics: growthMetrics })
  };
}

// Get activity feed
async function getActivityFeed(supabase, userId, queryParams) {
  const { limit = 20, offset = 0, activity_type, visibility = 'all' } = queryParams || {};
  
  // Get user's allies for filtering ally activities
  const { data: allies } = await supabase
    .from('user_allies')
    .select('user_id, ally_id')
    .or(`user_id.eq.${userId},ally_id.eq.${userId}`)
    .eq('status', 'accepted');

  const allyIds = new Set();
  allies?.forEach(connection => {
    const allyId = connection.user_id === userId ? connection.ally_id : connection.user_id;
    allyIds.add(allyId);
  });

  let query = supabase
    .from('social_activities')
    .select(`
      id,
      activity_type,
      activity_data,
      visibility,
      created_at,
      user:user_id(id, display_name),
      related_user:related_user_id(id, display_name)
    `)
    .order('created_at', { ascending: false })
    .range(offset, offset + limit - 1);

  // Filter activities based on visibility and relationships
  if (visibility === 'own') {
    query = query.eq('user_id', userId);
  } else {
    // Show own activities, public activities, and ally activities
    const userAndAllyIds = [userId, ...Array.from(allyIds)];
    query = query.or(`user_id.in.(${userAndAllyIds.join(',')}),visibility.eq.public`);
  }

  if (activity_type) {
    query = query.eq('activity_type', activity_type);
  }

  const { data, error } = await query;

  if (error) {
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ error: 'Failed to fetch activity feed', details: error.message })
    };
  }

  // Enhance activities with additional context
  const enhancedActivities = data?.map(activity => ({
    ...activity,
    is_own_activity: activity.user.id === userId,
    is_ally_activity: allyIds.has(activity.user.id),
    formatted_message: formatActivityMessage(activity)
  })) || [];

  return {
    statusCode: 200,
    headers,
    body: JSON.stringify({
      activities: enhancedActivities,
      total: enhancedActivities.length,
      hasMore: enhancedActivities.length === limit
    })
  };
}

// Format activity message for display
function formatActivityMessage(activity) {
  const { activity_type, user, related_user, activity_data } = activity;
  
  switch (activity_type) {
    case 'connection_made':
      return `${user.display_name} connected with ${activity_data?.ally_name || related_user?.display_name}`;
    case 'endorsement_received':
      return `${user.display_name} received an endorsement for ${activity_data?.skill_name} from ${activity_data?.endorser_name}`;
    case 'endorsement_given':
      return `${user.display_name} endorsed ${activity_data?.endorsed_name} for ${activity_data?.skill_name}`;
    case 'project_completed':
      return `${user.display_name} completed a project: ${activity_data?.project_name}`;
    case 'skill_verified':
      return `${user.display_name} verified their ${activity_data?.skill_name} skill`;
    case 'collaboration_started':
      return `${user.display_name} started collaborating on ${activity_data?.project_name}`;
    default:
      return `${user.display_name} had an activity`;
  }
}
