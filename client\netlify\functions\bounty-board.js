// Bounty Board API
// Backend Specialist: Public bounty posting and hunter application system
const { createClient } = require('@supabase/supabase-js');

// Supabase client
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_KEY
);

// Helper function to get user from request
const getUserFromRequest = (event) => {
  const authHeader = event.headers.authorization;
  if (!authHeader) return null;
  
  try {
    const token = authHeader.replace('Bearer ', '');
    const payload = JSON.parse(Buffer.from(token.split('.')[1], 'base64').toString());
    return payload.sub;
  } catch {
    return null;
  }
};

// Helper function to calculate bounty hunter score
const calculateHunterScore = async (userId) => {
  try {
    // Get completed bounties
    const { data: completedBounties } = await supabase
      .from('tasks')
      .select('id, difficulty_points, bounty_amount')
      .eq('assignee_id', userId)
      .eq('status', 'done')
      .eq('task_category', 'bounty');

    // Get user endorsements
    const { data: endorsements } = await supabase
      .from('user_endorsements')
      .select('endorsement_level, is_verified')
      .eq('endorsed_user_id', userId)
      .eq('status', 'active');

    // Calculate base score
    const bountyCount = completedBounties?.length || 0;
    const totalDifficulty = completedBounties?.reduce((sum, b) => sum + (b.difficulty_points || 0), 0) || 0;
    const totalEarnings = completedBounties?.reduce((sum, b) => sum + (b.bounty_amount || 0), 0) || 0;
    
    // Calculate endorsement score
    const endorsementScore = endorsements?.reduce((sum, e) => {
      const levelMultiplier = e.endorsement_level || 1;
      const verificationBonus = e.is_verified ? 1.5 : 1;
      return sum + (levelMultiplier * verificationBonus);
    }, 0) || 0;

    // Weighted score calculation
    const baseScore = (bountyCount * 10) + (totalDifficulty * 5) + (totalEarnings * 0.01);
    const finalScore = Math.min(baseScore + endorsementScore, 100);

    return {
      score: Math.round(finalScore),
      bounty_count: bountyCount,
      total_difficulty: totalDifficulty,
      total_earnings: totalEarnings,
      endorsement_score: Math.round(endorsementScore)
    };
  } catch (error) {
    console.error('Calculate hunter score error:', error);
    return { score: 0, bounty_count: 0, total_difficulty: 0, total_earnings: 0, endorsement_score: 0 };
  }
};

// Get Public Bounties
const getPublicBounties = async (event) => {
  try {
    const queryParams = new URLSearchParams(event.queryStringParameters || {});
    const category = queryParams.get('category'); // 'development', 'design', 'marketing', etc.
    const difficulty = queryParams.get('difficulty'); // 'easy', 'medium', 'hard', 'expert'
    const minBounty = queryParams.get('min_bounty');
    const maxBounty = queryParams.get('max_bounty');
    const skills = queryParams.get('skills'); // comma-separated skill names
    const sortBy = queryParams.get('sort_by') || 'created_at'; // 'created_at', 'bounty_amount', 'deadline'
    const sortOrder = queryParams.get('sort_order') || 'desc';
    const limit = parseInt(queryParams.get('limit') || '20');

    // Build query for public bounties
    let query = supabase
      .from('tasks')
      .select(`
        id,
        title,
        description,
        task_category,
        bounty_amount,
        bounty_currency,
        difficulty_level,
        difficulty_points,
        estimated_hours,
        required_skills,
        deadline,
        is_public,
        status,
        created_at,
        updated_at,
        projects(
          id,
          name,
          title,
          alliance_id,
          teams(
            id,
            name,
            alliance_type,
            industry
          )
        ),
        creator:users!tasks_created_by_fkey(
          id,
          display_name,
          avatar_url,
          is_premium
        ),
        applications:bounty_applications(
          id,
          status,
          applied_at
        )
      `)
      .eq('task_category', 'bounty')
      .eq('is_public', true)
      .in('status', ['open', 'todo', 'in_progress']);

    // Apply filters
    if (category) {
      query = query.eq('task_type', category);
    }

    if (difficulty) {
      query = query.eq('difficulty_level', difficulty);
    }

    if (minBounty) {
      query = query.gte('bounty_amount', parseFloat(minBounty));
    }

    if (maxBounty) {
      query = query.lte('bounty_amount', parseFloat(maxBounty));
    }

    if (skills) {
      const skillList = skills.split(',').map(s => s.trim());
      query = query.overlaps('required_skills', skillList);
    }

    // Apply sorting
    const validSortFields = ['created_at', 'bounty_amount', 'deadline', 'difficulty_points'];
    const sortField = validSortFields.includes(sortBy) ? sortBy : 'created_at';
    const ascending = sortOrder === 'asc';

    const { data: bounties, error: bountiesError } = await query
      .order(sortField, { ascending })
      .limit(limit);

    if (bountiesError) {
      throw new Error(`Failed to fetch bounties: ${bountiesError.message}`);
    }

    // Enhance bounties with additional data
    const enhancedBounties = bounties?.map(bounty => {
      const applicationCount = bounty.applications?.length || 0;
      const isUrgent = bounty.deadline && new Date(bounty.deadline) <= new Date(Date.now() + 7 * 24 * 60 * 60 * 1000);
      
      return {
        id: bounty.id,
        title: bounty.title,
        description: bounty.description,
        category: bounty.task_category,
        bounty_amount: bounty.bounty_amount || 0,
        bounty_currency: bounty.bounty_currency || 'USD',
        difficulty_level: bounty.difficulty_level,
        difficulty_points: bounty.difficulty_points,
        estimated_hours: bounty.estimated_hours,
        required_skills: bounty.required_skills || [],
        deadline: bounty.deadline,
        is_urgent: isUrgent,
        status: bounty.status,
        application_count: applicationCount,
        venture: bounty.projects ? {
          id: bounty.projects.id,
          name: bounty.projects.name || bounty.projects.title,
          alliance: bounty.projects.teams ? {
            id: bounty.projects.teams.id,
            name: bounty.projects.teams.name,
            type: bounty.projects.teams.alliance_type,
            industry: bounty.projects.teams.industry
          } : null
        } : null,
        poster: bounty.creator ? {
          id: bounty.creator.id,
          name: bounty.creator.display_name,
          avatar_url: bounty.creator.avatar_url,
          is_premium: bounty.creator.is_premium
        } : null,
        created_at: bounty.created_at,
        updated_at: bounty.updated_at
      };
    }) || [];

    // Calculate bounty board statistics
    const stats = {
      total_bounties: enhancedBounties.length,
      total_value: enhancedBounties.reduce((sum, b) => sum + (b.bounty_amount || 0), 0),
      average_bounty: enhancedBounties.length > 0 ? 
        enhancedBounties.reduce((sum, b) => sum + (b.bounty_amount || 0), 0) / enhancedBounties.length : 0,
      urgent_bounties: enhancedBounties.filter(b => b.is_urgent).length,
      categories: [...new Set(enhancedBounties.map(b => b.category))],
      difficulty_distribution: enhancedBounties.reduce((acc, b) => {
        acc[b.difficulty_level] = (acc[b.difficulty_level] || 0) + 1;
        return acc;
      }, {})
    };

    return {
      statusCode: 200,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        bounties: enhancedBounties,
        statistics: stats,
        filters: {
          category,
          difficulty,
          min_bounty: minBounty,
          max_bounty: maxBounty,
          skills: skills?.split(',') || [],
          sort_by: sortField,
          sort_order: sortOrder
        }
      })
    };

  } catch (error) {
    console.error('Get public bounties error:', error);
    return {
      statusCode: 500,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ error: 'Failed to fetch bounties' })
    };
  }
};

// Get Bounty Details
const getBountyDetails = async (event) => {
  try {
    const bountyId = event.path.split('/').pop();
    const userId = getUserFromRequest(event);

    // Get bounty with full details
    const { data: bounty, error: bountyError } = await supabase
      .from('tasks')
      .select(`
        id,
        title,
        description,
        task_category,
        task_type,
        bounty_amount,
        bounty_currency,
        difficulty_level,
        difficulty_points,
        estimated_hours,
        required_skills,
        deadline,
        is_public,
        status,
        created_at,
        updated_at,
        created_by,
        assignee_id,
        projects(
          id,
          name,
          title,
          description,
          alliance_id,
          teams(
            id,
            name,
            alliance_type,
            industry,
            business_model
          )
        ),
        creator:users!tasks_created_by_fkey(
          id,
          display_name,
          avatar_url,
          is_premium
        ),
        assignee:users!tasks_assignee_id_fkey(
          id,
          display_name,
          avatar_url
        ),
        applications:bounty_applications(
          id,
          applicant_id,
          application_message,
          proposed_timeline,
          proposed_approach,
          status,
          applied_at,
          reviewed_at,
          applicant:users!bounty_applications_applicant_id_fkey(
            id,
            display_name,
            avatar_url,
            is_premium
          )
        )
      `)
      .eq('id', bountyId)
      .eq('task_category', 'bounty')
      .single();

    if (bountyError) {
      return {
        statusCode: 404,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Bounty not found' })
      };
    }

    // Check if bounty is public or user has access
    const hasAccess = bounty.is_public || 
                     bounty.created_by === userId ||
                     bounty.assignee_id === userId ||
                     bounty.applications?.some(app => app.applicant_id === userId);

    if (!hasAccess) {
      return {
        statusCode: 403,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Access denied' })
      };
    }

    // Get user's application if exists
    const userApplication = userId ? 
      bounty.applications?.find(app => app.applicant_id === userId) : null;

    // Calculate application statistics
    const applicationStats = {
      total_applications: bounty.applications?.length || 0,
      pending_applications: bounty.applications?.filter(app => app.status === 'pending').length || 0,
      accepted_applications: bounty.applications?.filter(app => app.status === 'accepted').length || 0,
      rejected_applications: bounty.applications?.filter(app => app.status === 'rejected').length || 0
    };

    const response = {
      id: bounty.id,
      title: bounty.title,
      description: bounty.description,
      category: bounty.task_category,
      type: bounty.task_type,
      bounty_amount: bounty.bounty_amount || 0,
      bounty_currency: bounty.bounty_currency || 'USD',
      difficulty_level: bounty.difficulty_level,
      difficulty_points: bounty.difficulty_points,
      estimated_hours: bounty.estimated_hours,
      required_skills: bounty.required_skills || [],
      deadline: bounty.deadline,
      is_urgent: bounty.deadline && new Date(bounty.deadline) <= new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
      status: bounty.status,
      is_assigned: !!bounty.assignee_id,
      venture: bounty.projects ? {
        id: bounty.projects.id,
        name: bounty.projects.name || bounty.projects.title,
        description: bounty.projects.description,
        alliance: bounty.projects.teams ? {
          id: bounty.projects.teams.id,
          name: bounty.projects.teams.name,
          type: bounty.projects.teams.alliance_type,
          industry: bounty.projects.teams.industry,
          business_model: bounty.projects.teams.business_model
        } : null
      } : null,
      poster: bounty.creator ? {
        id: bounty.creator.id,
        name: bounty.creator.display_name,
        avatar_url: bounty.creator.avatar_url,
        is_premium: bounty.creator.is_premium
      } : null,
      assignee: bounty.assignee,
      applications: bounty.applications?.map(app => ({
        id: app.id,
        applicant: app.applicant,
        application_message: app.application_message,
        proposed_timeline: app.proposed_timeline,
        proposed_approach: app.proposed_approach,
        status: app.status,
        applied_at: app.applied_at,
        reviewed_at: app.reviewed_at
      })) || [],
      user_application: userApplication,
      application_statistics: applicationStats,
      created_at: bounty.created_at,
      updated_at: bounty.updated_at,
      can_apply: userId && !userApplication && !bounty.assignee_id && bounty.status === 'open',
      can_manage: userId && (bounty.created_by === userId)
    };

    return {
      statusCode: 200,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(response)
    };

  } catch (error) {
    console.error('Get bounty details error:', error);
    return {
      statusCode: 500,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ error: 'Failed to fetch bounty details' })
    };
  }
};

// Apply for Bounty
const applyForBounty = async (event) => {
  try {
    const userId = getUserFromRequest(event);
    if (!userId) {
      return {
        statusCode: 401,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    const bountyId = event.path.split('/')[2]; // /bounty-board/{id}/apply
    const data = JSON.parse(event.body);

    // Validate required fields
    if (!data.application_message) {
      return {
        statusCode: 400,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Application message is required' })
      };
    }

    // Check if bounty exists and is available
    const { data: bounty, error: bountyError } = await supabase
      .from('tasks')
      .select('id, status, assignee_id, is_public, task_category')
      .eq('id', bountyId)
      .eq('task_category', 'bounty')
      .single();

    if (bountyError || !bounty) {
      return {
        statusCode: 404,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Bounty not found' })
      };
    }

    if (!bounty.is_public) {
      return {
        statusCode: 403,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Bounty is not public' })
      };
    }

    if (bounty.assignee_id) {
      return {
        statusCode: 400,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Bounty is already assigned' })
      };
    }

    if (bounty.status !== 'open') {
      return {
        statusCode: 400,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Bounty is not open for applications' })
      };
    }

    // Check if user already applied
    const { data: existingApplication } = await supabase
      .from('bounty_applications')
      .select('id')
      .eq('task_id', bountyId)
      .eq('applicant_id', userId)
      .single();

    if (existingApplication) {
      return {
        statusCode: 400,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'You have already applied for this bounty' })
      };
    }

    // Create application
    const applicationData = {
      task_id: bountyId,
      applicant_id: userId,
      application_message: data.application_message,
      proposed_timeline: data.proposed_timeline || null,
      proposed_approach: data.proposed_approach || null,
      status: 'pending'
    };

    const { data: application, error: applicationError } = await supabase
      .from('bounty_applications')
      .insert([applicationData])
      .select(`
        id,
        application_message,
        proposed_timeline,
        proposed_approach,
        status,
        applied_at,
        applicant:users!bounty_applications_applicant_id_fkey(
          id,
          display_name,
          avatar_url
        )
      `)
      .single();

    if (applicationError) {
      throw new Error(`Failed to create application: ${applicationError.message}`);
    }

    return {
      statusCode: 201,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ application })
    };

  } catch (error) {
    console.error('Apply for bounty error:', error);
    return {
      statusCode: 500,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ error: 'Failed to apply for bounty' })
    };
  }
};

// Route requests to appropriate handlers
exports.handler = async (event, context) => {
  // Add CORS headers
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS'
  };

  // Handle preflight requests
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers,
      body: ''
    };
  }

  const path = event.path.replace('/.netlify/functions/bounty-board', '');

  try {
    let response;

    if (event.httpMethod === 'GET') {
      if (path === '' || path === '/') {
        response = await getPublicBounties(event);
      } else if (path.includes('/') && !path.includes('/apply')) {
        response = await getBountyDetails(event);
      } else {
        response = {
          statusCode: 404,
          body: JSON.stringify({ error: 'Endpoint not found' })
        };
      }
    } else if (event.httpMethod === 'POST') {
      if (path.includes('/apply')) {
        response = await applyForBounty(event);
      } else {
        response = {
          statusCode: 404,
          body: JSON.stringify({ error: 'Endpoint not found' })
        };
      }
    } else {
      response = {
        statusCode: 405,
        body: JSON.stringify({ error: 'Method not allowed' })
      };
    }

    // Add CORS headers to response
    response.headers = { ...response.headers, ...headers };
    return response;

  } catch (error) {
    console.error('Bounty Board API error:', error);
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ error: 'Internal server error' })
    };
  }
};
