/**
 * Copyright & Patent Integration System
 * 
 * Integration with copyright and patent systems for proper IP registration and protection:
 * - Copyright registration and management
 * - Patent application and tracking
 * - Trademark registration and monitoring
 * - IP portfolio management
 * - Prior art search and analysis
 * - IP valuation and assessment
 */

import { ipRightsFramework } from './ipRightsFramework.js';
import { rightsManagementSystem } from './rightsManagementSystem.js';

// ============================================================================
// IP REGISTRATION TYPES AND STATUS
// ============================================================================

export const REGISTRATION_TYPES = {
  COPYRIGHT: 'copyright',
  PATENT: 'patent',
  TRADEMARK: 'trademark',
  DESIGN_PATENT: 'design_patent',
  UTILITY_MODEL: 'utility_model',
  TRADE_SECRET: 'trade_secret'
};

export const REGISTRATION_STATUS = {
  UNREGISTERED: 'unregistered',
  PENDING: 'pending',
  REGISTERED: 'registered',
  REJECTED: 'rejected',
  EXPIRED: 'expired',
  ABANDONED: 'abandoned',
  RENEWED: 'renewed'
};

export const PATENT_TYPES = {
  UTILITY: 'utility',
  DESIGN: 'design',
  PLANT: 'plant',
  PROVISIONAL: 'provisional',
  CONTINUATION: 'continuation',
  DIVISIONAL: 'divisional'
};

export const COPYRIGHT_CATEGORIES = {
  LITERARY_WORK: 'literary_work',
  MUSICAL_WORK: 'musical_work',
  DRAMATIC_WORK: 'dramatic_work',
  CHOREOGRAPHIC_WORK: 'choreographic_work',
  PICTORIAL_WORK: 'pictorial_work',
  AUDIOVISUAL_WORK: 'audiovisual_work',
  SOUND_RECORDING: 'sound_recording',
  ARCHITECTURAL_WORK: 'architectural_work',
  SOFTWARE: 'software'
};

export const TRADEMARK_TYPES = {
  WORD_MARK: 'word_mark',
  DESIGN_MARK: 'design_mark',
  COMPOSITE_MARK: 'composite_mark',
  SERVICE_MARK: 'service_mark',
  COLLECTIVE_MARK: 'collective_mark',
  CERTIFICATION_MARK: 'certification_mark'
};

// ============================================================================
// COPYRIGHT & PATENT INTEGRATION CLASS
// ============================================================================

export class CopyrightPatentIntegration {
  constructor() {
    this.registrations = new Map();
    this.applications = new Map();
    this.portfolios = new Map();
    this.priorArtSearches = new Map();
    this.valuations = new Map();
    this.renewals = new Map();
  }

  /**
   * Register copyright for creative work
   */
  registerCopyright(copyrightDefinition) {
    const copyrightRegistration = {
      id: copyrightDefinition.id || this.generateRegistrationId(),
      
      // Work information
      title: copyrightDefinition.title,
      alternativeTitles: copyrightDefinition.alternativeTitles || [],
      category: copyrightDefinition.category || COPYRIGHT_CATEGORIES.LITERARY_WORK,
      description: copyrightDefinition.description,
      
      // Author information
      authors: copyrightDefinition.authors || [],
      claimants: copyrightDefinition.claimants || [],
      workForHire: copyrightDefinition.workForHire || false,
      
      // Creation details
      creationDate: copyrightDefinition.creationDate,
      publicationDate: copyrightDefinition.publicationDate,
      firstPublication: copyrightDefinition.firstPublication || {},
      
      // Registration details
      registrationType: REGISTRATION_TYPES.COPYRIGHT,
      registrationNumber: copyrightDefinition.registrationNumber,
      applicationDate: copyrightDefinition.applicationDate || new Date().toISOString(),
      registrationDate: copyrightDefinition.registrationDate,
      status: copyrightDefinition.status || REGISTRATION_STATUS.PENDING,
      
      // Copyright office information
      copyrightOffice: copyrightDefinition.copyrightOffice || 'USPTO',
      jurisdiction: copyrightDefinition.jurisdiction || 'US',
      
      // Deposit materials
      depositMaterials: copyrightDefinition.depositMaterials || [],
      depositDate: copyrightDefinition.depositDate,
      
      // Rights information
      rightsOwner: copyrightDefinition.rightsOwner,
      transferHistory: copyrightDefinition.transferHistory || [],
      
      // Duration and renewal
      protectionDuration: this.calculateCopyrightDuration(copyrightDefinition),
      renewalRequired: copyrightDefinition.renewalRequired || false,
      renewalDates: copyrightDefinition.renewalDates || [],
      
      // International registration
      internationalRegistrations: copyrightDefinition.internationalRegistrations || [],
      berneConvention: copyrightDefinition.berneConvention !== false,
      
      // Enforcement
      enforcementHistory: [],
      infringementClaims: [],
      
      // Metadata
      createdAt: new Date().toISOString(),
      lastUpdated: new Date().toISOString()
    };

    this.registrations.set(copyrightRegistration.id, copyrightRegistration);
    
    // Create IP asset record
    this.createIPAssetRecord(copyrightRegistration);
    
    return copyrightRegistration;
  }

  /**
   * File patent application
   */
  filePatentApplication(patentDefinition) {
    const patentApplication = {
      id: patentDefinition.id || this.generateApplicationId(),
      
      // Invention information
      title: patentDefinition.title,
      inventionDescription: patentDefinition.inventionDescription,
      technicalField: patentDefinition.technicalField,
      backgroundArt: patentDefinition.backgroundArt,
      
      // Patent type and classification
      patentType: patentDefinition.patentType || PATENT_TYPES.UTILITY,
      classification: patentDefinition.classification || {},
      ipcClassification: patentDefinition.ipcClassification || [],
      
      // Inventor information
      inventors: patentDefinition.inventors || [],
      applicant: patentDefinition.applicant,
      assignee: patentDefinition.assignee,
      
      // Application details
      applicationType: patentDefinition.applicationType || 'original',
      applicationNumber: patentDefinition.applicationNumber,
      filingDate: patentDefinition.filingDate || new Date().toISOString(),
      priorityDate: patentDefinition.priorityDate,
      priorityClaims: patentDefinition.priorityClaims || [],
      
      // Patent office information
      patentOffice: patentDefinition.patentOffice || 'USPTO',
      jurisdiction: patentDefinition.jurisdiction || 'US',
      
      // Claims and specifications
      claims: patentDefinition.claims || [],
      specifications: patentDefinition.specifications || {},
      drawings: patentDefinition.drawings || [],
      abstract: patentDefinition.abstract,
      
      // Prior art and search
      priorArtReferences: patentDefinition.priorArtReferences || [],
      priorArtSearchConducted: patentDefinition.priorArtSearchConducted || false,
      noveltyAssessment: patentDefinition.noveltyAssessment || {},
      
      // Examination process
      examinationStatus: patentDefinition.examinationStatus || 'pending',
      examinerActions: patentDefinition.examinerActions || [],
      applicantResponses: patentDefinition.applicantResponses || [],
      
      // Publication and grant
      publicationDate: patentDefinition.publicationDate,
      publicationNumber: patentDefinition.publicationNumber,
      grantDate: patentDefinition.grantDate,
      patentNumber: patentDefinition.patentNumber,
      
      // Status and timeline
      status: patentDefinition.status || REGISTRATION_STATUS.PENDING,
      statusHistory: patentDefinition.statusHistory || [],
      
      // Fees and costs
      filingFees: patentDefinition.filingFees || {},
      maintenanceFees: patentDefinition.maintenanceFees || {},
      totalCosts: patentDefinition.totalCosts || 0,
      
      // International filing
      pctApplication: patentDefinition.pctApplication || false,
      nationalPhaseEntries: patentDefinition.nationalPhaseEntries || [],
      
      // Commercial information
      commercialValue: patentDefinition.commercialValue || {},
      licensingOpportunities: patentDefinition.licensingOpportunities || [],
      
      // Metadata
      createdAt: new Date().toISOString(),
      lastUpdated: new Date().toISOString()
    };

    this.applications.set(patentApplication.id, patentApplication);
    
    // Schedule maintenance fee reminders
    this.scheduleMaintenanceFees(patentApplication);
    
    return patentApplication;
  }

  /**
   * Register trademark
   */
  registerTrademark(trademarkDefinition) {
    const trademarkRegistration = {
      id: trademarkDefinition.id || this.generateRegistrationId(),
      
      // Mark information
      markText: trademarkDefinition.markText,
      markType: trademarkDefinition.markType || TRADEMARK_TYPES.WORD_MARK,
      markDescription: trademarkDefinition.markDescription,
      markImage: trademarkDefinition.markImage,
      
      // Applicant information
      applicant: trademarkDefinition.applicant,
      owner: trademarkDefinition.owner,
      
      // Goods and services
      goodsAndServices: trademarkDefinition.goodsAndServices || [],
      niceClassification: trademarkDefinition.niceClassification || [],
      
      // Application details
      applicationNumber: trademarkDefinition.applicationNumber,
      filingDate: trademarkDefinition.filingDate || new Date().toISOString(),
      filingBasis: trademarkDefinition.filingBasis || 'intent_to_use',
      
      // Use information
      firstUseDate: trademarkDefinition.firstUseDate,
      firstUseInCommerce: trademarkDefinition.firstUseInCommerce,
      useEvidence: trademarkDefinition.useEvidence || [],
      
      // Registration details
      registrationNumber: trademarkDefinition.registrationNumber,
      registrationDate: trademarkDefinition.registrationDate,
      status: trademarkDefinition.status || REGISTRATION_STATUS.PENDING,
      
      // Trademark office information
      trademarkOffice: trademarkDefinition.trademarkOffice || 'USPTO',
      jurisdiction: trademarkDefinition.jurisdiction || 'US',
      
      // Examination process
      examinationStatus: trademarkDefinition.examinationStatus || 'pending',
      officeActions: trademarkDefinition.officeActions || [],
      applicantResponses: trademarkDefinition.applicantResponses || [],
      
      // Publication and opposition
      publicationDate: trademarkDefinition.publicationDate,
      oppositionPeriod: trademarkDefinition.oppositionPeriod || {},
      oppositions: trademarkDefinition.oppositions || [],
      
      // Renewal and maintenance
      renewalDates: trademarkDefinition.renewalDates || [],
      renewalStatus: trademarkDefinition.renewalStatus || 'current',
      
      // International registration
      madridProtocol: trademarkDefinition.madridProtocol || false,
      internationalRegistrations: trademarkDefinition.internationalRegistrations || [],
      
      // Enforcement
      enforcementActions: trademarkDefinition.enforcementActions || [],
      infringementClaims: trademarkDefinition.infringementClaims || [],
      
      // Commercial information
      brandValue: trademarkDefinition.brandValue || {},
      licensingAgreements: trademarkDefinition.licensingAgreements || [],
      
      // Metadata
      createdAt: new Date().toISOString(),
      lastUpdated: new Date().toISOString()
    };

    this.registrations.set(trademarkRegistration.id, trademarkRegistration);
    
    // Schedule renewal reminders
    this.scheduleTrademarkRenewals(trademarkRegistration);
    
    return trademarkRegistration;
  }

  /**
   * Conduct prior art search
   */
  conductPriorArtSearch(searchDefinition) {
    const priorArtSearch = {
      id: searchDefinition.id || this.generateSearchId(),
      
      // Search parameters
      inventionDescription: searchDefinition.inventionDescription,
      keywords: searchDefinition.keywords || [],
      classifications: searchDefinition.classifications || [],
      searchStrategy: searchDefinition.searchStrategy || {},
      
      // Search scope
      databases: searchDefinition.databases || ['USPTO', 'EPO', 'WIPO', 'Google Patents'],
      dateRange: searchDefinition.dateRange || {},
      jurisdictions: searchDefinition.jurisdictions || ['US', 'EP', 'WO'],
      languages: searchDefinition.languages || ['EN'],
      
      // Search execution
      searchDate: new Date().toISOString(),
      searchDuration: searchDefinition.searchDuration,
      searchConductedBy: searchDefinition.searchConductedBy,
      
      // Results
      totalResults: 0,
      relevantResults: [],
      keyReferences: [],
      
      // Analysis
      noveltyAssessment: {
        novel: null,
        obviousness: null,
        patentability: null,
        recommendations: []
      },
      
      // Documentation
      searchReport: searchDefinition.searchReport || {},
      searchHistory: searchDefinition.searchHistory || [],
      
      // Status
      status: 'in_progress',
      completedAt: null
    };

    this.priorArtSearches.set(priorArtSearch.id, priorArtSearch);
    
    // Execute search
    this.executePriorArtSearch(priorArtSearch);
    
    return priorArtSearch;
  }

  /**
   * Create IP portfolio for organization
   */
  createIPPortfolio(portfolioDefinition) {
    const portfolio = {
      id: portfolioDefinition.id || this.generatePortfolioId(),
      
      // Portfolio information
      name: portfolioDefinition.name,
      description: portfolioDefinition.description,
      owner: portfolioDefinition.owner,
      
      // IP assets
      copyrights: portfolioDefinition.copyrights || [],
      patents: portfolioDefinition.patents || [],
      trademarks: portfolioDefinition.trademarks || [],
      tradeSecrets: portfolioDefinition.tradeSecrets || [],
      
      // Portfolio metrics
      totalAssets: 0,
      totalValue: 0,
      portfolioStrength: 0,
      
      // Management
      managers: portfolioDefinition.managers || [],
      strategy: portfolioDefinition.strategy || {},
      
      // Valuation
      lastValuation: portfolioDefinition.lastValuation,
      valuationMethod: portfolioDefinition.valuationMethod,
      valuationHistory: portfolioDefinition.valuationHistory || [],
      
      // Licensing and monetization
      licensingStrategy: portfolioDefinition.licensingStrategy || {},
      activelicenses: portfolioDefinition.activelicenses || [],
      revenueGenerated: portfolioDefinition.revenueGenerated || 0,
      
      // Risk management
      riskAssessment: portfolioDefinition.riskAssessment || {},
      maintenanceSchedule: portfolioDefinition.maintenanceSchedule || {},
      
      // Metadata
      createdAt: new Date().toISOString(),
      lastUpdated: new Date().toISOString()
    };

    // Calculate portfolio metrics
    this.calculatePortfolioMetrics(portfolio);
    
    this.portfolios.set(portfolio.id, portfolio);
    return portfolio;
  }

  /**
   * Conduct IP valuation
   */
  conductIPValuation(valuationDefinition) {
    const valuation = {
      id: valuationDefinition.id || this.generateValuationId(),
      
      // Asset information
      assetId: valuationDefinition.assetId,
      assetType: valuationDefinition.assetType,
      assetDescription: valuationDefinition.assetDescription,
      
      // Valuation parameters
      valuationDate: new Date().toISOString(),
      valuationPurpose: valuationDefinition.valuationPurpose || 'licensing',
      valuationMethod: valuationDefinition.valuationMethod || 'income_approach',
      
      // Valuation approaches
      costApproach: this.calculateCostApproach(valuationDefinition),
      marketApproach: this.calculateMarketApproach(valuationDefinition),
      incomeApproach: this.calculateIncomeApproach(valuationDefinition),
      
      // Risk factors
      riskFactors: valuationDefinition.riskFactors || [],
      riskAdjustment: valuationDefinition.riskAdjustment || 0,
      
      // Market analysis
      marketSize: valuationDefinition.marketSize || {},
      competitiveAnalysis: valuationDefinition.competitiveAnalysis || {},
      industryTrends: valuationDefinition.industryTrends || {},
      
      // Financial projections
      revenueProjections: valuationDefinition.revenueProjections || [],
      discountRate: valuationDefinition.discountRate || 0.15,
      terminalValue: valuationDefinition.terminalValue || 0,
      
      // Final valuation
      estimatedValue: 0,
      valuationRange: { low: 0, high: 0 },
      confidenceLevel: valuationDefinition.confidenceLevel || 'medium',
      
      // Documentation
      valuationReport: valuationDefinition.valuationReport || {},
      assumptions: valuationDefinition.assumptions || [],
      limitations: valuationDefinition.limitations || [],
      
      // Validation
      validatedBy: valuationDefinition.validatedBy,
      validationDate: valuationDefinition.validationDate,
      
      // Status
      status: 'completed',
      createdAt: new Date().toISOString()
    };

    // Calculate final valuation
    this.calculateFinalValuation(valuation);
    
    this.valuations.set(valuation.id, valuation);
    return valuation;
  }

  // ============================================================================
  // HELPER METHODS
  // ============================================================================

  /**
   * Calculate copyright protection duration
   */
  calculateCopyrightDuration(copyrightDefinition) {
    const creationYear = new Date(copyrightDefinition.creationDate).getFullYear();
    const isWorkForHire = copyrightDefinition.workForHire;
    
    if (isWorkForHire) {
      // Work for hire: 95 years from publication or 120 years from creation
      return {
        type: 'work_for_hire',
        duration: '95_years_from_publication_or_120_from_creation',
        expirationYear: creationYear + 120
      };
    } else {
      // Individual author: life + 70 years
      return {
        type: 'individual_author',
        duration: 'life_plus_70',
        expirationYear: null // Depends on author's death
      };
    }
  }

  /**
   * Create IP asset record from registration
   */
  createIPAssetRecord(registration) {
    const assetDefinition = {
      title: registration.title,
      description: registration.description,
      type: registration.registrationType,
      createdBy: registration.rightsOwner,
      registrationStatus: registration.status,
      registrationNumber: registration.registrationNumber,
      registrationDate: registration.registrationDate
    };

    return ipRightsFramework.defineIPAsset(assetDefinition);
  }

  /**
   * Schedule maintenance fees for patents
   */
  scheduleMaintenanceFees(patentApplication) {
    if (patentApplication.patentType === PATENT_TYPES.UTILITY) {
      const maintenanceDates = [
        { year: 3.5, description: 'First maintenance fee' },
        { year: 7.5, description: 'Second maintenance fee' },
        { year: 11.5, description: 'Third maintenance fee' }
      ];

      maintenanceDates.forEach(fee => {
        const dueDate = new Date(patentApplication.grantDate);
        dueDate.setFullYear(dueDate.getFullYear() + fee.year);
        
        this.renewals.set(this.generateRenewalId(), {
          assetId: patentApplication.id,
          assetType: 'patent',
          feeType: 'maintenance',
          description: fee.description,
          dueDate: dueDate.toISOString(),
          status: 'scheduled'
        });
      });
    }
  }

  /**
   * Schedule trademark renewals
   */
  scheduleTrademarkRenewals(trademarkRegistration) {
    // US trademarks: renewal between 5th-6th year, then every 10 years
    const registrationDate = new Date(trademarkRegistration.registrationDate);
    
    // First renewal (5-6 years)
    const firstRenewal = new Date(registrationDate);
    firstRenewal.setFullYear(firstRenewal.getFullYear() + 5);
    
    this.renewals.set(this.generateRenewalId(), {
      assetId: trademarkRegistration.id,
      assetType: 'trademark',
      feeType: 'renewal',
      description: 'First renewal (5-6 years)',
      dueDate: firstRenewal.toISOString(),
      status: 'scheduled'
    });
  }

  /**
   * Execute prior art search
   */
  executePriorArtSearch(priorArtSearch) {
    // Mock implementation - in production, this would integrate with patent databases
    setTimeout(() => {
      priorArtSearch.status = 'completed';
      priorArtSearch.completedAt = new Date().toISOString();
      priorArtSearch.totalResults = Math.floor(Math.random() * 100) + 10;
      priorArtSearch.relevantResults = this.generateMockPriorArt(priorArtSearch.totalResults);
      priorArtSearch.noveltyAssessment = this.assessNovelty(priorArtSearch.relevantResults);
    }, 1000);
  }

  /**
   * Generate mock prior art results
   */
  generateMockPriorArt(count) {
    const results = [];
    for (let i = 0; i < Math.min(count, 10); i++) {
      results.push({
        id: `prior_art_${i}`,
        title: `Related Patent ${i + 1}`,
        publicationNumber: `US${Math.floor(Math.random() * 10000000)}`,
        publicationDate: new Date(Date.now() - Math.random() * 10 * 365 * 24 * 60 * 60 * 1000).toISOString(),
        relevanceScore: Math.random(),
        abstract: `Abstract for related patent ${i + 1}...`
      });
    }
    return results;
  }

  /**
   * Assess novelty based on prior art
   */
  assessNovelty(priorArtResults) {
    const highRelevanceCount = priorArtResults.filter(result => result.relevanceScore > 0.8).length;
    
    return {
      novel: highRelevanceCount === 0,
      obviousness: highRelevanceCount > 2 ? 'likely_obvious' : 'non_obvious',
      patentability: highRelevanceCount === 0 ? 'likely_patentable' : 'questionable',
      recommendations: highRelevanceCount > 0 ? 
        ['Consider claim amendments', 'Conduct detailed comparison'] : 
        ['Proceed with application']
    };
  }

  /**
   * Calculate portfolio metrics
   */
  calculatePortfolioMetrics(portfolio) {
    portfolio.totalAssets = portfolio.copyrights.length + portfolio.patents.length + 
                           portfolio.trademarks.length + portfolio.tradeSecrets.length;
    
    // Mock portfolio strength calculation
    portfolio.portfolioStrength = Math.min(100, portfolio.totalAssets * 10 + Math.random() * 20);
  }

  /**
   * Valuation calculation methods
   */
  calculateCostApproach(valuationDefinition) {
    const developmentCosts = valuationDefinition.developmentCosts || 0;
    const replacementCost = valuationDefinition.replacementCost || developmentCosts * 1.2;
    
    return {
      developmentCosts,
      replacementCost,
      estimatedValue: replacementCost
    };
  }

  calculateMarketApproach(valuationDefinition) {
    const comparableTransactions = valuationDefinition.comparableTransactions || [];
    const averageMultiple = comparableTransactions.length > 0 ? 
      comparableTransactions.reduce((sum, t) => sum + t.multiple, 0) / comparableTransactions.length : 5;
    
    return {
      comparableTransactions,
      averageMultiple,
      estimatedValue: (valuationDefinition.annualRevenue || 0) * averageMultiple
    };
  }

  calculateIncomeApproach(valuationDefinition) {
    const projectedCashFlows = valuationDefinition.revenueProjections || [];
    const discountRate = valuationDefinition.discountRate || 0.15;
    
    let npv = 0;
    projectedCashFlows.forEach((cashFlow, year) => {
      npv += cashFlow / Math.pow(1 + discountRate, year + 1);
    });
    
    return {
      projectedCashFlows,
      discountRate,
      netPresentValue: npv,
      estimatedValue: npv
    };
  }

  calculateFinalValuation(valuation) {
    const approaches = [
      valuation.costApproach.estimatedValue,
      valuation.marketApproach.estimatedValue,
      valuation.incomeApproach.estimatedValue
    ].filter(value => value > 0);

    if (approaches.length > 0) {
      valuation.estimatedValue = approaches.reduce((sum, value) => sum + value, 0) / approaches.length;
      valuation.valuationRange = {
        low: Math.min(...approaches) * 0.8,
        high: Math.max(...approaches) * 1.2
      };
    }
  }

  /**
   * Generate unique IDs
   */
  generateRegistrationId() {
    return 'reg_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  generateApplicationId() {
    return 'app_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  generateSearchId() {
    return 'search_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  generatePortfolioId() {
    return 'portfolio_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  generateValuationId() {
    return 'valuation_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  generateRenewalId() {
    return 'renewal_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  /**
   * Get all registrations
   */
  getAllRegistrations() {
    return Array.from(this.registrations.values());
  }

  /**
   * Get registrations by type
   */
  getRegistrationsByType(type) {
    return Array.from(this.registrations.values()).filter(reg => reg.registrationType === type);
  }

  /**
   * Get pending renewals
   */
  getPendingRenewals() {
    const now = new Date();
    const sixMonthsFromNow = new Date(now.getTime() + 6 * 30 * 24 * 60 * 60 * 1000);
    
    return Array.from(this.renewals.values()).filter(renewal => 
      renewal.status === 'scheduled' && 
      new Date(renewal.dueDate) <= sixMonthsFromNow
    );
  }

  /**
   * Export IP registration data
   */
  exportIPData() {
    return {
      registrations: Array.from(this.registrations.values()),
      applications: Array.from(this.applications.values()),
      portfolios: Array.from(this.portfolios.values()),
      priorArtSearches: Array.from(this.priorArtSearches.values()),
      valuations: Array.from(this.valuations.values()),
      renewals: Array.from(this.renewals.values()),
      exportedAt: new Date().toISOString()
    };
  }
}

// Export the copyright and patent integration system
export const copyrightPatentIntegration = new CopyrightPatentIntegration();
