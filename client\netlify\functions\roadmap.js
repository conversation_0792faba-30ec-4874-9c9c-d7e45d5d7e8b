// Roadmap-related functions using Supabase
const { createClient } = require('@supabase/supabase-js');

// Log environment variables for debugging (redacted for security)
console.log('SUPABASE_URL exists:', !!process.env.SUPABASE_URL);
console.log('SUPABASE_SERVICE_KEY exists:', !!process.env.SUPABASE_SERVICE_KEY);

// Supabase client with error handling
let supabase;
try {
  if (!process.env.SUPABASE_URL || !process.env.SUPABASE_SERVICE_KEY) {
    console.error('Missing Supabase environment variables');
  }

  supabase = createClient(
    process.env.SUPABASE_URL || '',
    process.env.SUPABASE_SERVICE_KEY || ''
  );
  console.log('Supabase client initialized successfully');
} catch (error) {
  console.error('Error initializing Supabase client:', error);
}

exports.handler = async function(event, context) {
  // Log request details for debugging
  console.log('Function invoked with method:', event.httpMethod);
  console.log('Path:', event.path);
  console.log('Headers:', JSON.stringify(event.headers));

  // Set CORS headers for better browser compatibility
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Allow-Methods': 'GET, POST, OPTIONS'
  };

  // Handle OPTIONS request (preflight)
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 204,
      headers,
      body: ''
    };
  }

  // Get the JWT token from the request headers
  const authHeader = event.headers.authorization;
  const token = authHeader && authHeader.split(' ')[1];

  // Check if we have a token for write operations
  if (event.httpMethod !== 'GET' && (!token || token === 'null')) {
    return {
      statusCode: 401,
      headers,
      body: JSON.stringify({ message: "Unauthorized" })
    };
  }

  // Check if Supabase client is initialized
  if (!supabase) {
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ message: "Supabase client not initialized. Check environment variables." })
    };
  }

  try {
    // For write operations, verify the token and check admin status
    let user = null;
    if (event.httpMethod !== 'GET') {
      const { data: userData, error: authError } = await supabase.auth.getUser(token);

      if (authError || !userData.user) {
        return {
          statusCode: 401,
          headers,
          body: JSON.stringify({ message: "Invalid token", error: authError?.message })
        };
      }

      user = userData.user;

      // Check if user is admin
      const { data: adminData, error: adminError } = await supabase
        .from('users')
        .select('is_admin')
        .eq('id', user.id)
        .single();

      if (adminError || !adminData || !adminData.is_admin) {
        return {
          statusCode: 403,
          headers,
          body: JSON.stringify({ message: "Only admins can modify roadmap data" })
        };
      }
    }

    // Handle different HTTP methods
    if (event.httpMethod === 'GET') {
      // Get roadmap data
      // For GET requests, first try to get data from the roadmap table
      try {
        const { data, error } = await supabase.rpc('get_roadmap');

        if (error) {
          console.error('Error getting roadmap data from RPC:', error);

          // Fallback to direct table query if RPC fails
          const { data: tableData, error: tableError } = await supabase
            .from('roadmap')
            .select('data')
            .order('last_updated', { ascending: false })
            .limit(1);

          if (tableError) {
            return {
              statusCode: 500,
              headers,
              body: JSON.stringify({
                message: "Error getting roadmap data",
                error: tableError.message,
                originalError: error.message
              })
            };
          }

          // Return the data from the table query
          return {
            statusCode: 200,
            headers,
            body: JSON.stringify(tableData && tableData[0] ? tableData[0].data : [])
          };
        }

        // Return the data from the RPC call
        return {
          statusCode: 200,
          headers,
          body: JSON.stringify(data || [])
        };
      } catch (fetchError) {
        console.error('Error in GET request:', fetchError);
        return {
          statusCode: 500,
          headers,
          body: JSON.stringify({
            message: "Error processing GET request",
            error: fetchError.message
          })
        };
      }
    } else if (event.httpMethod === 'POST') {
      // Update roadmap data
      const roadmapData = JSON.parse(event.body);

      try {
        // Try using the RPC function first
        const { error } = await supabase.rpc('update_roadmap', {
          new_data: roadmapData,
          user_id: user.id
        });

        if (error) {
          console.error('Error updating roadmap data with RPC:', error);

          // Fallback to direct table operations if RPC fails
          // First delete existing data
          const { error: deleteError } = await supabase
            .from('roadmap')
            .delete()
            .not('id', 'is', null); // Delete all records

          if (deleteError) {
            return {
              statusCode: 500,
              headers,
              body: JSON.stringify({
                message: "Error deleting existing roadmap data",
                error: deleteError.message,
                originalError: error.message
              })
            };
          }

          // Then insert new data
          const { error: insertError } = await supabase
            .from('roadmap')
            .insert({
              data: roadmapData,
              updated_by: user.id
            });

          if (insertError) {
            return {
              statusCode: 500,
              headers,
              body: JSON.stringify({
                message: "Error inserting new roadmap data",
                error: insertError.message,
                originalError: error.message
              })
            };
          }

          return {
            statusCode: 200,
            headers,
            body: JSON.stringify({ message: "Roadmap data updated successfully (fallback method)" })
          };
        }

        return {
          statusCode: 200,
          headers,
          body: JSON.stringify({ message: "Roadmap data updated successfully" })
        };
      } catch (updateError) {
        console.error('Error in POST request:', updateError);
        return {
          statusCode: 500,
          headers,
          body: JSON.stringify({
            message: "Error processing POST request",
            error: updateError.message
          })
        };
      }
    }

    return {
      statusCode: 405,
      headers,
      body: JSON.stringify({ message: "Method not allowed" })
    };
  } catch (error) {
    console.error('Error:', error);
    console.error('Unhandled error in roadmap function:', error);
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ message: "Server error", error: error.message })
    };
  }
};
