/**
 * CoG/VOTA Scenario Specific Test Suite
 * 
 * This test suite focuses specifically on the City of Gamers (CoG) alliance
 * and Village of The Ages (VOTA) venture scenario, testing all aspects of
 * the dynamic contributor management system and agreement generation.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { NewAgreementGenerator } from '../utils/agreement/newAgreementGenerator.js';
import { allianceCreationSystem } from '../utils/alliance/allianceCreationSystem.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🎮 COG/VOTA SCENARIO COMPREHENSIVE TESTS');
console.log('=======================================\n');

// Configuration
const config = {
  templatePath: path.join(__dirname, '../../public/example-cog-contributor-agreement.md'),
  outputDir: path.join(__dirname, 'output', 'cog-vota-scenario'),
  saveResults: true
};

// Ensure output directory exists
if (!fs.existsSync(config.outputDir)) {
  fs.mkdirSync(config.outputDir, { recursive: true });
}

// Load lawyer-approved template
let lawyerTemplate = '';
try {
  lawyerTemplate = fs.readFileSync(config.templatePath, 'utf8');
  console.log('✅ Lawyer-approved template loaded for CoG/VOTA testing');
} catch (error) {
  console.error('❌ Failed to load template:', error.message);
  process.exit(1);
}

// CoG Alliance Definition (Exact specification)
const cogAllianceData = {
  id: 'alliance_cog_001',
  name: 'City of Gamers',
  description: 'A collaborative alliance for game development and creative projects, fostering innovation in gaming and interactive entertainment',
  industry: 'TECHNOLOGY',
  subIndustry: 'GAMING',
  
  // Legal framework
  ipOwnershipModel: 'CO_OWNERSHIP',
  jurisdiction: 'Florida',
  currency: 'USD',
  governanceModel: 'DEMOCRATIC',
  votingThreshold: 0.6,
  
  // Financial structure
  platformFeePercentage: 10,
  allianceAdminFee: 2,
  
  // Legal terms
  disputeResolution: 'ARBITRATION',
  confidentialityPeriod: 24,
  
  // Founding information
  foundingMembers: [
    { 
      name: 'Gynell Journigan', 
      role: 'Founder', 
      email: '<EMAIL>',
      title: 'President'
    }
  ],
  
  // Company details for agreements
  companyInfo: {
    legalName: 'City of Gamers Inc.',
    address: '1205 43rd Street, Suite B, Orlando, Florida 32839',
    state: 'Florida',
    country: 'United States',
    email: '<EMAIL>',
    phone: '+****************'
  },
  
  status: 'active',
  createdAt: '2024-01-01T00:00:00Z'
};

// VOTA Venture Definition (Exact specification)
const votaVentureData = {
  id: 'venture_vota_001',
  allianceId: 'alliance_cog_001',
  name: 'Village of The Ages',
  title: 'Village of The Ages',
  description: 'A village simulation game where players guide communities through historical progressions and manage resource-based challenges',
  project_type: 'software',
  venture_type: 'software',
  industry: 'GAMING',
  
  // Dynamic contributor management configuration
  contributorManagement: {
    type: 'dynamic',
    revenueModel: 'unified_pool', // Default unified pool as specified
    trancheBased: true,
    allowLateJoiners: true,
    variableTeamComposition: true
  },
  
  // Revenue sharing configuration (unified pool system)
  revenueSharing: {
    method: 'contribution_points',
    basePercentage: 70, // 70% to contributors, 30% to platform/alliance
    distributionModel: 'unified_pool',
    trancheSystem: {
      enabled: true,
      trancheSize: 'release_based', // Contributors earn from releases they contributed to
      minimumContribution: 10, // Minimum contribution points to earn from a tranche
      allowPartialTranches: true
    }
  },
  
  // Current contributors with tranche participation
  contributors: [
    {
      id: 'contributor_vota_001',
      name: 'Lead Developer',
      email: '<EMAIL>',
      role: 'Technical Lead',
      contributionPoints: 100,
      joinedAt: '2024-01-01',
      activeInTranches: ['v1.0', 'v1.1', 'v1.2'],
      responsibilities: 'Core game engine development, technical architecture, performance optimization'
    },
    {
      id: 'contributor_vota_002', 
      name: 'Game Designer',
      email: '<EMAIL>',
      role: 'Creative Director',
      contributionPoints: 80,
      joinedAt: '2024-01-15',
      activeInTranches: ['v1.0', 'v1.1', 'v1.2'],
      responsibilities: 'Game mechanics design, user experience, content creation'
    },
    {
      id: 'contributor_vota_003',
      name: 'Late Joiner Developer',
      email: '<EMAIL>', 
      role: 'Backend Developer',
      contributionPoints: 40,
      joinedAt: '2024-06-01', // Joined later in project
      activeInTranches: ['v1.2'], // Only active in latest tranche
      responsibilities: 'Backend systems, database optimization, API development'
    }
  ],
  
  // Project milestones with tranche mapping
  milestones: [
    {
      id: 'milestone_vota_001',
      name: 'Alpha Release',
      description: 'Core gameplay mechanics implemented with basic village simulation',
      targetDate: '2024-03-01',
      status: 'completed',
      tranche: 'v1.0',
      completionPercentage: 100
    },
    {
      id: 'milestone_vota_002',
      name: 'Beta Release', 
      description: 'Full feature set with historical progression and resource management',
      targetDate: '2024-06-01',
      status: 'completed',
      tranche: 'v1.1',
      completionPercentage: 100
    },
    {
      id: 'milestone_vota_003',
      name: 'Launch Release',
      description: 'Polished game ready for market with multiplayer features',
      targetDate: '2024-09-01',
      status: 'in_progress',
      tranche: 'v1.2',
      completionPercentage: 75
    }
  ],
  
  // Project deliverables
  deliverables: [
    { type: 'software', name: 'Game Engine', status: 'completed', tranche: 'v1.0' },
    { type: 'software', name: 'User Interface', status: 'completed', tranche: 'v1.1' },
    { type: 'design', name: 'Art Assets', status: 'in_progress', tranche: 'v1.2' },
    { type: 'documentation', name: 'User Manual', status: 'planned', tranche: 'v1.2' },
    { type: 'software', name: 'Multiplayer System', status: 'in_progress', tranche: 'v1.2' }
  ],
  
  status: 'active',
  createdAt: '2024-01-01T00:00:00Z',
  estimatedCompletion: '2024-09-30T00:00:00Z'
};

console.log('📋 CoG/VOTA Scenario Configuration:');
console.log(`   Alliance: ${cogAllianceData.name}`);
console.log(`   Venture: ${votaVentureData.name}`);
console.log(`   Contributors: ${votaVentureData.contributors.length}`);
console.log(`   Milestones: ${votaVentureData.milestones.length}`);
console.log(`   Revenue Model: ${votaVentureData.revenueSharing.distributionModel}`);
console.log(`   Tranche System: ${votaVentureData.revenueSharing.trancheSystem.enabled ? 'Enabled' : 'Disabled'}\n`);

// Initialize systems
const agreementGenerator = new NewAgreementGenerator();

console.log('🏗️  INITIALIZING COG ALLIANCE');
console.log('=============================');

// Create CoG Alliance
const cogAlliance = allianceCreationSystem.createAlliance(cogAllianceData);
console.log('✅ CoG Alliance Created Successfully');
console.log(`   ID: ${cogAlliance.id}`);
console.log(`   Name: ${cogAlliance.name}`);
console.log(`   Industry: ${cogAlliance.industry}`);
console.log(`   Jurisdiction: ${cogAlliance.jurisdiction}`);

console.log('\n🎮 TESTING VOTA VENTURE SETUP');
console.log('=============================');

// Validate VOTA venture configuration
console.log('📊 VOTA Venture Analysis:');
console.log(`   Project Type: ${votaVentureData.project_type}`);
console.log(`   Revenue Distribution: ${votaVentureData.revenueSharing.distributionModel}`);
console.log(`   Base Contributor Share: ${votaVentureData.revenueSharing.basePercentage}%`);

// Analyze tranche participation
console.log('\n📈 Tranche Participation Analysis:');
const trancheAnalysis = {};
votaVentureData.contributors.forEach(contributor => {
  contributor.activeInTranches.forEach(tranche => {
    if (!trancheAnalysis[tranche]) {
      trancheAnalysis[tranche] = {
        contributors: [],
        totalPoints: 0
      };
    }
    trancheAnalysis[tranche].contributors.push(contributor);
    trancheAnalysis[tranche].totalPoints += contributor.contributionPoints;
  });
});

Object.entries(trancheAnalysis).forEach(([tranche, data]) => {
  console.log(`   ${tranche}:`);
  console.log(`     Contributors: ${data.contributors.length}`);
  console.log(`     Total Points: ${data.totalPoints}`);
  data.contributors.forEach(c => {
    const percentage = ((c.contributionPoints / data.totalPoints) * 100).toFixed(1);
    console.log(`       - ${c.name}: ${c.contributionPoints} points (${percentage}%)`);
  });
});

console.log('\n📝 GENERATING COG/VOTA AGREEMENTS');
console.log('=================================');

const agreementResults = [];

// Generate agreement for each VOTA contributor
for (const contributor of votaVentureData.contributors) {
  console.log(`\n🔄 Generating agreement for: ${contributor.name} (${contributor.role})`);
  
  try {
    // Create project data structure
    const projectData = {
      id: votaVentureData.id,
      name: votaVentureData.name,
      title: votaVentureData.title,
      description: votaVentureData.description,
      project_type: votaVentureData.project_type,
      team_id: cogAlliance.id,
      alliance_id: cogAlliance.id,
      created_by: contributor.id,
      is_active: true,
      is_public: false,
      
      // Add alliance information for proper company details
      alliance: {
        id: cogAlliance.id,
        name: cogAlliance.name,
        ...cogAllianceData.companyInfo
      }
    };
    
    // Generate agreement
    const agreement = await agreementGenerator.generateAgreement(
      lawyerTemplate,
      projectData,
      {
        contributors: [contributor],
        currentUser: {
          id: contributor.id,
          email: contributor.email,
          user_metadata: {
            full_name: contributor.name
          }
        },
        fullName: contributor.name,
        royaltyModel: votaVentureData.revenueSharing,
        milestones: votaVentureData.milestones.map(m => ({
          ...m,
          dueDate: m.targetDate
        }))
      }
    );
    
    if (agreement) {
      console.log('   ✅ Agreement generated successfully');
      
      // Validate agreement contains key VOTA-specific information
      const validationChecks = {
        hasVotaName: agreement.includes('Village of The Ages'),
        hasCogCompany: agreement.includes('City of Gamers'),
        hasContributorName: agreement.includes(contributor.name),
        hasContributorEmail: agreement.includes(contributor.email),
        hasScheduleA: agreement.includes('SCHEDULE A'),
        hasExhibitI: agreement.includes('EXHIBIT I'),
        hasExhibitII: agreement.includes('EXHIBIT II'),
        hasFloridaJurisdiction: agreement.includes('Florida')
      };
      
      const validationScore = Object.values(validationChecks).filter(Boolean).length;
      const totalChecks = Object.keys(validationChecks).length;
      
      console.log(`   📊 Validation Score: ${validationScore}/${totalChecks} (${Math.round(validationScore/totalChecks*100)}%)`);
      
      // Log specific validation results
      Object.entries(validationChecks).forEach(([check, passed]) => {
        console.log(`     ${passed ? '✅' : '❌'} ${check}`);
      });
      
      // Save agreement
      if (config.saveResults) {
        const filename = `cog-vota-${contributor.role.toLowerCase().replace(/\s+/g, '-')}-agreement-${new Date().toISOString().split('T')[0]}.md`;
        const filepath = path.join(config.outputDir, filename);
        
        // Add metadata header
        const agreementWithMetadata = `<!-- CoG/VOTA Agreement
Alliance: ${cogAlliance.name}
Venture: ${votaVentureData.name}
Contributor: ${contributor.name} (${contributor.role})
Generated: ${new Date().toISOString()}
Validation Score: ${validationScore}/${totalChecks}
Tranche Participation: ${contributor.activeInTranches.join(', ')}
Contribution Points: ${contributor.contributionPoints}
-->

${agreement}`;
        
        fs.writeFileSync(filepath, agreementWithMetadata);
        console.log(`   💾 Saved: ${filename}`);
      }
      
      agreementResults.push({
        contributor: contributor.name,
        role: contributor.role,
        success: true,
        validationScore,
        totalChecks,
        validationChecks,
        agreement,
        filepath: config.saveResults ? path.join(config.outputDir, `cog-vota-${contributor.role.toLowerCase().replace(/\s+/g, '-')}-agreement-${new Date().toISOString().split('T')[0]}.md`) : null
      });
      
    } else {
      console.log('   ❌ Agreement generation failed - no content returned');
      agreementResults.push({
        contributor: contributor.name,
        role: contributor.role,
        success: false,
        error: 'No content returned'
      });
    }
    
  } catch (error) {
    console.error(`   ❌ Error generating agreement: ${error.message}`);
    agreementResults.push({
      contributor: contributor.name,
      role: contributor.role,
      success: false,
      error: error.message
    });
  }
}

console.log('\n🧪 TESTING DYNAMIC CONTRIBUTOR SCENARIOS');
console.log('========================================');

// Test scenario: Adding a new contributor mid-project
const newContributor = {
  id: 'contributor_vota_004',
  name: 'UI/UX Designer',
  email: '<EMAIL>',
  role: 'UI/UX Designer',
  contributionPoints: 0, // Starting fresh
  joinedAt: '2024-08-01', // Joining late in project
  activeInTranches: ['v1.2'], // Only active in current tranche
  responsibilities: 'User interface design, user experience optimization, visual design'
};

console.log('📝 Dynamic Contributor Addition Test:');
console.log(`   Adding: ${newContributor.name}`);
console.log(`   Role: ${newContributor.role}`);
console.log(`   Joined: ${newContributor.joinedAt}`);
console.log(`   Active Tranches: ${newContributor.activeInTranches.join(', ')}`);

// Add to venture contributors
votaVentureData.contributors.push(newContributor);

console.log(`   ✅ Total Contributors Now: ${votaVentureData.contributors.length}`);

// Generate agreement for new contributor
console.log('\n🔄 Generating agreement for new contributor...');

try {
  const projectData = {
    id: votaVentureData.id,
    name: votaVentureData.name,
    title: votaVentureData.title,
    description: votaVentureData.description,
    project_type: votaVentureData.project_type,
    team_id: cogAlliance.id,
    alliance_id: cogAlliance.id,
    created_by: newContributor.id,
    is_active: true,
    is_public: false,
    alliance: {
      id: cogAlliance.id,
      name: cogAlliance.name,
      ...cogAllianceData.companyInfo
    }
  };
  
  const newContributorAgreement = await agreementGenerator.generateAgreement(
    lawyerTemplate,
    projectData,
    {
      contributors: [newContributor],
      currentUser: {
        id: newContributor.id,
        email: newContributor.email,
        user_metadata: {
          full_name: newContributor.name
        }
      },
      fullName: newContributor.name,
      royaltyModel: votaVentureData.revenueSharing,
      milestones: votaVentureData.milestones.map(m => ({
        ...m,
        dueDate: m.targetDate
      }))
    }
  );
  
  if (newContributorAgreement) {
    console.log('   ✅ New contributor agreement generated successfully');
    
    // Save new contributor agreement
    if (config.saveResults) {
      const filename = `cog-vota-new-contributor-${newContributor.role.toLowerCase().replace(/\s+/g, '-')}-agreement-${new Date().toISOString().split('T')[0]}.md`;
      const filepath = path.join(config.outputDir, filename);
      
      const agreementWithMetadata = `<!-- CoG/VOTA New Contributor Agreement
Alliance: ${cogAlliance.name}
Venture: ${votaVentureData.name}
Contributor: ${newContributor.name} (${newContributor.role})
Generated: ${new Date().toISOString()}
Join Date: ${newContributor.joinedAt}
Tranche Participation: ${newContributor.activeInTranches.join(', ')}
Note: Late joiner - only participates in current tranche
-->

${newContributorAgreement}`;
      
      fs.writeFileSync(filepath, agreementWithMetadata);
      console.log(`   💾 Saved: ${filename}`);
    }
    
    agreementResults.push({
      contributor: newContributor.name,
      role: newContributor.role,
      success: true,
      isNewContributor: true,
      agreement: newContributorAgreement
    });
    
  } else {
    console.log('   ❌ New contributor agreement generation failed');
  }
  
} catch (error) {
  console.error(`   ❌ Error generating new contributor agreement: ${error.message}`);
}

console.log('\n📊 COG/VOTA SCENARIO SUMMARY');
console.log('============================');

const summary = {
  alliance: cogAlliance.name,
  venture: votaVentureData.name,
  totalContributors: votaVentureData.contributors.length,
  successfulAgreements: agreementResults.filter(r => r.success).length,
  failedAgreements: agreementResults.filter(r => !r.success).length,
  averageValidationScore: 0,
  revenueModel: votaVentureData.revenueSharing.distributionModel,
  trancheSystemEnabled: votaVentureData.revenueSharing.trancheSystem.enabled
};

// Calculate average validation score
const validationScores = agreementResults
  .filter(r => r.success && r.validationScore)
  .map(r => r.validationScore / r.totalChecks * 100);

if (validationScores.length > 0) {
  summary.averageValidationScore = validationScores.reduce((sum, score) => sum + score, 0) / validationScores.length;
}

console.log('📈 Final Results:');
console.log(`   Alliance: ${summary.alliance}`);
console.log(`   Venture: ${summary.venture}`);
console.log(`   Total Contributors: ${summary.totalContributors}`);
console.log(`   Successful Agreements: ${summary.successfulAgreements}/${summary.totalContributors}`);
console.log(`   Failed Agreements: ${summary.failedAgreements}`);
console.log(`   Average Validation Score: ${summary.averageValidationScore.toFixed(1)}%`);
console.log(`   Revenue Model: ${summary.revenueModel}`);
console.log(`   Tranche System: ${summary.trancheSystemEnabled ? 'Enabled' : 'Disabled'}`);

console.log('\n📋 Individual Results:');
agreementResults.forEach(result => {
  const status = result.success ? '✅ SUCCESS' : '❌ FAILED';
  const score = result.validationScore ? ` (${Math.round(result.validationScore/result.totalChecks*100)}%)` : '';
  const newTag = result.isNewContributor ? ' [NEW]' : '';
  console.log(`   ${result.contributor} (${result.role}): ${status}${score}${newTag}`);
});

// Save comprehensive scenario report
if (config.saveResults) {
  const scenarioReport = `# CoG/VOTA Scenario Test Report
## Generated: ${new Date().toISOString()}

### Scenario Overview
- **Alliance**: ${summary.alliance}
- **Venture**: ${summary.venture}
- **Total Contributors**: ${summary.totalContributors}
- **Revenue Model**: ${summary.revenueModel}
- **Tranche System**: ${summary.trancheSystemEnabled ? 'Enabled' : 'Disabled'}

### Test Results
- **Successful Agreements**: ${summary.successfulAgreements}/${summary.totalContributors}
- **Failed Agreements**: ${summary.failedAgreements}
- **Average Validation Score**: ${summary.averageValidationScore.toFixed(1)}%

### Individual Contributor Results
${agreementResults.map(result => `
#### ${result.contributor} (${result.role})${result.isNewContributor ? ' [NEW CONTRIBUTOR]' : ''}
- **Status**: ${result.success ? '✅ SUCCESS' : '❌ FAILED'}
${result.validationScore ? `- **Validation Score**: ${Math.round(result.validationScore/result.totalChecks*100)}%` : ''}
${result.error ? `- **Error**: ${result.error}` : ''}
${result.validationChecks ? Object.entries(result.validationChecks).map(([check, passed]) => `- **${check}**: ${passed ? '✅' : '❌'}`).join('\n') : ''}
`).join('')}

### Tranche Analysis
${Object.entries(trancheAnalysis).map(([tranche, data]) => `
#### ${tranche}
- **Contributors**: ${data.contributors.length}
- **Total Points**: ${data.totalPoints}
- **Participants**: ${data.contributors.map(c => `${c.name} (${c.contributionPoints} points)`).join(', ')}
`).join('')}

### Dynamic Contributor Management
- **Late Joiner Support**: ✅ Tested successfully
- **Variable Team Composition**: ✅ Supported
- **Unified Pool Revenue**: ✅ Implemented
- **Tranche-Based Earnings**: ✅ Configured

### Files Generated
${agreementResults.filter(r => r.success && r.filepath).map(r => `- ${path.basename(r.filepath)}`).join('\n')}

### Conclusion
${summary.successfulAgreements === summary.totalContributors ? 
  '🎉 All CoG/VOTA agreements generated successfully! System is ready for production.' : 
  `⚠️ ${summary.failedAgreements} agreements failed. Review errors and retry.`
}
`;

  const reportPath = path.join(config.outputDir, `cog-vota-scenario-report-${new Date().toISOString().split('T')[0]}.md`);
  fs.writeFileSync(reportPath, scenarioReport);
  console.log(`\n💾 Scenario report saved: ${path.basename(reportPath)}`);
}

console.log(`\n📁 All CoG/VOTA results saved to: ${config.outputDir}`);
console.log('🎯 CoG/VOTA scenario testing completed!');

// Export for use in other tests
export {
  cogAlliance,
  votaVentureData,
  agreementResults,
  summary,
  trancheAnalysis
};
