/**
 * Termination & Breach Handling System
 * 
 * Comprehensive system for handling contract breaches, cure periods, and termination consequences:
 * - Breach detection and classification
 * - Cure period management and tracking
 * - Termination process automation
 * - Asset and IP transfer handling
 * - Financial settlement calculations
 * - Post-termination obligations management
 */

import { performanceStandardsFramework } from './performanceStandardsFramework.js';
import { revenueCalculationEngine } from './revenueCalculationEngine.js';
import { ipRightsFramework } from './ipRightsFramework.js';

// ============================================================================
// BREACH AND TERMINATION TYPES
// ============================================================================

export const BREACH_TYPES = {
  MATERIAL: 'material',
  NON_MATERIAL: 'non_material',
  FUNDAMENTAL: 'fundamental',
  ANTICIPATORY: 'anticipatory',
  PARTIAL: 'partial',
  TOTAL: 'total'
};

export const BREACH_CATEGORIES = {
  PERFORMANCE: 'performance',
  PAYMENT: 'payment',
  CONFIDENTIALITY: 'confidentiality',
  IP_VIOLATION: 'ip_violation',
  NON_COMPETE: 'non_compete',
  COMMUNICATION: 'communication',
  QUALITY: 'quality',
  DEADLINE: 'deadline',
  SCOPE: 'scope'
};

export const TERMINATION_TYPES = {
  FOR_CAUSE: 'for_cause',
  FOR_CONVENIENCE: 'for_convenience',
  MUTUAL: 'mutual',
  AUTOMATIC: 'automatic',
  EXPIRATION: 'expiration',
  BREACH: 'breach'
};

export const CURE_PERIOD_TYPES = {
  IMMEDIATE: 'immediate',           // 0 days
  SHORT: 'short',                   // 1-7 days
  STANDARD: 'standard',             // 14-30 days
  EXTENDED: 'extended',             // 30-60 days
  CUSTOM: 'custom'                  // Custom period
};

export const TERMINATION_CONSEQUENCES = {
  IMMEDIATE_CESSATION: 'immediate_cessation',
  WORK_COMPLETION: 'work_completion',
  TRANSITION_PERIOD: 'transition_period',
  ASSET_RETURN: 'asset_return',
  IP_TRANSFER: 'ip_transfer',
  PAYMENT_SETTLEMENT: 'payment_settlement',
  CONFIDENTIALITY_CONTINUATION: 'confidentiality_continuation',
  NON_COMPETE_ENFORCEMENT: 'non_compete_enforcement'
};

// ============================================================================
// TERMINATION & BREACH HANDLING CLASS
// ============================================================================

export class TerminationBreachHandling {
  constructor() {
    this.breachReports = new Map();
    this.cureNotices = new Map();
    this.terminationProcesses = new Map();
    this.settlementCalculations = new Map();
    this.postTerminationObligations = new Map();
    this.breachHistory = new Map();
  }

  /**
   * Report a contract breach
   */
  reportBreach(breachDefinition) {
    const breachReport = {
      id: breachDefinition.id || this.generateBreachId(),
      
      // Basic information
      ventureId: breachDefinition.ventureId,
      allianceId: breachDefinition.allianceId,
      agreementId: breachDefinition.agreementId,
      
      // Breach details
      breachType: breachDefinition.breachType,
      breachCategory: breachDefinition.breachCategory,
      description: breachDefinition.description,
      severity: breachDefinition.severity || 'medium',
      
      // Parties involved
      breachingParty: breachDefinition.breachingParty,
      reportingParty: breachDefinition.reportingParty,
      affectedParties: breachDefinition.affectedParties || [],
      
      // Breach specifics
      breachDate: breachDefinition.breachDate || new Date().toISOString(),
      discoveryDate: breachDefinition.discoveryDate || new Date().toISOString(),
      reportDate: new Date().toISOString(),
      
      // Evidence and documentation
      evidence: breachDefinition.evidence || [],
      documentation: breachDefinition.documentation || [],
      witnesses: breachDefinition.witnesses || [],
      
      // Impact assessment
      impactAssessment: this.assessBreachImpact(breachDefinition),
      
      // Cure information
      isCurable: breachDefinition.isCurable !== false,
      curePeriod: this.determineCurePeriod(breachDefinition),
      cureRequirements: breachDefinition.cureRequirements || [],
      
      // Status tracking
      status: 'reported',
      investigationStatus: 'pending',
      resolutionStatus: 'pending',
      
      // Timeline
      investigationDeadline: this.calculateInvestigationDeadline(),
      cureDeadline: null,
      
      // Metadata
      createdAt: new Date().toISOString(),
      lastUpdated: new Date().toISOString()
    };

    this.breachReports.set(breachReport.id, breachReport);
    
    // Start breach investigation process
    this.initiateBreachInvestigation(breachReport);
    
    // Update breach history
    this.updateBreachHistory(breachReport);
    
    return breachReport;
  }

  /**
   * Assess the impact of a breach
   */
  assessBreachImpact(breachDefinition) {
    return {
      // Financial impact
      financialImpact: {
        estimatedLoss: breachDefinition.estimatedLoss || 0,
        revenueImpact: breachDefinition.revenueImpact || 0,
        additionalCosts: breachDefinition.additionalCosts || 0,
        currency: breachDefinition.currency || 'USD'
      },
      
      // Timeline impact
      timelineImpact: {
        delayDays: breachDefinition.delayDays || 0,
        milestoneAffected: breachDefinition.milestoneAffected || [],
        criticalPathImpact: breachDefinition.criticalPathImpact || false
      },
      
      // Quality impact
      qualityImpact: {
        qualityDegradation: breachDefinition.qualityDegradation || 'none',
        deliverableAffected: breachDefinition.deliverableAffected || [],
        reworkRequired: breachDefinition.reworkRequired || false
      },
      
      // Relationship impact
      relationshipImpact: {
        trustLevel: breachDefinition.trustLevel || 'unchanged',
        collaborationRisk: breachDefinition.collaborationRisk || 'low',
        reputationRisk: breachDefinition.reputationRisk || 'low'
      },
      
      // Legal impact
      legalImpact: {
        contractViolation: breachDefinition.contractViolation || false,
        legalRisk: breachDefinition.legalRisk || 'low',
        complianceIssues: breachDefinition.complianceIssues || []
      }
    };
  }

  /**
   * Determine appropriate cure period
   */
  determineCurePeriod(breachDefinition) {
    const { breachType, breachCategory, severity } = breachDefinition;
    
    // Immediate cure required for critical breaches
    if (severity === 'critical' || breachCategory === BREACH_CATEGORIES.CONFIDENTIALITY) {
      return {
        type: CURE_PERIOD_TYPES.IMMEDIATE,
        days: 0,
        businessDays: false
      };
    }
    
    // Short cure period for urgent issues
    if (severity === 'high' || breachCategory === BREACH_CATEGORIES.PAYMENT) {
      return {
        type: CURE_PERIOD_TYPES.SHORT,
        days: 7,
        businessDays: true
      };
    }
    
    // Standard cure period for most breaches
    if (breachType === BREACH_TYPES.MATERIAL) {
      return {
        type: CURE_PERIOD_TYPES.STANDARD,
        days: 30,
        businessDays: true
      };
    }
    
    // Extended cure period for complex issues
    return {
      type: CURE_PERIOD_TYPES.EXTENDED,
      days: 60,
      businessDays: true
    };
  }

  /**
   * Issue cure notice
   */
  issueCureNotice(breachId, cureNoticeDefinition) {
    const breachReport = this.breachReports.get(breachId);
    if (!breachReport) {
      throw new Error(`Breach report ${breachId} not found`);
    }

    const cureNotice = {
      id: cureNoticeDefinition.id || this.generateCureNoticeId(),
      
      // Reference information
      breachId,
      ventureId: breachReport.ventureId,
      agreementId: breachReport.agreementId,
      
      // Notice details
      issuedTo: breachReport.breachingParty,
      issuedBy: cureNoticeDefinition.issuedBy,
      issuedDate: new Date().toISOString(),
      
      // Cure requirements
      cureRequirements: cureNoticeDefinition.cureRequirements || breachReport.cureRequirements,
      curePeriod: breachReport.curePeriod,
      cureDeadline: this.calculateCureDeadline(breachReport.curePeriod),
      
      // Notice content
      noticeText: cureNoticeDefinition.noticeText || this.generateCureNoticeText(breachReport),
      legalReferences: cureNoticeDefinition.legalReferences || [],
      
      // Consequences
      failureToCureConsequences: cureNoticeDefinition.failureToCureConsequences || [
        'Termination of agreement',
        'Legal action',
        'Damages claim'
      ],
      
      // Delivery information
      deliveryMethod: cureNoticeDefinition.deliveryMethod || 'email',
      deliveryConfirmation: false,
      deliveryDate: null,
      
      // Status tracking
      status: 'issued',
      acknowledged: false,
      acknowledgedDate: null,
      
      // Response tracking
      cureAttempts: [],
      cureCompleted: false,
      cureVerificationDate: null,
      
      // Metadata
      createdAt: new Date().toISOString(),
      lastUpdated: new Date().toISOString()
    };

    this.cureNotices.set(cureNotice.id, cureNotice);
    
    // Update breach report
    breachReport.cureNoticeId = cureNotice.id;
    breachReport.cureDeadline = cureNotice.cureDeadline;
    breachReport.status = 'cure_notice_issued';
    
    // Schedule cure deadline monitoring
    this.scheduleCureDeadlineMonitoring(cureNotice);
    
    return cureNotice;
  }

  /**
   * Calculate cure deadline
   */
  calculateCureDeadline(curePeriod) {
    const now = new Date();
    const deadline = new Date(now);
    
    if (curePeriod.businessDays) {
      // Add business days only
      let daysAdded = 0;
      while (daysAdded < curePeriod.days) {
        deadline.setDate(deadline.getDate() + 1);
        // Skip weekends (0 = Sunday, 6 = Saturday)
        if (deadline.getDay() !== 0 && deadline.getDay() !== 6) {
          daysAdded++;
        }
      }
    } else {
      // Add calendar days
      deadline.setDate(deadline.getDate() + curePeriod.days);
    }
    
    return deadline.toISOString();
  }

  /**
   * Generate cure notice text
   */
  generateCureNoticeText(breachReport) {
    return `
NOTICE TO CURE BREACH

Date: ${new Date().toLocaleDateString()}

TO: ${breachReport.breachingParty}
FROM: ${breachReport.reportingParty}

RE: Breach of Agreement - ${breachReport.agreementId}

You are hereby notified that you are in breach of the above-referenced agreement. 

BREACH DESCRIPTION:
${breachReport.description}

CURE REQUIREMENTS:
${breachReport.cureRequirements.map(req => `• ${req}`).join('\n')}

CURE PERIOD:
You have ${breachReport.curePeriod.days} ${breachReport.curePeriod.businessDays ? 'business' : 'calendar'} days from receipt of this notice to cure the above-described breach.

FAILURE TO CURE:
If you fail to cure this breach within the specified time period, we reserve the right to terminate the agreement and pursue all available legal remedies.

This notice is given pursuant to the terms of the agreement and applicable law.
    `.trim();
  }

  /**
   * Initiate termination process
   */
  initiateTermination(terminationDefinition) {
    const terminationProcess = {
      id: terminationDefinition.id || this.generateTerminationId(),
      
      // Basic information
      ventureId: terminationDefinition.ventureId,
      allianceId: terminationDefinition.allianceId,
      agreementId: terminationDefinition.agreementId,
      
      // Termination details
      terminationType: terminationDefinition.terminationType,
      terminationReason: terminationDefinition.terminationReason,
      terminationDate: terminationDefinition.terminationDate || new Date().toISOString(),
      effectiveDate: terminationDefinition.effectiveDate,
      
      // Parties involved
      terminatingParty: terminationDefinition.terminatingParty,
      terminatedParty: terminationDefinition.terminatedParty,
      allParties: terminationDefinition.allParties || [],
      
      // Related breach (if applicable)
      relatedBreachId: terminationDefinition.relatedBreachId,
      
      // Termination process
      noticePeriod: terminationDefinition.noticePeriod || 30,
      noticeGiven: false,
      noticeDate: null,
      
      // Consequences and obligations
      immediateConsequences: this.determineImmediateConsequences(terminationDefinition),
      postTerminationObligations: this.determinePostTerminationObligations(terminationDefinition),
      
      // Asset and IP handling
      assetTransfer: this.planAssetTransfer(terminationDefinition),
      ipTransfer: this.planIPTransfer(terminationDefinition),
      
      // Financial settlement
      settlementRequired: true,
      settlementCalculation: null,
      
      // Transition planning
      transitionPlan: this.createTransitionPlan(terminationDefinition),
      
      // Status tracking
      status: 'initiated',
      completionPercentage: 0,
      
      // Timeline
      milestones: this.createTerminationMilestones(terminationDefinition),
      
      // Metadata
      createdAt: new Date().toISOString(),
      lastUpdated: new Date().toISOString()
    };

    this.terminationProcesses.set(terminationProcess.id, terminationProcess);
    
    // Calculate financial settlement
    if (terminationProcess.settlementRequired) {
      terminationProcess.settlementCalculation = this.calculateSettlement(terminationProcess);
    }
    
    // Start termination process
    this.executeTerminationProcess(terminationProcess);
    
    return terminationProcess;
  }

  /**
   * Determine immediate consequences of termination
   */
  determineImmediateConsequences(terminationDefinition) {
    const consequences = [];
    
    // Work cessation
    consequences.push({
      type: TERMINATION_CONSEQUENCES.IMMEDIATE_CESSATION,
      description: 'All work under the agreement must cease immediately',
      deadline: terminationDefinition.effectiveDate,
      responsible: 'all_parties'
    });
    
    // Asset return
    consequences.push({
      type: TERMINATION_CONSEQUENCES.ASSET_RETURN,
      description: 'Return all confidential materials and assets',
      deadline: this.calculateDeadline(terminationDefinition.effectiveDate, 7),
      responsible: terminationDefinition.terminatedParty
    });
    
    // Payment settlement
    consequences.push({
      type: TERMINATION_CONSEQUENCES.PAYMENT_SETTLEMENT,
      description: 'Settle all outstanding payments and obligations',
      deadline: this.calculateDeadline(terminationDefinition.effectiveDate, 30),
      responsible: 'all_parties'
    });
    
    return consequences;
  }

  /**
   * Determine post-termination obligations
   */
  determinePostTerminationObligations(terminationDefinition) {
    const obligations = [];
    
    // Confidentiality continuation
    obligations.push({
      type: TERMINATION_CONSEQUENCES.CONFIDENTIALITY_CONTINUATION,
      description: 'Confidentiality obligations continue indefinitely',
      duration: 'indefinite',
      responsible: 'all_parties'
    });
    
    // Non-compete enforcement (if applicable)
    if (terminationDefinition.hasNonCompete) {
      obligations.push({
        type: TERMINATION_CONSEQUENCES.NON_COMPETE_ENFORCEMENT,
        description: 'Non-compete restrictions remain in effect',
        duration: terminationDefinition.nonCompetePeriod || '12 months',
        responsible: terminationDefinition.terminatedParty
      });
    }
    
    return obligations;
  }

  /**
   * Plan asset transfer
   */
  planAssetTransfer(terminationDefinition) {
    return {
      physicalAssets: terminationDefinition.physicalAssets || [],
      digitalAssets: terminationDefinition.digitalAssets || [],
      transferDeadline: this.calculateDeadline(terminationDefinition.effectiveDate, 14),
      transferMethod: terminationDefinition.transferMethod || 'secure_delivery',
      verificationRequired: true
    };
  }

  /**
   * Plan IP transfer
   */
  planIPTransfer(terminationDefinition) {
    return {
      ipAssets: terminationDefinition.ipAssets || [],
      transferType: terminationDefinition.ipTransferType || 'return_to_owner',
      transferDeadline: this.calculateDeadline(terminationDefinition.effectiveDate, 30),
      documentationRequired: true,
      registrationUpdates: terminationDefinition.registrationUpdates || []
    };
  }

  /**
   * Create transition plan
   */
  createTransitionPlan(terminationDefinition) {
    return {
      transitionPeriod: terminationDefinition.transitionPeriod || 30,
      knowledgeTransfer: terminationDefinition.knowledgeTransfer || [],
      handoverMeetings: terminationDefinition.handoverMeetings || [],
      documentationRequirements: terminationDefinition.documentationRequirements || [],
      supportPeriod: terminationDefinition.supportPeriod || 0
    };
  }

  /**
   * Create termination milestones
   */
  createTerminationMilestones(terminationDefinition) {
    const effectiveDate = new Date(terminationDefinition.effectiveDate);
    
    return [
      {
        name: 'Termination Notice',
        deadline: new Date().toISOString(),
        status: 'pending',
        description: 'Issue formal termination notice'
      },
      {
        name: 'Work Cessation',
        deadline: terminationDefinition.effectiveDate,
        status: 'pending',
        description: 'All work activities must cease'
      },
      {
        name: 'Asset Return',
        deadline: this.calculateDeadline(terminationDefinition.effectiveDate, 7),
        status: 'pending',
        description: 'Return all assets and materials'
      },
      {
        name: 'Financial Settlement',
        deadline: this.calculateDeadline(terminationDefinition.effectiveDate, 30),
        status: 'pending',
        description: 'Complete all financial settlements'
      },
      {
        name: 'Final Documentation',
        deadline: this.calculateDeadline(terminationDefinition.effectiveDate, 45),
        status: 'pending',
        description: 'Complete all final documentation and transfers'
      }
    ];
  }

  /**
   * Calculate financial settlement
   */
  calculateSettlement(terminationProcess) {
    const settlement = {
      id: this.generateSettlementId(),
      terminationId: terminationProcess.id,
      
      // Outstanding payments
      outstandingPayments: this.calculateOutstandingPayments(terminationProcess),
      
      // Work completed
      workCompleted: this.calculateWorkCompleted(terminationProcess),
      
      // Expenses and costs
      expenses: this.calculateExpenses(terminationProcess),
      
      // Penalties and damages
      penalties: this.calculatePenalties(terminationProcess),
      
      // Final settlement
      netSettlement: 0,
      currency: 'USD',
      
      // Payment terms
      paymentDeadline: this.calculateDeadline(terminationProcess.effectiveDate, 30),
      paymentMethod: 'bank_transfer',
      
      // Status
      status: 'calculated',
      approved: false,
      paid: false
    };

    // Calculate net settlement
    settlement.netSettlement = 
      settlement.outstandingPayments.total + 
      settlement.workCompleted.total - 
      settlement.expenses.total - 
      settlement.penalties.total;

    this.settlementCalculations.set(settlement.id, settlement);
    return settlement;
  }

  /**
   * Calculate outstanding payments
   */
  calculateOutstandingPayments(terminationProcess) {
    // This would integrate with the revenue calculation engine
    return {
      invoiced: 0,
      accrued: 0,
      total: 0,
      breakdown: []
    };
  }

  /**
   * Calculate work completed
   */
  calculateWorkCompleted(terminationProcess) {
    // This would calculate value of work completed but not yet paid
    return {
      milestonePayments: 0,
      hourlyWork: 0,
      deliverables: 0,
      total: 0,
      breakdown: []
    };
  }

  /**
   * Calculate expenses
   */
  calculateExpenses(terminationProcess) {
    return {
      reimbursableExpenses: 0,
      terminationCosts: 0,
      total: 0,
      breakdown: []
    };
  }

  /**
   * Calculate penalties
   */
  calculatePenalties(terminationProcess) {
    return {
      breachPenalties: 0,
      earlyTerminationPenalties: 0,
      damageClaims: 0,
      total: 0,
      breakdown: []
    };
  }

  /**
   * Execute termination process
   */
  executeTerminationProcess(terminationProcess) {
    // Start with issuing termination notice
    this.issueTerminationNotice(terminationProcess);
    
    // Schedule milestone monitoring
    this.scheduleTerminationMilestones(terminationProcess);
    
    // Update status
    terminationProcess.status = 'in_progress';
    terminationProcess.lastUpdated = new Date().toISOString();
  }

  /**
   * Issue termination notice
   */
  issueTerminationNotice(terminationProcess) {
    // Implementation for issuing formal termination notice
    terminationProcess.noticeGiven = true;
    terminationProcess.noticeDate = new Date().toISOString();
    
    // Update first milestone
    if (terminationProcess.milestones.length > 0) {
      terminationProcess.milestones[0].status = 'completed';
      terminationProcess.milestones[0].completedDate = new Date().toISOString();
    }
  }

  /**
   * Helper methods
   */
  calculateDeadline(baseDate, daysToAdd) {
    const deadline = new Date(baseDate);
    deadline.setDate(deadline.getDate() + daysToAdd);
    return deadline.toISOString();
  }

  calculateInvestigationDeadline() {
    const deadline = new Date();
    deadline.setDate(deadline.getDate() + 7); // 7 days for investigation
    return deadline.toISOString();
  }

  initiateBreachInvestigation(breachReport) {
    breachReport.investigationStatus = 'in_progress';
    breachReport.investigationStartDate = new Date().toISOString();
  }

  updateBreachHistory(breachReport) {
    const partyId = breachReport.breachingParty;
    
    if (!this.breachHistory.has(partyId)) {
      this.breachHistory.set(partyId, {
        partyId,
        totalBreaches: 0,
        breachTypes: {},
        lastBreachDate: null,
        breachTrend: 'stable'
      });
    }

    const history = this.breachHistory.get(partyId);
    history.totalBreaches++;
    history.breachTypes[breachReport.breachCategory] = (history.breachTypes[breachReport.breachCategory] || 0) + 1;
    history.lastBreachDate = breachReport.breachDate;
  }

  scheduleCureDeadlineMonitoring(cureNotice) {
    // Implementation for monitoring cure deadlines
    console.log(`Monitoring cure deadline for notice ${cureNotice.id}`);
  }

  scheduleTerminationMilestones(terminationProcess) {
    // Implementation for monitoring termination milestones
    console.log(`Monitoring termination milestones for process ${terminationProcess.id}`);
  }

  /**
   * Generate unique IDs
   */
  generateBreachId() {
    return 'breach_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  generateCureNoticeId() {
    return 'cure_notice_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  generateTerminationId() {
    return 'termination_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  generateSettlementId() {
    return 'settlement_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  /**
   * Get all active breach reports
   */
  getActiveBreaches() {
    return Array.from(this.breachReports.values()).filter(breach => 
      breach.status !== 'resolved' && breach.status !== 'dismissed'
    );
  }

  /**
   * Get pending cure notices
   */
  getPendingCureNotices() {
    return Array.from(this.cureNotices.values()).filter(notice => 
      notice.status === 'issued' && !notice.cureCompleted
    );
  }

  /**
   * Get active termination processes
   */
  getActiveTerminations() {
    return Array.from(this.terminationProcesses.values()).filter(process => 
      process.status === 'in_progress'
    );
  }

  /**
   * Export termination data
   */
  exportTerminationData() {
    return {
      breachReports: Array.from(this.breachReports.values()),
      cureNotices: Array.from(this.cureNotices.values()),
      terminationProcesses: Array.from(this.terminationProcesses.values()),
      settlementCalculations: Array.from(this.settlementCalculations.values()),
      breachHistory: Array.from(this.breachHistory.values()),
      exportedAt: new Date().toISOString()
    };
  }
}

// Export the termination and breach handling system
export const terminationBreachHandling = new TerminationBreachHandling();
