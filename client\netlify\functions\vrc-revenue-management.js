// VRC Revenue Management API
// Backend Specialist: Venture Revenue Commission tracking and distribution system
const { createClient } = require('@supabase/supabase-js');

// Supabase client
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_KEY
);

// Helper function to get user from request
const getUserFromRequest = (event) => {
  const authHeader = event.headers.authorization;
  if (!authHeader) return null;
  
  try {
    const token = authHeader.replace('Bearer ', '');
    const payload = JSON.parse(Buffer.from(token.split('.')[1], 'base64').toString());
    return payload.sub;
  } catch {
    return null;
  }
};

// Helper function to calculate revenue distribution
const calculateRevenueDistribution = (totalRevenue, revenueModel, contributorShares = {}) => {
  const distribution = {
    alliance_share: 0,
    platform_share: 0,
    contributor_shares: {},
    total_distributed: 0
  };

  if (!revenueModel || !revenueModel.distribution) {
    // Default distribution if not specified
    distribution.alliance_share = totalRevenue * 0.70; // 70% to alliance
    distribution.platform_share = totalRevenue * 0.30; // 30% to platform
    distribution.total_distributed = totalRevenue;
    return distribution;
  }

  const { alliance_share, platform_share, contributor_share } = revenueModel.distribution;

  // Calculate base shares
  distribution.alliance_share = totalRevenue * (alliance_share / 100);
  distribution.platform_share = totalRevenue * (platform_share / 100);
  
  // Calculate contributor distributions
  const totalContributorAmount = totalRevenue * (contributor_share / 100);
  const totalContributorShares = Object.values(contributorShares).reduce((sum, share) => sum + share, 0);

  if (totalContributorShares > 0) {
    Object.entries(contributorShares).forEach(([userId, share]) => {
      distribution.contributor_shares[userId] = (totalContributorAmount * share) / totalContributorShares;
    });
  }

  distribution.total_distributed = distribution.alliance_share + distribution.platform_share + 
    Object.values(distribution.contributor_shares).reduce((sum, amount) => sum + amount, 0);

  return distribution;
};

// Record Revenue Transaction
const recordRevenue = async (event) => {
  try {
    const userId = getUserFromRequest(event);
    if (!userId) {
      return {
        statusCode: 401,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    const data = JSON.parse(event.body);
    
    // Validate required fields
    if (!data.venture_id || !data.amount || !data.source) {
      return {
        statusCode: 400,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          error: 'venture_id, amount, and source are required' 
        })
      };
    }

    // Validate amount
    const amount = parseFloat(data.amount);
    if (isNaN(amount) || amount <= 0) {
      return {
        statusCode: 400,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Amount must be a positive number' })
      };
    }

    // Get venture details and check permissions
    const { data: venture, error: ventureError } = await supabase
      .from('projects')
      .select(`
        id,
        name,
        alliance_id,
        revenue_model,
        created_by,
        alliance:teams!projects_alliance_id_fkey(
          id,
          name,
          business_model
        )
      `)
      .eq('id', data.venture_id)
      .single();

    if (ventureError || !venture) {
      return {
        statusCode: 404,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Venture not found' })
      };
    }

    // Check if user has permission to record revenue
    const { data: allianceMember } = await supabase
      .from('team_members')
      .select('role, status')
      .eq('team_id', venture.alliance_id)
      .eq('user_id', userId)
      .eq('status', 'active')
      .single();

    const { data: ventureContributor } = await supabase
      .from('project_contributors')
      .select('role, status')
      .eq('project_id', data.venture_id)
      .eq('user_id', userId)
      .eq('status', 'active')
      .single();

    const hasPermission = (allianceMember && ['founder', 'owner', 'admin'].includes(allianceMember.role)) ||
                         (ventureContributor && ['lead', 'admin'].includes(ventureContributor.role)) ||
                         venture.created_by === userId;

    if (!hasPermission) {
      return {
        statusCode: 403,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Insufficient permissions to record revenue' })
      };
    }

    // Get contributor shares for distribution calculation
    const { data: contributors } = await supabase
      .from('project_contributors')
      .select('user_id, contribution_percentage')
      .eq('project_id', data.venture_id)
      .eq('status', 'active');

    const contributorShares = {};
    contributors?.forEach(contributor => {
      contributorShares[contributor.user_id] = contributor.contribution_percentage || 0;
    });

    // Calculate revenue distribution
    const distribution = calculateRevenueDistribution(amount, venture.revenue_model, contributorShares);

    // Record revenue transaction
    const revenueData = {
      venture_id: data.venture_id,
      alliance_id: venture.alliance_id,
      amount: amount,
      currency: data.currency || 'USD',
      source: data.source,
      source_reference: data.source_reference || null,
      transaction_date: data.transaction_date || new Date().toISOString(),
      description: data.description || null,
      recorded_by: userId,
      distribution_data: distribution,
      status: 'pending_distribution'
    };

    const { data: revenueRecord, error: revenueError } = await supabase
      .from('revenue_tracking')
      .insert([revenueData])
      .select()
      .single();

    if (revenueError) {
      throw new Error(`Failed to record revenue: ${revenueError.message}`);
    }

    // Create distribution records for each recipient
    const distributionRecords = [];

    // Alliance share
    if (distribution.alliance_share > 0) {
      distributionRecords.push({
        revenue_id: revenueRecord.id,
        recipient_type: 'alliance',
        recipient_id: venture.alliance_id,
        amount: distribution.alliance_share,
        currency: data.currency || 'USD',
        status: 'pending'
      });
    }

    // Platform share
    if (distribution.platform_share > 0) {
      distributionRecords.push({
        revenue_id: revenueRecord.id,
        recipient_type: 'platform',
        recipient_id: null,
        amount: distribution.platform_share,
        currency: data.currency || 'USD',
        status: 'pending'
      });
    }

    // Contributor shares
    Object.entries(distribution.contributor_shares).forEach(([contributorId, shareAmount]) => {
      if (shareAmount > 0) {
        distributionRecords.push({
          revenue_id: revenueRecord.id,
          recipient_type: 'user',
          recipient_id: contributorId,
          amount: shareAmount,
          currency: data.currency || 'USD',
          status: 'pending'
        });
      }
    });

    // Insert distribution records
    if (distributionRecords.length > 0) {
      const { error: distributionError } = await supabase
        .from('revenue_distributions')
        .insert(distributionRecords);

      if (distributionError) {
        console.error('Failed to create distribution records:', distributionError);
      }
    }

    return {
      statusCode: 201,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        revenue_record: revenueRecord,
        distribution: distribution,
        distribution_records_created: distributionRecords.length
      })
    };

  } catch (error) {
    console.error('Record revenue error:', error);
    return {
      statusCode: 500,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ error: 'Failed to record revenue' })
    };
  }
};

// Get Revenue Dashboard
const getRevenueDashboard = async (event) => {
  try {
    const userId = getUserFromRequest(event);
    if (!userId) {
      return {
        statusCode: 401,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    const queryParams = new URLSearchParams(event.queryStringParameters || {});
    const ventureId = queryParams.get('venture_id');
    const allianceId = queryParams.get('alliance_id');
    const period = queryParams.get('period') || 'monthly'; // monthly, quarterly, yearly

    // Build base query conditions
    let revenueQuery = supabase
      .from('revenue_tracking')
      .select(`
        id,
        amount,
        currency,
        source,
        transaction_date,
        description,
        status,
        distribution_data,
        venture:projects!revenue_tracking_venture_id_fkey(
          id,
          name,
          title
        ),
        alliance:teams!revenue_tracking_alliance_id_fkey(
          id,
          name
        )
      `)
      .order('transaction_date', { ascending: false });

    // Apply filters
    if (ventureId) {
      revenueQuery = revenueQuery.eq('venture_id', ventureId);
    } else if (allianceId) {
      revenueQuery = revenueQuery.eq('alliance_id', allianceId);
    } else {
      // Get user's accessible revenue records
      const { data: userAlliances } = await supabase
        .from('team_members')
        .select('team_id')
        .eq('user_id', userId)
        .eq('status', 'active');

      const allianceIds = userAlliances?.map(m => m.team_id) || [];
      
      if (allianceIds.length === 0) {
        return {
          statusCode: 200,
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            revenue_records: [],
            summary: { total_revenue: 0, total_distributions: 0, pending_distributions: 0 },
            period_analysis: {}
          })
        };
      }

      revenueQuery = revenueQuery.in('alliance_id', allianceIds);
    }

    // Apply date filter based on period
    const now = new Date();
    let startDate;
    
    switch (period) {
      case 'monthly':
        startDate = new Date(now.getFullYear(), now.getMonth(), 1);
        break;
      case 'quarterly':
        const quarter = Math.floor(now.getMonth() / 3);
        startDate = new Date(now.getFullYear(), quarter * 3, 1);
        break;
      case 'yearly':
        startDate = new Date(now.getFullYear(), 0, 1);
        break;
      default:
        startDate = new Date(now.getFullYear(), now.getMonth(), 1);
    }

    revenueQuery = revenueQuery.gte('transaction_date', startDate.toISOString());

    const { data: revenueRecords, error: revenueError } = await revenueQuery.limit(100);

    if (revenueError) {
      throw new Error(`Failed to fetch revenue records: ${revenueError.message}`);
    }

    // Get user's distribution records
    const { data: userDistributions, error: distributionError } = await supabase
      .from('revenue_distributions')
      .select(`
        id,
        amount,
        currency,
        status,
        distributed_at,
        revenue:revenue_tracking!revenue_distributions_revenue_id_fkey(
          id,
          source,
          transaction_date,
          venture:projects!revenue_tracking_venture_id_fkey(
            id,
            name
          )
        )
      `)
      .eq('recipient_type', 'user')
      .eq('recipient_id', userId)
      .gte('revenue.transaction_date', startDate.toISOString())
      .order('revenue.transaction_date', { ascending: false });

    if (distributionError) {
      console.warn('Failed to fetch user distributions:', distributionError);
    }

    // Calculate summary statistics
    const totalRevenue = revenueRecords?.reduce((sum, record) => sum + record.amount, 0) || 0;
    const totalUserDistributions = userDistributions?.reduce((sum, dist) => sum + dist.amount, 0) || 0;
    const pendingDistributions = userDistributions?.filter(d => d.status === 'pending').length || 0;

    // Analyze revenue by period
    const periodAnalysis = {};
    revenueRecords?.forEach(record => {
      const date = new Date(record.transaction_date);
      const key = period === 'monthly' ? 
        `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}` :
        period === 'quarterly' ?
        `${date.getFullYear()}-Q${Math.floor(date.getMonth() / 3) + 1}` :
        `${date.getFullYear()}`;

      if (!periodAnalysis[key]) {
        periodAnalysis[key] = { revenue: 0, transactions: 0 };
      }
      periodAnalysis[key].revenue += record.amount;
      periodAnalysis[key].transactions += 1;
    });

    return {
      statusCode: 200,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        revenue_records: revenueRecords || [],
        user_distributions: userDistributions || [],
        summary: {
          total_revenue: totalRevenue,
          total_user_distributions: totalUserDistributions,
          pending_distributions: pendingDistributions,
          period: period
        },
        period_analysis: periodAnalysis
      })
    };

  } catch (error) {
    console.error('Get revenue dashboard error:', error);
    return {
      statusCode: 500,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ error: 'Failed to fetch revenue dashboard' })
    };
  }
};

// Process Revenue Distribution
const processDistribution = async (event) => {
  try {
    const userId = getUserFromRequest(event);
    if (!userId) {
      return {
        statusCode: 401,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    const distributionId = event.path.split('/').pop();
    const data = JSON.parse(event.body);

    // Get distribution record
    const { data: distribution, error: distributionError } = await supabase
      .from('revenue_distributions')
      .select(`
        id,
        revenue_id,
        recipient_type,
        recipient_id,
        amount,
        currency,
        status,
        revenue:revenue_tracking!revenue_distributions_revenue_id_fkey(
          id,
          venture_id,
          alliance_id
        )
      `)
      .eq('id', distributionId)
      .single();

    if (distributionError || !distribution) {
      return {
        statusCode: 404,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Distribution record not found' })
      };
    }

    // Check if user has permission to process distributions
    const { data: allianceMember } = await supabase
      .from('team_members')
      .select('role, status')
      .eq('team_id', distribution.revenue.alliance_id)
      .eq('user_id', userId)
      .eq('status', 'active')
      .single();

    if (!allianceMember || !['founder', 'owner', 'admin'].includes(allianceMember.role)) {
      return {
        statusCode: 403,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Insufficient permissions to process distributions' })
      };
    }

    // Validate status transition
    const validStatuses = ['completed', 'failed', 'cancelled'];
    if (!data.status || !validStatuses.includes(data.status)) {
      return {
        statusCode: 400,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Invalid status. Must be completed, failed, or cancelled' })
      };
    }

    // Update distribution status
    const updateData = {
      status: data.status,
      processed_by: userId,
      processed_at: new Date().toISOString(),
      transaction_reference: data.transaction_reference || null,
      notes: data.notes || null
    };

    if (data.status === 'completed') {
      updateData.distributed_at = new Date().toISOString();
    }

    const { data: updatedDistribution, error: updateError } = await supabase
      .from('revenue_distributions')
      .update(updateData)
      .eq('id', distributionId)
      .select()
      .single();

    if (updateError) {
      throw new Error(`Failed to update distribution: ${updateError.message}`);
    }

    return {
      statusCode: 200,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ distribution: updatedDistribution })
    };

  } catch (error) {
    console.error('Process distribution error:', error);
    return {
      statusCode: 500,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ error: 'Failed to process distribution' })
    };
  }
};

// Route requests to appropriate handlers
exports.handler = async (event, context) => {
  // Add CORS headers
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS'
  };

  // Handle preflight requests
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers,
      body: ''
    };
  }

  const path = event.path.replace('/.netlify/functions/vrc-revenue-management', '');

  try {
    let response;

    if (event.httpMethod === 'GET') {
      if (path === '/dashboard' || path === '/dashboard/') {
        response = await getRevenueDashboard(event);
      } else {
        response = {
          statusCode: 404,
          body: JSON.stringify({ error: 'Endpoint not found' })
        };
      }
    } else if (event.httpMethod === 'POST') {
      if (path === '/record' || path === '/record/') {
        response = await recordRevenue(event);
      } else {
        response = {
          statusCode: 404,
          body: JSON.stringify({ error: 'Endpoint not found' })
        };
      }
    } else if (event.httpMethod === 'PUT') {
      if (path.includes('/distributions/')) {
        response = await processDistribution(event);
      } else {
        response = {
          statusCode: 404,
          body: JSON.stringify({ error: 'Endpoint not found' })
        };
      }
    } else {
      response = {
        statusCode: 405,
        body: JSON.stringify({ error: 'Method not allowed' })
      };
    }

    // Add CORS headers to response
    response.headers = { ...response.headers, ...headers };
    return response;

  } catch (error) {
    console.error('VRC Revenue Management API error:', error);
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ error: 'Internal server error' })
    };
  }
};
