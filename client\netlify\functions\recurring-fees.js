// CRITICAL COMPLIANCE: Recurring Fee Management API
// Day 3 - Developer 3: VRC talent fee and subscription management

const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_ANON_KEY
);

// CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS'
};

// Get user from authorization header
async function getUser(authHeader) {
  if (!authHeader?.startsWith('Bearer ')) {
    throw new Error('Missing or invalid authorization header');
  }

  const token = authHeader.substring(7);
  const { data: { user }, error } = await supabase.auth.getUser(token);

  if (error || !user) {
    throw new Error('Invalid authentication token');
  }

  return user;
}

// Validation functions
function validateRecurringFeeData(data) {
  const errors = [];

  // Required fields
  if (!data.company_id) errors.push('Company ID is required');
  if (!data.payee_user_id) errors.push('Payee user ID is required');
  if (!data.fee_type) errors.push('Fee type is required');
  if (!data.amount || data.amount <= 0) errors.push('Valid amount is required');
  if (!data.frequency) errors.push('Frequency is required');
  if (!data.start_date) errors.push('Start date is required');
  if (!data.description?.trim()) errors.push('Description is required');

  // Validate fee type
  const validFeeTypes = ['talent_fee', 'subscription', 'retainer', 'maintenance'];
  if (data.fee_type && !validFeeTypes.includes(data.fee_type)) {
    errors.push('Invalid fee type');
  }

  // Validate frequency
  const validFrequencies = ['weekly', 'monthly', 'quarterly', 'annually'];
  if (data.frequency && !validFrequencies.includes(data.frequency)) {
    errors.push('Invalid frequency');
  }

  // Validate amounts
  if (data.amount && (isNaN(data.amount) || data.amount <= 0)) {
    errors.push('Amount must be a positive number');
  }

  // Validate dates
  if (data.start_date && new Date(data.start_date) < new Date()) {
    errors.push('Start date cannot be in the past');
  }

  if (data.end_date && data.start_date && new Date(data.end_date) <= new Date(data.start_date)) {
    errors.push('End date must be after start date');
  }

  return errors;
}

// Calculate next payment date based on frequency
function calculateNextPaymentDate(startDate, frequency) {
  const date = new Date(startDate);

  switch (frequency) {
    case 'weekly':
      date.setDate(date.getDate() + 7);
      break;
    case 'monthly':
      date.setMonth(date.getMonth() + 1);
      break;
    case 'quarterly':
      date.setMonth(date.getMonth() + 3);
      break;
    case 'annually':
      date.setFullYear(date.getFullYear() + 1);
      break;
    default:
      throw new Error('Invalid frequency');
  }

  return date.toISOString().split('T')[0]; // Return YYYY-MM-DD format
}

// Check if user has permission to manage recurring fees for company
async function checkRecurringFeePermission(userId, companyId) {
  const { data, error } = await supabase
    .from('teams')
    .select(`
      id,
      team_members!inner(user_id, is_admin)
    `)
    .eq('company_id', companyId)
    .eq('team_members.user_id', userId)
    .eq('team_members.is_admin', true);

  if (error) {
    console.error('Error checking recurring fee permission:', error);
    return false;
  }

  return data && data.length > 0;
}

// Main handler function
exports.handler = async (event, context) => {
  // Handle CORS preflight
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers: corsHeaders,
      body: ''
    };
  }

  try {
    const { httpMethod, path, headers, body } = event;
    const authHeader = headers.authorization || headers.Authorization;

    // Get authenticated user
    const user = await getUser(authHeader);

    // Route handling
    const pathParts = path.split('/').filter(p => p);
    const feeId = pathParts[pathParts.length - 1];

    switch (httpMethod) {
      case 'GET':
        if (feeId && feeId !== 'recurring-fees') {
          // Get specific recurring fee
          return await getRecurringFee(feeId, user.id);
        } else {
          // List recurring fees with filters
          const queryParams = new URLSearchParams(event.queryStringParameters || {});
          return await listRecurringFees(user.id, queryParams);
        }

      case 'POST':
        // Create recurring fee
        const createData = JSON.parse(body);
        return await createRecurringFee(createData, user.id);

      case 'PUT':
        // Update recurring fee (pause, resume, modify, etc.)
        const updateData = JSON.parse(body);
        return await updateRecurringFee(feeId, updateData, user.id);

      case 'DELETE':
        // Cancel recurring fee
        return await cancelRecurringFee(feeId, user.id);

      default:
        return {
          statusCode: 405,
          headers: corsHeaders,
          body: JSON.stringify({ error: 'Method not allowed' })
        };
    }

  } catch (error) {
    console.error('Recurring Fee API Error:', error);
    return {
      statusCode: error.message.includes('authentication') ? 401 : 500,
      headers: corsHeaders,
      body: JSON.stringify({
        error: error.message || 'Internal server error',
        timestamp: new Date().toISOString()
      })
    };
  }
};

// List recurring fees
async function listRecurringFees(userId, queryParams) {
  const companyId = queryParams.get('company_id');
  const payeeId = queryParams.get('payee_user_id');
  const feeType = queryParams.get('fee_type');
  const isActive = queryParams.get('is_active');
  const dueSoon = queryParams.get('due_soon'); // Show fees due in next 7 days
  const limit = Math.min(parseInt(queryParams.get('limit')) || 50, 100);
  const offset = parseInt(queryParams.get('offset')) || 0;

  let query = supabase
    .from('recurring_fees')
    .select('*')
    .order('next_payment_date', { ascending: true })
    .range(offset, offset + limit - 1);

  // Apply filters
  if (companyId) query = query.eq('company_id', companyId);
  if (payeeId) query = query.eq('payee_user_id', payeeId);
  if (feeType) query = query.eq('fee_type', feeType);
  if (isActive !== null) query = query.eq('is_active', isActive === 'true');

  if (dueSoon === 'true') {
    const nextWeek = new Date();
    nextWeek.setDate(nextWeek.getDate() + 7);
    query = query.lte('next_payment_date', nextWeek.toISOString().split('T')[0]);
  }

  const { data, error, count } = await query;

  if (error) {
    throw new Error(`Failed to fetch recurring fees: ${error.message}`);
  }

  return {
    statusCode: 200,
    headers: corsHeaders,
    body: JSON.stringify({
      recurringFees: data,
      count: count,
      pagination: {
        limit,
        offset,
        hasMore: count > offset + limit
      },
      timestamp: new Date().toISOString()
    })
  };
}

// Get specific recurring fee
async function getRecurringFee(feeId, userId) {
  const { data, error } = await supabase
    .from('recurring_fees')
    .select('*')
    .eq('id', feeId)
    .single();

  if (error) {
    if (error.code === 'PGRST116') {
      return {
        statusCode: 404,
        headers: corsHeaders,
        body: JSON.stringify({ error: 'Recurring fee not found' })
      };
    }
    throw new Error(`Failed to fetch recurring fee: ${error.message}`);
  }

  return {
    statusCode: 200,
    headers: corsHeaders,
    body: JSON.stringify({
      recurringFee: data,
      timestamp: new Date().toISOString()
    })
  };
}

// Create new recurring fee
async function createRecurringFee(data, userId) {
  // Validate input data
  const errors = validateRecurringFeeData(data);
  if (errors.length > 0) {
    return {
      statusCode: 400,
      headers: corsHeaders,
      body: JSON.stringify({
        error: 'Validation failed',
        details: errors
      })
    };
  }

  // Check permission
  const hasPermission = await checkRecurringFeePermission(userId, data.company_id);
  if (!hasPermission) {
    return {
      statusCode: 403,
      headers: corsHeaders,
      body: JSON.stringify({
        error: 'Insufficient permissions to create recurring fees for this company'
      })
    };
  }

  // Calculate next payment date
  const nextPaymentDate = calculateNextPaymentDate(data.start_date, data.frequency);

  // Prepare recurring fee data
  const recurringFeeData = {
    company_id: data.company_id,
    payee_user_id: data.payee_user_id,
    fee_type: data.fee_type,
    amount: parseFloat(data.amount),
    currency: data.currency || 'USD',
    frequency: data.frequency,
    start_date: data.start_date,
    end_date: data.end_date || null,
    next_payment_date: nextPaymentDate,
    is_active: true,
    description: data.description.trim(),
    created_by: userId
  };

  const { data: recurringFee, error } = await supabase
    .from('recurring_fees')
    .insert(recurringFeeData)
    .select('*')
    .single();

  if (error) {
    throw new Error(`Failed to create recurring fee: ${error.message}`);
  }

  return {
    statusCode: 201,
    headers: corsHeaders,
    body: JSON.stringify({
      recurringFee,
      message: 'Recurring fee created successfully',
      timestamp: new Date().toISOString()
    })
  };
}

// Update recurring fee
async function updateRecurringFee(feeId, data, userId) {
  // Get existing fee
  const { data: existingFee, error: fetchError } = await supabase
    .from('recurring_fees')
    .select('*, company_id')
    .eq('id', feeId)
    .single();

  if (fetchError) {
    if (fetchError.code === 'PGRST116') {
      return {
        statusCode: 404,
        headers: corsHeaders,
        body: JSON.stringify({ error: 'Recurring fee not found' })
      };
    }
    throw new Error(`Failed to fetch recurring fee: ${fetchError.message}`);
  }

  // Check permission
  const hasPermission = await checkRecurringFeePermission(userId, existingFee.company_id);
  if (!hasPermission) {
    return {
      statusCode: 403,
      headers: corsHeaders,
      body: JSON.stringify({
        error: 'Insufficient permissions to update this recurring fee'
      })
    };
  }

  // Prepare update data
  const updateData = {};

  // Handle specific actions
  if (data.action === 'pause') {
    updateData.is_active = false;
    updateData.paused_until = data.paused_until;
  } else if (data.action === 'resume') {
    updateData.is_active = true;
    updateData.paused_until = null;
    // Recalculate next payment date
    updateData.next_payment_date = calculateNextPaymentDate(new Date().toISOString().split('T')[0], existingFee.frequency);
  } else if (data.action === 'modify') {
    // Allow modification of amount, frequency, end date
    if (data.amount) updateData.amount = parseFloat(data.amount);
    if (data.frequency) {
      updateData.frequency = data.frequency;
      // Recalculate next payment date with new frequency
      updateData.next_payment_date = calculateNextPaymentDate(existingFee.next_payment_date, data.frequency);
    }
    if (data.end_date !== undefined) updateData.end_date = data.end_date;
    if (data.description) updateData.description = data.description;
  }

  const { data: recurringFee, error } = await supabase
    .from('recurring_fees')
    .update(updateData)
    .eq('id', feeId)
    .select('*')
    .single();

  if (error) {
    throw new Error(`Failed to update recurring fee: ${error.message}`);
  }

  return {
    statusCode: 200,
    headers: corsHeaders,
    body: JSON.stringify({
      recurringFee,
      message: 'Recurring fee updated successfully',
      timestamp: new Date().toISOString()
    })
  };
}

// Cancel recurring fee
async function cancelRecurringFee(feeId, userId) {
  // Get existing fee
  const { data: existingFee, error: fetchError } = await supabase
    .from('recurring_fees')
    .select('*, company_id')
    .eq('id', feeId)
    .single();

  if (fetchError) {
    if (fetchError.code === 'PGRST116') {
      return {
        statusCode: 404,
        headers: corsHeaders,
        body: JSON.stringify({ error: 'Recurring fee not found' })
      };
    }
    throw new Error(`Failed to fetch recurring fee: ${fetchError.message}`);
  }

  // Check permission
  const hasPermission = await checkRecurringFeePermission(userId, existingFee.company_id);
  if (!hasPermission) {
    return {
      statusCode: 403,
      headers: corsHeaders,
      body: JSON.stringify({
        error: 'Insufficient permissions to cancel this recurring fee'
      })
    };
  }

  // Cancel the recurring fee
  const { data: recurringFee, error } = await supabase
    .from('recurring_fees')
    .update({
      is_active: false,
      end_date: new Date().toISOString().split('T')[0]
    })
    .eq('id', feeId)
    .select('*')
    .single();

  if (error) {
    throw new Error(`Failed to cancel recurring fee: ${error.message}`);
  }

  return {
    statusCode: 200,
    headers: corsHeaders,
    body: JSON.stringify({
      recurringFee,
      message: 'Recurring fee cancelled successfully',
      timestamp: new Date().toISOString()
    })
  };
}
