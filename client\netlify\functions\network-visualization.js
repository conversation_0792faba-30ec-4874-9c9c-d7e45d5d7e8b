// Network Visualization API
// Backend Specialist: Network analysis and visualization data for social graphs
const { createClient } = require('@supabase/supabase-js');

// Supabase client
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_KEY
);

// Helper function to get user from request
const getUserFromRequest = (event) => {
  const authHeader = event.headers.authorization;
  if (!authHeader) return null;
  
  try {
    const token = authHeader.replace('Bearer ', '');
    const payload = JSON.parse(Buffer.from(token.split('.')[1], 'base64').toString());
    return payload.sub;
  } catch {
    return null;
  }
};

// Get Network Graph Data
const getNetworkGraph = async (event) => {
  try {
    const userId = getUserFromRequest(event);
    if (!userId) {
      return {
        statusCode: 401,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    const queryParams = new URLSearchParams(event.queryStringParameters || {});
    const depth = parseInt(queryParams.get('depth') || '2'); // 1 = direct connections, 2 = friends of friends
    const includeMetrics = queryParams.get('include_metrics') === 'true';
    const filterBySkill = queryParams.get('skill');

    // Get user's direct connections
    const { data: directConnections, error: connectionsError } = await supabase
      .from('user_allies')
      .select(`
        id,
        connection_type,
        accepted_at,
        ally:users!user_allies_ally_id_fkey(
          id,
          display_name,
          avatar_url,
          is_premium
        ),
        user:users!user_allies_user_id_fkey(
          id,
          display_name,
          avatar_url,
          is_premium
        )
      `)
      .or(`user_id.eq.${userId},ally_id.eq.${userId}`)
      .eq('status', 'accepted');

    if (connectionsError) {
      throw new Error(`Failed to fetch connections: ${connectionsError.message}`);
    }

    // Build nodes and edges for the network graph
    const nodes = new Map();
    const edges = [];

    // Add the current user as the central node
    const { data: currentUser } = await supabase
      .from('users')
      .select('id, display_name, avatar_url, is_premium')
      .eq('id', userId)
      .single();

    if (currentUser) {
      nodes.set(userId, {
        id: userId,
        label: currentUser.display_name,
        avatar_url: currentUser.avatar_url,
        is_premium: currentUser.is_premium,
        type: 'self',
        size: 30,
        color: '#3B82F6'
      });
    }

    // Add direct connections
    directConnections?.forEach(connection => {
      const ally = connection.user_id === userId ? connection.ally : connection.user;
      
      if (!nodes.has(ally.id)) {
        nodes.set(ally.id, {
          id: ally.id,
          label: ally.display_name,
          avatar_url: ally.avatar_url,
          is_premium: ally.is_premium,
          type: 'direct',
          size: 20,
          color: '#10B981'
        });
      }

      // Add edge between current user and ally
      edges.push({
        id: `${userId}-${ally.id}`,
        source: userId,
        target: ally.id,
        type: connection.connection_type,
        weight: 1,
        color: '#6B7280'
      });
    });

    // If depth > 1, get second-degree connections
    if (depth > 1) {
      const directAllyIds = Array.from(nodes.keys()).filter(id => id !== userId);
      
      if (directAllyIds.length > 0) {
        const { data: secondDegreeConnections } = await supabase
          .from('user_allies')
          .select(`
            user_id,
            ally_id,
            connection_type,
            ally:users!user_allies_ally_id_fkey(
              id,
              display_name,
              avatar_url,
              is_premium
            ),
            user:users!user_allies_user_id_fkey(
              id,
              display_name,
              avatar_url,
              is_premium
            )
          `)
          .in('user_id', directAllyIds)
          .eq('status', 'accepted')
          .limit(100); // Limit to prevent huge graphs

        secondDegreeConnections?.forEach(connection => {
          const ally = connection.ally;
          
          // Don't add if it's the current user or already exists
          if (ally.id !== userId && !nodes.has(ally.id)) {
            nodes.set(ally.id, {
              id: ally.id,
              label: ally.display_name,
              avatar_url: ally.avatar_url,
              is_premium: ally.is_premium,
              type: 'second_degree',
              size: 15,
              color: '#F59E0B'
            });

            // Add edge between first-degree and second-degree connections
            edges.push({
              id: `${connection.user_id}-${ally.id}`,
              source: connection.user_id,
              target: ally.id,
              type: connection.connection_type,
              weight: 0.5,
              color: '#D1D5DB'
            });
          }
        });
      }
    }

    // Add metrics if requested
    let networkMetrics = {};
    if (includeMetrics) {
      networkMetrics = await calculateNetworkMetrics(Array.from(nodes.values()), edges, userId);
    }

    // Apply skill filter if specified
    let filteredNodes = Array.from(nodes.values());
    let filteredEdges = edges;

    if (filterBySkill) {
      const { data: usersWithSkill } = await supabase
        .from('user_skills')
        .select('user_id')
        .eq('skill_name', filterBySkill)
        .gte('proficiency_score', 50);

      const skillUserIds = new Set(usersWithSkill?.map(us => us.user_id) || []);
      
      filteredNodes = filteredNodes.filter(node => 
        node.id === userId || skillUserIds.has(node.id)
      );
      
      const filteredNodeIds = new Set(filteredNodes.map(n => n.id));
      filteredEdges = edges.filter(edge => 
        filteredNodeIds.has(edge.source) && filteredNodeIds.has(edge.target)
      );
    }

    return {
      statusCode: 200,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        nodes: filteredNodes,
        edges: filteredEdges,
        metrics: networkMetrics,
        total_nodes: filteredNodes.length,
        total_edges: filteredEdges.length,
        depth: depth,
        center_user_id: userId
      })
    };

  } catch (error) {
    console.error('Get network graph error:', error);
    return {
      statusCode: 500,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ error: 'Failed to fetch network graph' })
    };
  }
};

// Get Network Clusters
const getNetworkClusters = async (event) => {
  try {
    const userId = getUserFromRequest(event);
    if (!userId) {
      return {
        statusCode: 401,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    const queryParams = new URLSearchParams(event.queryStringParameters || {});
    const clusterBy = queryParams.get('cluster_by') || 'skill'; // 'skill', 'project', 'alliance'

    // Get user's network
    const { data: connections } = await supabase
      .from('user_allies')
      .select(`
        ally_id,
        user_id,
        ally:users!user_allies_ally_id_fkey(
          id,
          display_name,
          avatar_url
        ),
        user:users!user_allies_user_id_fkey(
          id,
          display_name,
          avatar_url
        )
      `)
      .or(`user_id.eq.${userId},ally_id.eq.${userId}`)
      .eq('status', 'accepted');

    const allyIds = connections?.map(conn => 
      conn.user_id === userId ? conn.ally_id : conn.user_id
    ) || [];

    if (allyIds.length === 0) {
      return {
        statusCode: 200,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ clusters: [], total_clusters: 0 })
      };
    }

    let clusters = [];

    if (clusterBy === 'skill') {
      // Cluster by shared skills
      const { data: userSkills } = await supabase
        .from('user_skills')
        .select(`
          user_id,
          skill_name,
          proficiency_score,
          user:users(
            id,
            display_name,
            avatar_url
          )
        `)
        .in('user_id', [...allyIds, userId])
        .gte('proficiency_score', 50);

      const skillClusters = {};
      userSkills?.forEach(userSkill => {
        if (!skillClusters[userSkill.skill_name]) {
          skillClusters[userSkill.skill_name] = [];
        }
        skillClusters[userSkill.skill_name].push({
          user: userSkill.user,
          proficiency_score: userSkill.proficiency_score
        });
      });

      clusters = Object.entries(skillClusters)
        .filter(([skill, users]) => users.length >= 2) // At least 2 users
        .map(([skill, users]) => ({
          id: `skill-${skill}`,
          name: skill,
          type: 'skill',
          members: users,
          size: users.length,
          density: calculateClusterDensity(users, connections)
        }))
        .sort((a, b) => b.size - a.size);

    } else if (clusterBy === 'project') {
      // Cluster by shared projects
      const { data: projectContributors } = await supabase
        .from('project_contributors')
        .select(`
          user_id,
          project_id,
          project:projects(
            id,
            name,
            title
          ),
          user:users(
            id,
            display_name,
            avatar_url
          )
        `)
        .in('user_id', [...allyIds, userId]);

      const projectClusters = {};
      projectContributors?.forEach(contributor => {
        const projectKey = contributor.project.id;
        if (!projectClusters[projectKey]) {
          projectClusters[projectKey] = {
            project: contributor.project,
            members: []
          };
        }
        projectClusters[projectKey].members.push(contributor.user);
      });

      clusters = Object.values(projectClusters)
        .filter(cluster => cluster.members.length >= 2)
        .map(cluster => ({
          id: `project-${cluster.project.id}`,
          name: cluster.project.title || cluster.project.name,
          type: 'project',
          members: cluster.members,
          size: cluster.members.length,
          density: calculateClusterDensity(cluster.members, connections)
        }))
        .sort((a, b) => b.size - a.size);

    } else if (clusterBy === 'alliance') {
      // Cluster by shared alliances
      const { data: allianceMembers } = await supabase
        .from('team_members')
        .select(`
          user_id,
          team_id,
          team:teams(
            id,
            name
          ),
          user:users(
            id,
            display_name,
            avatar_url
          )
        `)
        .in('user_id', [...allyIds, userId]);

      const allianceClusters = {};
      allianceMembers?.forEach(member => {
        const allianceKey = member.team.id;
        if (!allianceClusters[allianceKey]) {
          allianceClusters[allianceKey] = {
            alliance: member.team,
            members: []
          };
        }
        allianceClusters[allianceKey].members.push(member.user);
      });

      clusters = Object.values(allianceClusters)
        .filter(cluster => cluster.members.length >= 2)
        .map(cluster => ({
          id: `alliance-${cluster.alliance.id}`,
          name: cluster.alliance.name,
          type: 'alliance',
          members: cluster.members,
          size: cluster.members.length,
          density: calculateClusterDensity(cluster.members, connections)
        }))
        .sort((a, b) => b.size - a.size);
    }

    return {
      statusCode: 200,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        clusters: clusters,
        total_clusters: clusters.length,
        cluster_by: clusterBy
      })
    };

  } catch (error) {
    console.error('Get network clusters error:', error);
    return {
      statusCode: 500,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ error: 'Failed to fetch network clusters' })
    };
  }
};

// Get Network Insights
const getNetworkInsights = async (event) => {
  try {
    const userId = getUserFromRequest(event);
    if (!userId) {
      return {
        statusCode: 401,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    // Get latest network analytics
    const { data: analytics } = await supabase
      .from('network_analytics')
      .select('*')
      .eq('user_id', userId)
      .order('period_start', { ascending: false })
      .limit(1)
      .single();

    // Get network growth over time
    const { data: historicalAnalytics } = await supabase
      .from('network_analytics')
      .select('total_connections, network_influence_score, period_start')
      .eq('user_id', userId)
      .order('period_start', { ascending: false })
      .limit(12);

    // Calculate insights
    const insights = {
      network_health: calculateNetworkHealth(analytics),
      growth_trend: calculateGrowthTrend(historicalAnalytics),
      influence_level: categorizeInfluenceLevel(analytics?.network_influence_score || 0),
      connection_quality: categorizeConnectionQuality(analytics),
      diversity_score: analytics?.skill_diversity_in_network || 0,
      recommendations: generateNetworkRecommendations(analytics)
    };

    // Get key connections (most influential allies)
    const { data: keyConnections } = await supabase
      .from('user_allies')
      .select(`
        ally_id,
        user_id,
        connection_type,
        ally:users!user_allies_ally_id_fkey(
          id,
          display_name,
          avatar_url,
          is_premium
        ),
        user:users!user_allies_user_id_fkey(
          id,
          display_name,
          avatar_url,
          is_premium
        )
      `)
      .or(`user_id.eq.${userId},ally_id.eq.${userId}`)
      .eq('status', 'accepted')
      .limit(10);

    // Enhance key connections with influence scores
    const enhancedConnections = await Promise.all(
      (keyConnections || []).map(async (connection) => {
        const ally = connection.user_id === userId ? connection.ally : connection.user;
        
        // Get ally's network influence
        const { data: allyAnalytics } = await supabase
          .from('network_analytics')
          .select('network_influence_score, total_connections')
          .eq('user_id', ally.id)
          .order('period_start', { ascending: false })
          .limit(1)
          .single();

        return {
          ally: ally,
          connection_type: connection.connection_type,
          influence_score: allyAnalytics?.network_influence_score || 0,
          network_size: allyAnalytics?.total_connections || 0
        };
      })
    );

    // Sort by influence score
    enhancedConnections.sort((a, b) => b.influence_score - a.influence_score);

    return {
      statusCode: 200,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        insights: insights,
        current_analytics: analytics,
        historical_data: historicalAnalytics || [],
        key_connections: enhancedConnections.slice(0, 5),
        generated_at: new Date().toISOString()
      })
    };

  } catch (error) {
    console.error('Get network insights error:', error);
    return {
      statusCode: 500,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ error: 'Failed to fetch network insights' })
    };
  }
};

// Helper functions
const calculateNetworkMetrics = async (nodes, edges, centerUserId) => {
  const totalNodes = nodes.length;
  const totalEdges = edges.length;
  
  // Calculate basic metrics
  const density = totalNodes > 1 ? (2 * totalEdges) / (totalNodes * (totalNodes - 1)) : 0;
  const avgDegree = totalNodes > 0 ? (2 * totalEdges) / totalNodes : 0;
  
  // Calculate centrality for center user
  const centerNodeEdges = edges.filter(e => e.source === centerUserId || e.target === centerUserId);
  const degreeCentrality = centerNodeEdges.length / (totalNodes - 1);
  
  return {
    total_nodes: totalNodes,
    total_edges: totalEdges,
    network_density: density,
    average_degree: avgDegree,
    user_degree_centrality: degreeCentrality,
    clustering_coefficient: 0 // Simplified for now
  };
};

const calculateClusterDensity = (members, connections) => {
  if (members.length < 2) return 0;
  
  const memberIds = new Set(members.map(m => m.id));
  const internalConnections = connections?.filter(conn => 
    memberIds.has(conn.user_id) && memberIds.has(conn.ally_id)
  ).length || 0;
  
  const maxPossibleConnections = (members.length * (members.length - 1)) / 2;
  return maxPossibleConnections > 0 ? internalConnections / maxPossibleConnections : 0;
};

const calculateNetworkHealth = (analytics) => {
  if (!analytics) return 'unknown';
  
  const score = (analytics.network_density * 30) + 
                (analytics.clustering_coefficient * 30) + 
                (Math.min(analytics.total_connections / 50, 1) * 40);
  
  if (score >= 80) return 'excellent';
  if (score >= 60) return 'good';
  if (score >= 40) return 'fair';
  return 'needs_improvement';
};

const calculateGrowthTrend = (historicalData) => {
  if (!historicalData || historicalData.length < 2) return 'stable';
  
  const recent = historicalData[0];
  const previous = historicalData[1];
  const growthRate = ((recent.total_connections - previous.total_connections) / previous.total_connections) * 100;
  
  if (growthRate > 10) return 'rapid_growth';
  if (growthRate > 5) return 'steady_growth';
  if (growthRate > 0) return 'slow_growth';
  if (growthRate === 0) return 'stable';
  return 'declining';
};

const categorizeInfluenceLevel = (influenceScore) => {
  if (influenceScore >= 80) return 'high_influence';
  if (influenceScore >= 60) return 'moderate_influence';
  if (influenceScore >= 40) return 'emerging_influence';
  return 'building_influence';
};

const categorizeConnectionQuality = (analytics) => {
  if (!analytics) return 'unknown';
  
  const strongTieRatio = analytics.strong_ties / (analytics.total_connections || 1);
  
  if (strongTieRatio >= 0.3) return 'high_quality';
  if (strongTieRatio >= 0.2) return 'good_quality';
  if (strongTieRatio >= 0.1) return 'moderate_quality';
  return 'needs_improvement';
};

const generateNetworkRecommendations = (analytics) => {
  const recommendations = [];
  
  if (!analytics) {
    recommendations.push('Build your network by connecting with allies in your field');
    return recommendations;
  }
  
  if (analytics.total_connections < 10) {
    recommendations.push('Expand your network by connecting with more professionals');
  }
  
  if (analytics.skill_diversity_in_network < 50) {
    recommendations.push('Connect with professionals from different skill areas');
  }
  
  if (analytics.strong_ties < 3) {
    recommendations.push('Strengthen relationships with key connections through collaboration');
  }
  
  if (analytics.bridge_connections < 2) {
    recommendations.push('Connect with people from different professional circles');
  }
  
  return recommendations;
};

// Route requests to appropriate handlers
exports.handler = async (event, context) => {
  // Add CORS headers
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS'
  };

  // Handle preflight requests
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers,
      body: ''
    };
  }

  const path = event.path.replace('/.netlify/functions/network-visualization', '');

  try {
    let response;

    if (event.httpMethod === 'GET') {
      if (path === '/graph' || path === '/graph/') {
        response = await getNetworkGraph(event);
      } else if (path === '/clusters' || path === '/clusters/') {
        response = await getNetworkClusters(event);
      } else if (path === '/insights' || path === '/insights/') {
        response = await getNetworkInsights(event);
      } else {
        response = {
          statusCode: 404,
          body: JSON.stringify({ error: 'Endpoint not found' })
        };
      }
    } else {
      response = {
        statusCode: 405,
        body: JSON.stringify({ error: 'Method not allowed' })
      };
    }

    // Add CORS headers to response
    response.headers = { ...response.headers, ...headers };
    return response;

  } catch (error) {
    console.error('Network Visualization API error:', error);
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ error: 'Internal server error' })
    };
  }
};
