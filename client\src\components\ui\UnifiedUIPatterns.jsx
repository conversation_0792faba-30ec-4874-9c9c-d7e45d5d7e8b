import React from 'react';
import { motion } from 'framer-motion';
import { Card, CardBody, CardHeader, Button, Chip, Progress } from '@heroui/react';
import { ArrowRight, TrendingUp, Clock, Users, Star } from 'lucide-react';

/**
 * Unified UI Patterns Component
 * 
 * Provides consistent UI patterns and components across all tiles for:
 * - Standardized card layouts and animations
 * - Consistent color schemes and gradients
 * - Unified typography and spacing
 * - Cross-tile design language
 * - Accessibility and responsive patterns
 */

// Unified color schemes for different tile types
export const tileColorSchemes = {
  track: {
    primary: 'from-slate-900 via-orange-900 to-red-900',
    accent: 'from-orange-500 to-red-500',
    card: 'from-orange-500/20 to-red-500/20',
    border: 'border-orange-500/30',
    text: 'text-orange-400'
  },
  earn: {
    primary: 'from-slate-900 via-yellow-900 to-orange-900',
    accent: 'from-yellow-500 to-orange-500',
    card: 'from-yellow-500/20 to-orange-500/20',
    border: 'border-yellow-500/30',
    text: 'text-yellow-400'
  },
  projects: {
    primary: 'from-slate-900 via-blue-900 to-purple-900',
    accent: 'from-blue-500 to-purple-500',
    card: 'from-blue-500/20 to-purple-500/20',
    border: 'border-blue-500/30',
    text: 'text-blue-400'
  },
  teams: {
    primary: 'from-slate-900 via-green-900 to-teal-900',
    accent: 'from-green-500 to-teal-500',
    card: 'from-green-500/20 to-teal-500/20',
    border: 'border-green-500/30',
    text: 'text-green-400'
  },
  admin: {
    primary: 'from-slate-900 via-purple-900 to-indigo-900',
    accent: 'from-purple-500 to-indigo-500',
    card: 'from-purple-500/20 to-indigo-500/20',
    border: 'border-purple-500/30',
    text: 'text-purple-400'
  },
  system: {
    primary: 'from-slate-900 via-gray-900 to-slate-800',
    accent: 'from-gray-500 to-slate-500',
    card: 'from-gray-500/20 to-slate-500/20',
    border: 'border-gray-500/30',
    text: 'text-gray-400'
  }
};

// Unified animation variants
export const animationVariants = {
  fadeInUp: {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
    exit: { opacity: 0, y: -20 }
  },
  fadeInLeft: {
    initial: { opacity: 0, x: -20 },
    animate: { opacity: 1, x: 0 },
    exit: { opacity: 0, x: 20 }
  },
  scaleIn: {
    initial: { opacity: 0, scale: 0.95 },
    animate: { opacity: 1, scale: 1 },
    exit: { opacity: 0, scale: 0.95 }
  },
  slideInRight: {
    initial: { opacity: 0, x: 20 },
    animate: { opacity: 1, x: 0 },
    exit: { opacity: 0, x: -20 }
  }
};

// Unified Header Component
export const UnifiedHeader = ({ 
  title, 
  subtitle, 
  icon, 
  tileType = 'track',
  className = '' 
}) => {
  const colors = tileColorSchemes[tileType];
  
  return (
    <motion.div
      className={`relative z-10 pt-8 pb-6 ${className}`}
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
    >
      <div className="container mx-auto px-6">
        <div className="text-center mb-8">
          <motion.div
            className="text-6xl mb-4"
            animate={{
              scale: [1, 1.1, 1],
              rotate: [0, 5, -5, 0]
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              repeatType: "reverse"
            }}
          >
            {icon}
          </motion.div>
          <h1 className="text-4xl font-bold text-white mb-2">
            {title}
          </h1>
          <p className="text-white/80 text-lg max-w-2xl mx-auto">
            {subtitle}
          </p>
        </div>
      </div>
    </motion.div>
  );
};

// Unified Metric Card Component
export const UnifiedMetricCard = ({ 
  title, 
  value, 
  subtitle, 
  icon, 
  tileType = 'track',
  trend = null,
  delay = 0,
  className = '' 
}) => {
  const colors = tileColorSchemes[tileType];
  
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay }}
      className={className}
    >
      <Card className={`bg-gradient-to-br ${colors.card} ${colors.border}`}>
        <CardBody className="p-6 text-center">
          {icon && (
            <div className="flex justify-center mb-3">
              {icon}
            </div>
          )}
          <div className={`text-3xl font-bold ${colors.text} mb-2 flex items-center justify-center gap-2`}>
            {value}
            {trend && (
              <span className={`text-sm ${trend > 0 ? 'text-green-400' : 'text-red-400'}`}>
                <TrendingUp className={`h-4 w-4 ${trend < 0 ? 'rotate-180' : ''}`} />
              </span>
            )}
          </div>
          <div className="text-white/80 text-sm font-medium">{title}</div>
          {subtitle && (
            <div className="text-white/60 text-xs mt-1">{subtitle}</div>
          )}
        </CardBody>
      </Card>
    </motion.div>
  );
};

// Unified Action Card Component
export const UnifiedActionCard = ({ 
  title, 
  description, 
  icon, 
  action, 
  buttonText = 'Get Started',
  tileType = 'track',
  size = 'medium',
  className = '' 
}) => {
  const colors = tileColorSchemes[tileType];
  const isLarge = size === 'large';
  
  return (
    <Card className={`bg-gradient-to-br ${colors.card} ${colors.border} hover:border-white/30 transition-all cursor-pointer ${className}`}>
      <CardBody className={`${isLarge ? 'p-8' : 'p-6'} text-center`}>
        <div className={`${isLarge ? 'text-4xl mb-4' : 'text-2xl mb-3'}`}>{icon}</div>
        <h3 className={`${isLarge ? 'text-2xl' : 'text-xl'} font-bold text-white mb-4`}>{title}</h3>
        <p className={`text-white/80 ${isLarge ? 'mb-6' : 'mb-4'}`}>
          {description}
        </p>
        <Button
          size={isLarge ? 'lg' : 'md'}
          className={`bg-gradient-to-r ${colors.accent} text-white font-semibold`}
          onClick={action}
          endContent={<ArrowRight className="h-4 w-4" />}
        >
          {buttonText}
        </Button>
      </CardBody>
    </Card>
  );
};

// Unified Progress Card Component
export const UnifiedProgressCard = ({ 
  title, 
  items, 
  tileType = 'track',
  className = '' 
}) => {
  const colors = tileColorSchemes[tileType];
  
  return (
    <Card className={`bg-white/5 border border-white/10 ${className}`}>
      <CardHeader className="pb-3">
        <h3 className="text-xl font-semibold text-white">{title}</h3>
      </CardHeader>
      <CardBody className="space-y-4">
        {items.map((item, index) => (
          <div key={index}>
            <div className="flex justify-between text-sm mb-2">
              <span className="text-white/70">{item.label}</span>
              <span className="text-white">{item.value}</span>
            </div>
            <Progress
              value={item.percentage}
              color={item.color || 'primary'}
              className="max-w-full"
            />
          </div>
        ))}
      </CardBody>
    </Card>
  );
};

// Unified Status Chip Component
export const UnifiedStatusChip = ({ 
  status, 
  size = 'sm',
  className = '' 
}) => {
  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'completed':
      case 'approved':
      case 'active':
      case 'healthy':
        return 'success';
      case 'pending':
      case 'in_progress':
      case 'warning':
        return 'warning';
      case 'failed':
      case 'rejected':
      case 'error':
      case 'critical':
        return 'danger';
      case 'draft':
      case 'inactive':
        return 'default';
      default:
        return 'primary';
    }
  };

  return (
    <Chip 
      size={size} 
      color={getStatusColor(status)} 
      variant="flat"
      className={className}
    >
      {status?.toUpperCase()}
    </Chip>
  );
};

// Unified List Item Component
export const UnifiedListItem = ({ 
  title, 
  subtitle, 
  value, 
  status, 
  icon, 
  action,
  tileType = 'track',
  className = '' 
}) => {
  const colors = tileColorSchemes[tileType];
  
  return (
    <motion.div
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      className={`flex items-center justify-between p-3 bg-white/5 rounded-lg hover:bg-white/10 transition-all ${className}`}
      onClick={action}
    >
      <div className="flex items-center gap-3">
        {icon && <div className="text-lg">{icon}</div>}
        <div>
          <div className="text-white text-sm font-medium">{title}</div>
          {subtitle && (
            <div className="text-white/60 text-xs">{subtitle}</div>
          )}
        </div>
      </div>
      <div className="flex items-center gap-3">
        {value && (
          <div className={`${colors.text} text-sm font-bold`}>
            {value}
          </div>
        )}
        {status && <UnifiedStatusChip status={status} />}
        {action && <ArrowRight className="h-4 w-4 text-white/40" />}
      </div>
    </motion.div>
  );
};

// Unified Tab Configuration
export const createUnifiedTabs = (tileType, tabs) => {
  const colors = tileColorSchemes[tileType];
  
  return {
    variant: "underlined",
    classNames: {
      tabList: "gap-6 w-full relative rounded-none p-0 border-b border-divider",
      cursor: `w-full bg-gradient-to-r ${colors.accent}`,
      tab: "max-w-fit px-0 h-12",
      tabContent: "group-data-[selected=true]:text-white text-white/70"
    },
    tabs: tabs.map(tab => ({
      ...tab,
      title: (
        <div className="flex items-center space-x-2">
          <span>{tab.title}</span>
        </div>
      )
    }))
  };
};

export default {
  UnifiedHeader,
  UnifiedMetricCard,
  UnifiedActionCard,
  UnifiedProgressCard,
  UnifiedStatusChip,
  UnifiedListItem,
  createUnifiedTabs,
  tileColorSchemes,
  animationVariants
};
