import { test, expect } from '@playwright/test';

test.describe('Dashboard Button Functionality', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the production site
    await page.goto('https://royalty.technology');

    // Login with test credentials
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'TestPassword123!');
    await page.click('button[type="submit"]');

    // Wait for login to complete and initial page load
    await page.waitForSelector('text=Welcome back', { timeout: 15000 });

    // Check if we're in the grid view and need to navigate to content
    const isGridView = await page.locator('.absolute.inset-0.z-20').isVisible();
    if (isGridView) {
      // Look for the "Back to Content" button (green button with 📄)
      const backToContentButton = page.locator('button:has-text("📄")');
      if (await backToContentButton.isVisible()) {
        await backToContentButton.click();
        // Wait for transition to content view
        await page.waitForTimeout(1000);
      }
    }

    // Wait for dashboard content to be fully loaded and interactive
    await page.waitForSelector('button:has-text("Start New Project")', { timeout: 10000 });

    // Additional wait to ensure all animations and transitions are complete
    await page.waitForTimeout(2000);
  });

  test('Start New Project button should navigate to /start', async ({ page }) => {
    // Look for the Start New Project button
    const startButton = page.locator('button:has-text("Start New Project")');
    await expect(startButton).toBeVisible();

    // Ensure button is ready for interaction
    await startButton.waitFor({ state: 'visible' });
    await page.waitForTimeout(500);

    // Click the button with force to bypass any overlay issues
    await startButton.click({ force: true });

    // Wait for navigation to complete
    await page.waitForTimeout(3000);

    // Check for either URL change or content indicating we're on start page
    const currentUrl = page.url();
    const hasStartContent = await page.locator('text=Start').first().isVisible();

    expect(currentUrl.includes('/start') || hasStartContent).toBeTruthy();
  });

  test('Track Contribution button should navigate to /track', async ({ page }) => {
    // Look for the Track Contribution button
    const trackButton = page.locator('button:has-text("Track Contribution")');
    await expect(trackButton).toBeVisible();

    // Ensure button is ready for interaction
    await trackButton.waitFor({ state: 'visible' });
    await page.waitForTimeout(500);

    // Click the button with force to bypass any overlay issues
    await trackButton.click({ force: true });

    // Wait for navigation to complete
    await page.waitForTimeout(3000);

    const currentUrl = page.url();
    const hasTrackContent = await page.locator('text=Track').first().isVisible();

    expect(currentUrl.includes('/track') || hasTrackContent).toBeTruthy();
  });

  test('View Analytics button should navigate to /analytics', async ({ page }) => {
    // Look for the View Analytics button
    const analyticsButton = page.locator('button:has-text("View Analytics")');
    await expect(analyticsButton).toBeVisible();

    // Ensure button is ready for interaction
    await analyticsButton.waitFor({ state: 'visible' });
    await page.waitForTimeout(500);

    // Click the button with force to bypass any overlay issues
    await analyticsButton.click({ force: true });

    // Wait for navigation to complete
    await page.waitForTimeout(3000);

    const currentUrl = page.url();
    const hasAnalyticsContent = await page.locator('text=Analytics').first().isVisible();

    expect(currentUrl.includes('/analytics') || hasAnalyticsContent).toBeTruthy();
  });

  test('All dashboard buttons should be clickable', async ({ page }) => {
    // Check that all main dashboard buttons are present and clickable
    const buttons = [
      'button:has-text("Start New Project")',
      'button:has-text("Track Contribution")', 
      'button:has-text("View Analytics")'
    ];

    for (const buttonSelector of buttons) {
      const button = page.locator(buttonSelector);
      await expect(button).toBeVisible();
      await expect(button).toBeEnabled();
    }
  });
});
