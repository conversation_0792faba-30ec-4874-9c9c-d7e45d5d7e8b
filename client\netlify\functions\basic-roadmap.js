// Basic Roadmap API - No dependencies
exports.handler = async function(event, context) {
  // Set CORS headers
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type',
    'Access-Control-Allow-Methods': 'GET, OPTIONS',
    'Content-Type': 'application/json'
  };
  
  // Handle OPTIONS request
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 204,
      headers,
      body: ''
    };
  }
  
  try {
    // Static roadmap data
    const roadmapData = [
      {
        id: 1,
        title: "Foundation & User Management",
        timeframe: "Completed",
        expanded: true,
        sections: [
          {
            id: "1.1",
            title: "Project Setup & Configuration",
            tasks: [
              { id: "1.1.1", text: "Finalize tech stack (React, Supabase)", completed: true },
              { id: "1.1.2", text: "Set up development environment", completed: true },
              { id: "1.1.3", text: "Configure Netlify deployment", completed: true }
            ]
          }
        ]
      },
      {
        id: 2,
        title: "Project Creation & Management",
        timeframe: "Phase 1",
        expanded: false,
        sections: [
          {
            id: "2.1",
            title: "Project Wizard",
            tasks: [
              { id: "2.1.1", text: "Design project creation flow", completed: true },
              { id: "2.1.2", text: "Implement project basics form", completed: true },
              { id: "2.1.3", text: "Add team & contributors section", completed: true }
            ]
          }
        ]
      }
    ];
    
    // Calculate stats
    const stats = {
      totalTasks: 6,
      completedTasks: 6,
      progressPercentage: 100,
      phases: [
        {
          id: 1,
          title: "Foundation & User Management",
          timeframe: "Completed",
          progress: 100
        },
        {
          id: 2,
          title: "Project Creation & Management",
          timeframe: "Phase 1",
          progress: 100
        }
      ]
    };
    
    // Return the data
    return {
      statusCode: 200,
      headers,
      body: JSON.stringify({
        success: true,
        data: roadmapData,
        stats: stats,
        source: 'basic-function'
      })
    };
  } catch (error) {
    console.error('Error in basic-roadmap function:', error);
    
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({
        success: false,
        error: error.message
      })
    };
  }
};
