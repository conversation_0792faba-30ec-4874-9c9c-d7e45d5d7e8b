/**
 * Alliance Creation System
 * 
 * Creates and manages collaborative alliances for ventures.
 */

export class AllianceCreationSystem {
  constructor() {
    this.alliances = new Map();
  }

  /**
   * Create a new alliance
   */
  createAlliance(allianceDefinition) {
    const alliance = {
      id: this.generateAllianceId(),
      
      // Basic information
      name: allianceDefinition.name,
      description: allianceDefinition.description,
      industry: allianceDefinition.industry,
      
      // Configuration (legal framework only - no revenue models)
      ipOwnershipModel: allianceDefinition.ipOwnershipModel || 'CO_OWNERSHIP',
      jurisdiction: allianceDefinition.jurisdiction || 'Delaware',
      currency: allianceDefinition.currency || 'USD',

      // Governance
      governanceModel: allianceDefinition.governanceModel || 'DEMOCRATIC',
      votingThreshold: allianceDefinition.votingThreshold || 0.6,

      // Alliance-level fees only (ventures set their own revenue models)
      platformFeePercentage: allianceDefinition.platformFeePercentage || 10,
      allianceAdminFee: allianceDefinition.allianceAdminFee || 2, // Small admin fee for alliance operations
      
      // Legal framework
      disputeResolution: allianceDefinition.disputeResolution || 'ARBITRATION',
      confidentialityPeriod: allianceDefinition.confidentialityPeriod || 24,
      
      // Members
      foundingMembers: allianceDefinition.foundingMembers || [],
      
      // Status
      status: 'active',
      createdAt: new Date().toISOString()
    };

    this.alliances.set(alliance.id, alliance);
    return alliance;
  }

  /**
   * Generate unique alliance ID
   */
  generateAllianceId() {
    return 'alliance_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }
}

// Export the alliance creation system
export const allianceCreationSystem = new AllianceCreationSystem();
