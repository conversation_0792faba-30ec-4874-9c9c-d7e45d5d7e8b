// Content Moderation API
// Authentication & Security Agent: Content moderation and review system
// Created: January 16, 2025

const { createClient } = require('@supabase/supabase-js');
const { authMiddleware, logSecurityEvent, defaultHeaders } = require('./auth-middleware');

// Initialize Supabase client
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_KEY
);

// Helper function to log admin actions
async function logAdminAction(adminId, actionType, targetType, targetId, targetIdentifier, reason, details = {}) {
  try {
    await supabase
      .from('admin_actions')
      .insert([{
        admin_id: adminId,
        action_type: actionType,
        target_type: targetType,
        target_id: targetId,
        target_identifier: targetIdentifier,
        reason: reason,
        details: details
      }]);
  } catch (error) {
    console.error('Error logging admin action:', error);
  }
}

// Get moderation queue
async function getModerationQueue(event) {
  try {
    const { page = 1, limit = 20, status = 'pending', priority, contentType } = event.queryStringParameters || {};
    
    let query = supabase
      .from('moderation_queue')
      .select(`
        *,
        flagged_by_user:flagged_by (
          email,
          display_name
        ),
        reviewed_by_user:reviewed_by (
          email,
          display_name
        )
      `)
      .order('priority', { ascending: false })
      .order('created_at', { ascending: true });
    
    // Apply filters
    if (status !== 'all') {
      query = query.eq('status', status);
    }
    
    if (priority) {
      query = query.eq('priority', priority);
    }
    
    if (contentType) {
      query = query.eq('content_type', contentType);
    }
    
    // Apply pagination
    const offset = (page - 1) * limit;
    query = query.range(offset, offset + limit - 1);
    
    const { data: items, error, count } = await query;
    
    if (error) throw error;
    
    return {
      statusCode: 200,
      headers: defaultHeaders,
      body: JSON.stringify({
        items,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: count,
          pages: Math.ceil(count / limit)
        }
      })
    };
  } catch (error) {
    console.error('Error fetching moderation queue:', error);
    return {
      statusCode: 500,
      headers: defaultHeaders,
      body: JSON.stringify({
        error: 'Internal server error',
        message: 'Failed to fetch moderation queue'
      })
    };
  }
}

// Flag content for moderation
async function flagContent(event) {
  try {
    const {
      contentType,
      contentId,
      flagReason,
      flagDetails,
      flagCategory = 'other',
      priority = 'normal'
    } = JSON.parse(event.body || '{}');
    
    if (!contentType || !contentId || !flagReason) {
      return {
        statusCode: 400,
        headers: defaultHeaders,
        body: JSON.stringify({
          error: 'Bad request',
          message: 'Content type, content ID, and flag reason are required'
        })
      };
    }
    
    // Get content preview based on content type
    let contentPreview = '';
    try {
      let contentQuery;
      switch (contentType) {
        case 'project':
          contentQuery = supabase.from('projects').select('title, description').eq('id', contentId).single();
          break;
        case 'profile':
          contentQuery = supabase.from('users').select('display_name, headline').eq('id', contentId).single();
          break;
        case 'comment':
          contentQuery = supabase.from('profile_comments').select('content').eq('id', contentId).single();
          break;
        case 'message':
          contentQuery = supabase.from('messages').select('content').eq('id', contentId).single();
          break;
        default:
          contentPreview = 'Content preview not available';
      }
      
      if (contentQuery) {
        const { data: content } = await contentQuery;
        if (content) {
          if (contentType === 'project') {
            contentPreview = `${content.title}: ${content.description}`.substring(0, 500);
          } else if (contentType === 'profile') {
            contentPreview = `${content.display_name}: ${content.headline || ''}`.substring(0, 500);
          } else {
            contentPreview = content.content?.substring(0, 500) || 'No content';
          }
        }
      }
    } catch (error) {
      console.error('Error getting content preview:', error);
      contentPreview = 'Error loading content preview';
    }
    
    // Check if content is already flagged
    const { data: existingFlag } = await supabase
      .from('moderation_queue')
      .select('id, related_reports')
      .eq('content_type', contentType)
      .eq('content_id', contentId)
      .eq('status', 'pending')
      .single();
    
    if (existingFlag) {
      // Update existing flag with additional report
      const { data: updatedFlag, error: updateError } = await supabase
        .from('moderation_queue')
        .update({
          related_reports: existingFlag.related_reports + 1,
          priority: existingFlag.related_reports >= 2 ? 'high' : priority
        })
        .eq('id', existingFlag.id)
        .select()
        .single();
      
      if (updateError) throw updateError;
      
      return {
        statusCode: 200,
        headers: defaultHeaders,
        body: JSON.stringify({
          message: 'Additional report added to existing flag',
          flag: updatedFlag
        })
      };
    }
    
    // Create new moderation flag
    const { data: newFlag, error: insertError } = await supabase
      .from('moderation_queue')
      .insert([{
        content_type: contentType,
        content_id: contentId,
        content_preview: contentPreview,
        flagged_by: event.user.id,
        flag_reason: flagReason,
        flag_details: flagDetails,
        flag_category: flagCategory,
        priority: priority,
        status: 'pending'
      }])
      .select()
      .single();
    
    if (insertError) throw insertError;
    
    // Log security event
    await logSecurityEvent(
      'content_flagged',
      'warning',
      event.user.id,
      `Content flagged for moderation: ${flagReason}`,
      {
        content_type: contentType,
        content_id: contentId,
        flag_category: flagCategory,
        flag_reason: flagReason
      },
      30
    );
    
    return {
      statusCode: 201,
      headers: defaultHeaders,
      body: JSON.stringify({
        message: 'Content flagged for moderation',
        flag: newFlag
      })
    };
  } catch (error) {
    console.error('Error flagging content:', error);
    return {
      statusCode: 500,
      headers: defaultHeaders,
      body: JSON.stringify({
        error: 'Internal server error',
        message: 'Failed to flag content'
      })
    };
  }
}

// Review flagged content
async function reviewContent(event) {
  try {
    const { flagId } = event.pathParameters || {};
    const {
      decision, // 'approved', 'removed', 'edited', 'escalated'
      reviewNotes,
      decisionReason
    } = JSON.parse(event.body || '{}');
    
    if (!flagId || !decision) {
      return {
        statusCode: 400,
        headers: defaultHeaders,
        body: JSON.stringify({
          error: 'Bad request',
          message: 'Flag ID and decision are required'
        })
      };
    }
    
    // Get the flag
    const { data: flag, error: fetchError } = await supabase
      .from('moderation_queue')
      .select('*')
      .eq('id', flagId)
      .single();
    
    if (fetchError || !flag) {
      return {
        statusCode: 404,
        headers: defaultHeaders,
        body: JSON.stringify({
          error: 'Not found',
          message: 'Moderation flag not found'
        })
      };
    }
    
    // Update the flag
    const updateData = {
      status: decision,
      reviewed_by: event.user.id,
      review_notes: reviewNotes,
      review_decision_reason: decisionReason,
      reviewed_at: new Date().toISOString()
    };
    
    if (decision === 'escalated') {
      updateData.escalated_at = new Date().toISOString();
      updateData.priority = 'urgent';
    }
    
    const { data: updatedFlag, error: updateError } = await supabase
      .from('moderation_queue')
      .update(updateData)
      .eq('id', flagId)
      .select()
      .single();
    
    if (updateError) throw updateError;
    
    // Log admin action
    await logAdminAction(
      event.user.id,
      'content_moderation_review',
      'moderation_flag',
      flagId,
      `${flag.content_type}:${flag.content_id}`,
      decisionReason || `Content ${decision}`,
      {
        decision: decision,
        content_type: flag.content_type,
        content_id: flag.content_id,
        flag_reason: flag.flag_reason
      }
    );
    
    // Log security event
    await logSecurityEvent(
      'content_moderation_decision',
      decision === 'removed' ? 'warning' : 'info',
      flag.flagged_by,
      `Content moderation decision: ${decision}`,
      {
        moderator_id: event.user.id,
        content_type: flag.content_type,
        content_id: flag.content_id,
        decision: decision,
        reason: decisionReason
      },
      decision === 'removed' ? 40 : 10
    );
    
    return {
      statusCode: 200,
      headers: defaultHeaders,
      body: JSON.stringify({
        message: 'Content review completed',
        flag: updatedFlag
      })
    };
  } catch (error) {
    console.error('Error reviewing content:', error);
    return {
      statusCode: 500,
      headers: defaultHeaders,
      body: JSON.stringify({
        error: 'Internal server error',
        message: 'Failed to review content'
      })
    };
  }
}

// Get moderation statistics
async function getModerationStats(event) {
  try {
    const { timeframe = '7d' } = event.queryStringParameters || {};
    
    let dateFilter = new Date();
    switch (timeframe) {
      case '24h':
        dateFilter.setHours(dateFilter.getHours() - 24);
        break;
      case '7d':
        dateFilter.setDate(dateFilter.getDate() - 7);
        break;
      case '30d':
        dateFilter.setDate(dateFilter.getDate() - 30);
        break;
      default:
        dateFilter.setDate(dateFilter.getDate() - 7);
    }
    
    // Get various statistics
    const [
      { count: totalFlags },
      { count: pendingFlags },
      { count: resolvedFlags },
      { count: removedContent }
    ] = await Promise.all([
      supabase.from('moderation_queue').select('*', { count: 'exact', head: true }).gte('created_at', dateFilter.toISOString()),
      supabase.from('moderation_queue').select('*', { count: 'exact', head: true }).eq('status', 'pending'),
      supabase.from('moderation_queue').select('*', { count: 'exact', head: true }).in('status', ['approved', 'removed', 'edited']).gte('reviewed_at', dateFilter.toISOString()),
      supabase.from('moderation_queue').select('*', { count: 'exact', head: true }).eq('status', 'removed').gte('reviewed_at', dateFilter.toISOString())
    ]);
    
    return {
      statusCode: 200,
      headers: defaultHeaders,
      body: JSON.stringify({
        timeframe,
        stats: {
          total_flags: totalFlags,
          pending_flags: pendingFlags,
          resolved_flags: resolvedFlags,
          removed_content: removedContent,
          resolution_rate: totalFlags > 0 ? Math.round((resolvedFlags / totalFlags) * 100) : 0
        }
      })
    };
  } catch (error) {
    console.error('Error fetching moderation stats:', error);
    return {
      statusCode: 500,
      headers: defaultHeaders,
      body: JSON.stringify({
        error: 'Internal server error',
        message: 'Failed to fetch moderation statistics'
      })
    };
  }
}

// Main handler with middleware
exports.handler = async (event, context) => {
  // Apply authentication middleware
  const authResult = await authMiddleware({
    requireAuth: true,
    requireAdmin: false, // Allow regular users to flag content
    rateLimit: { maxRequests: 50, windowMs: 60000 }
  })(event, context);
  
  if (authResult) {
    return authResult; // Return error response from middleware
  }
  
  try {
    const path = event.path.replace('/.netlify/functions/content-moderation', '');
    const method = event.httpMethod;
    
    // Route requests
    if (method === 'GET' && path === '/queue') {
      // Require moderator access for viewing queue
      if (!event.user.is_admin && !['content_moderator', 'platform_admin', 'super_admin'].includes(event.user.admin_role)) {
        return {
          statusCode: 403,
          headers: defaultHeaders,
          body: JSON.stringify({
            error: 'Forbidden',
            message: 'Moderator access required'
          })
        };
      }
      return await getModerationQueue(event);
    } else if (method === 'POST' && path === '/flag') {
      return await flagContent(event);
    } else if (method === 'PUT' && path.match(/^\/review\/[^\/]+$/)) {
      // Require moderator access for reviewing content
      if (!event.user.is_admin && !['content_moderator', 'platform_admin', 'super_admin'].includes(event.user.admin_role)) {
        return {
          statusCode: 403,
          headers: defaultHeaders,
          body: JSON.stringify({
            error: 'Forbidden',
            message: 'Moderator access required'
          })
        };
      }
      return await reviewContent(event);
    } else if (method === 'GET' && path === '/stats') {
      // Require moderator access for stats
      if (!event.user.is_admin && !['content_moderator', 'platform_admin', 'super_admin'].includes(event.user.admin_role)) {
        return {
          statusCode: 403,
          headers: defaultHeaders,
          body: JSON.stringify({
            error: 'Forbidden',
            message: 'Moderator access required'
          })
        };
      }
      return await getModerationStats(event);
    } else {
      return {
        statusCode: 404,
        headers: defaultHeaders,
        body: JSON.stringify({
          error: 'Not found',
          message: 'Endpoint not found'
        })
      };
    }
  } catch (error) {
    console.error('Error in content moderation handler:', error);
    return {
      statusCode: 500,
      headers: defaultHeaders,
      body: JSON.stringify({
        error: 'Internal server error',
        message: 'An unexpected error occurred'
      })
    };
  }
};
