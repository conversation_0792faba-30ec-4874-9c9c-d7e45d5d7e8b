// Vetting Service API
// Integration & Services Agent: 6-level skill verification and education management

const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_ANON_KEY
);

// CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS'
};

// Level definitions and requirements
const LEVEL_DEFINITIONS = {
  0: {
    name: 'Unverified',
    color: '🔴',
    description: 'New platform users with no validation',
    access: ['Basic profile creation', 'Limited quest viewing'],
    restrictions: ['Cannot apply for paid quests', 'Limited to educational content', 'No client interaction privileges']
  },
  1: {
    name: 'Learning',
    color: '🟡',
    description: 'Completed foundational education requirements',
    access: ['Novice quests (⭐)', 'Basic collaboration tools'],
    requirements: [
      'Complete technology-specific learning path (minimum 40 hours)',
      'Pass automated skill assessments (70% minimum score)',
      'Submit portfolio with 2+ projects',
      'Complete platform orientation course'
    ]
  },
  2: {
    name: 'Peer Verified',
    color: '🟠',
    description: 'Community-validated skills through peer review',
    access: ['Apprentice quests (⭐⭐)', 'Team collaboration features'],
    requirements: [
      'Minimum 2 peer endorsements from Level 3+ users',
      'Complete collaborative project with satisfactory rating',
      'Demonstrate mentoring capability (assist 1+ Level 1 user)',
      'Pass peer code review process'
    ]
  },
  3: {
    name: 'Project Verified',
    color: '🟢',
    description: 'Proven delivery capability through client work',
    access: ['Journeyman quests (⭐⭐⭐)', 'Client management tools'],
    requirements: [
      'Complete minimum 3 client projects with 4.5+ star rating',
      'Demonstrate project management skills',
      'Provide client testimonials',
      'Show consistent delivery timeline adherence'
    ]
  },
  4: {
    name: 'Expert Verified',
    color: '🔵',
    description: 'Technical expertise validated by industry experts',
    access: ['Expert quests (⭐⭐⭐⭐)', 'Mentoring privileges', 'Review board participation'],
    requirements: [
      'Expert technical interview (90+ minute assessment)',
      'Architecture review of complex project',
      'Demonstrate thought leadership (blog posts, presentations)',
      'Mentor minimum 5 lower-level users successfully'
    ]
  },
  5: {
    name: 'Industry Certified',
    color: '🟣',
    description: 'Industry-recognized expertise with external validation',
    access: ['Master quests (⭐⭐⭐⭐⭐)', 'Platform governance participation', 'Expert reviewer role'],
    requirements: [
      'Industry certifications (AWS, Google, Microsoft, etc.)',
      'Conference speaking or significant open-source contributions',
      'Minimum 100 hours of platform mentoring',
      'External industry recognition or recommendations'
    ]
  }
};

// Authenticate user from JWT token
const authenticateUser = async (authHeader) => {
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    throw new Error('Missing or invalid authorization header');
  }

  const token = authHeader.substring(7);
  
  try {
    const { data: { user }, error } = await supabase.auth.getUser(token);
    
    if (error || !user) {
      throw new Error('Invalid authentication token');
    }
    
    return user;
  } catch (error) {
    console.error('Authentication error:', error);
    throw new Error('Authentication failed');
  }
};

// Get user's skill levels and verification status
const getUserSkillLevels = async (user) => {
  try {
    const { data: skillLevels, error } = await supabase
      .from('user_skill_levels')
      .select('*')
      .eq('user_id', user.id)
      .order('current_level', { ascending: false });

    if (error) {
      throw new Error(`Failed to fetch skill levels: ${error.message}`);
    }

    // Enrich with level definitions
    const enrichedLevels = (skillLevels || []).map(skill => ({
      ...skill,
      level_info: LEVEL_DEFINITIONS[skill.current_level],
      next_level_info: LEVEL_DEFINITIONS[skill.current_level + 1] || null
    }));

    return enrichedLevels;

  } catch (error) {
    console.error('Get user skill levels error:', error);
    throw error;
  }
};

// Get available learning paths
const getLearningPaths = async (technology = null, level = null) => {
  try {
    let query = supabase
      .from('learning_paths')
      .select('*')
      .eq('is_active', true)
      .order('technology', { ascending: true })
      .order('level', { ascending: true });

    if (technology) {
      query = query.eq('technology', technology);
    }

    if (level !== null) {
      query = query.eq('level', level);
    }

    const { data: paths, error } = await query;

    if (error) {
      throw new Error(`Failed to fetch learning paths: ${error.message}`);
    }

    return paths || [];

  } catch (error) {
    console.error('Get learning paths error:', error);
    throw error;
  }
};

// Get user's learning progress
const getLearningProgress = async (user, pathId = null) => {
  try {
    let query = supabase
      .from('learning_progress')
      .select(`
        *,
        learning_paths (
          technology,
          level,
          path_name,
          estimated_hours
        )
      `)
      .eq('user_id', user.id)
      .order('last_activity_at', { ascending: false });

    if (pathId) {
      query = query.eq('learning_path_id', pathId);
    }

    const { data: progress, error } = await query;

    if (error) {
      throw new Error(`Failed to fetch learning progress: ${error.message}`);
    }

    return progress || [];

  } catch (error) {
    console.error('Get learning progress error:', error);
    throw error;
  }
};

// Start a learning path
const startLearningPath = async (user, pathId) => {
  try {
    // Check if path exists and is active
    const { data: path, error: pathError } = await supabase
      .from('learning_paths')
      .select('*')
      .eq('id', pathId)
      .eq('is_active', true)
      .single();

    if (pathError || !path) {
      throw new Error('Learning path not found or inactive');
    }

    // Check if user already has progress for this path
    const { data: existingProgress, error: progressError } = await supabase
      .from('learning_progress')
      .select('id')
      .eq('user_id', user.id)
      .eq('learning_path_id', pathId)
      .single();

    if (progressError && progressError.code !== 'PGRST116') {
      throw new Error(`Failed to check existing progress: ${progressError.message}`);
    }

    if (existingProgress) {
      throw new Error('Learning path already started');
    }

    // Create initial progress record
    const { data: newProgress, error: createError } = await supabase
      .from('learning_progress')
      .insert({
        user_id: user.id,
        learning_path_id: pathId,
        status: 'in_progress',
        started_at: new Date().toISOString()
      })
      .select()
      .single();

    if (createError) {
      throw new Error(`Failed to start learning path: ${createError.message}`);
    }

    return {
      success: true,
      progress_id: newProgress.id,
      path: path,
      message: 'Learning path started successfully'
    };

  } catch (error) {
    console.error('Start learning path error:', error);
    throw error;
  }
};

// Update learning progress
const updateLearningProgress = async (user, progressId, updateData) => {
  try {
    const {
      progress_percentage,
      hours_completed,
      status,
      course_id,
      course_name,
      certificate_url
    } = updateData;

    // Verify user owns this progress record
    const { data: progress, error: verifyError } = await supabase
      .from('learning_progress')
      .select('id')
      .eq('id', progressId)
      .eq('user_id', user.id)
      .single();

    if (verifyError || !progress) {
      throw new Error('Learning progress not found or access denied');
    }

    // Prepare update data
    const updates = {
      last_activity_at: new Date().toISOString()
    };

    if (progress_percentage !== undefined) updates.progress_percentage = progress_percentage;
    if (hours_completed !== undefined) updates.hours_completed = hours_completed;
    if (status !== undefined) updates.status = status;
    if (course_id !== undefined) updates.course_id = course_id;
    if (course_name !== undefined) updates.course_name = course_name;
    if (certificate_url !== undefined) updates.certificate_url = certificate_url;

    if (status === 'completed') {
      updates.completed_at = new Date().toISOString();
    }

    const { data: updatedProgress, error: updateError } = await supabase
      .from('learning_progress')
      .update(updates)
      .eq('id', progressId)
      .select()
      .single();

    if (updateError) {
      throw new Error(`Failed to update learning progress: ${updateError.message}`);
    }

    return {
      success: true,
      progress: updatedProgress,
      message: 'Learning progress updated successfully'
    };

  } catch (error) {
    console.error('Update learning progress error:', error);
    throw error;
  }
};

// Submit assessment
const submitAssessment = async (user, assessmentData) => {
  try {
    const {
      technology,
      level,
      assessment_type,
      score,
      max_score = 100,
      assessment_data = {},
      time_taken_minutes
    } = assessmentData;

    if (!technology || level === undefined || !assessment_type || score === undefined) {
      throw new Error('technology, level, assessment_type, and score are required');
    }

    // Calculate if passed (70% minimum for automated assessments)
    const passed = assessment_type === 'automated' ? (score / max_score) >= 0.7 : score >= (max_score * 0.7);

    // Get attempt number
    const { data: previousAttempts, error: attemptsError } = await supabase
      .from('assessment_results')
      .select('attempt_number')
      .eq('user_id', user.id)
      .eq('technology', technology)
      .eq('level', level)
      .eq('assessment_type', assessment_type)
      .order('attempt_number', { ascending: false })
      .limit(1);

    if (attemptsError) {
      console.error('Failed to get previous attempts:', attemptsError);
    }

    const attemptNumber = previousAttempts && previousAttempts.length > 0 
      ? previousAttempts[0].attempt_number + 1 
      : 1;

    // Insert assessment result
    const { data: result, error } = await supabase
      .from('assessment_results')
      .insert({
        user_id: user.id,
        technology,
        level,
        assessment_type,
        score,
        max_score,
        passed,
        assessment_data,
        time_taken_minutes,
        attempt_number: attemptNumber
      })
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to submit assessment: ${error.message}`);
    }

    // If passed and it's a level advancement assessment, check if user can advance
    if (passed && assessment_type === 'automated') {
      await checkLevelAdvancement(user, technology, level);
    }

    return {
      success: true,
      assessment_id: result.id,
      passed,
      score,
      attempt_number: attemptNumber,
      message: passed ? 'Assessment passed successfully!' : 'Assessment not passed. Keep learning and try again!'
    };

  } catch (error) {
    console.error('Submit assessment error:', error);
    throw error;
  }
};

// Check and process level advancement
const checkLevelAdvancement = async (user, technology, targetLevel) => {
  try {
    // Get current skill level
    const { data: currentSkill, error: skillError } = await supabase
      .from('user_skill_levels')
      .select('*')
      .eq('user_id', user.id)
      .eq('technology', technology)
      .single();

    if (skillError && skillError.code !== 'PGRST116') {
      throw new Error(`Failed to get current skill level: ${skillError.message}`);
    }

    const currentLevel = currentSkill ? currentSkill.current_level : 0;

    // Check if advancement is valid (can only advance one level at a time)
    if (targetLevel !== currentLevel + 1) {
      return { canAdvance: false, reason: 'Can only advance one level at a time' };
    }

    // Check requirements for target level
    const requirements = await checkLevelRequirements(user, technology, targetLevel);
    
    if (requirements.allMet) {
      // Advance the user
      if (currentSkill) {
        await supabase
          .from('user_skill_levels')
          .update({
            current_level: targetLevel,
            verification_date: new Date().toISOString()
          })
          .eq('id', currentSkill.id);
      } else {
        await supabase
          .from('user_skill_levels')
          .insert({
            user_id: user.id,
            technology,
            current_level: targetLevel,
            verification_date: new Date().toISOString()
          });
      }

      return { 
        canAdvance: true, 
        advanced: true, 
        newLevel: targetLevel,
        levelInfo: LEVEL_DEFINITIONS[targetLevel]
      };
    }

    return { 
      canAdvance: false, 
      requirements: requirements.details,
      missingRequirements: requirements.missing
    };

  } catch (error) {
    console.error('Check level advancement error:', error);
    throw error;
  }
};

// Check requirements for a specific level
const checkLevelRequirements = async (user, technology, level) => {
  try {
    const requirements = {
      allMet: false,
      details: {},
      missing: []
    };

    switch (level) {
      case 1:
        // Check learning path completion (40+ hours)
        const { data: learningProgress } = await supabase
          .from('learning_progress')
          .select('hours_completed, status')
          .eq('user_id', user.id);
        
        const totalHours = learningProgress?.reduce((sum, p) => sum + (p.hours_completed || 0), 0) || 0;
        requirements.details.learningHours = { required: 40, completed: totalHours, met: totalHours >= 40 };
        
        // Check assessment pass
        const { data: assessments } = await supabase
          .from('assessment_results')
          .select('passed')
          .eq('user_id', user.id)
          .eq('technology', technology)
          .eq('level', 1)
          .eq('passed', true);
        
        requirements.details.assessment = { required: 1, completed: assessments?.length || 0, met: (assessments?.length || 0) >= 1 };
        
        // Check portfolio projects
        const { data: projects } = await supabase
          .from('portfolio_projects')
          .select('id')
          .eq('user_id', user.id)
          .eq('technology', technology)
          .eq('review_status', 'approved');
        
        requirements.details.portfolio = { required: 2, completed: projects?.length || 0, met: (projects?.length || 0) >= 2 };
        
        break;
        
      case 2:
        // Check peer endorsements
        const { data: peerReviews } = await supabase
          .from('peer_reviews')
          .select('recommendation')
          .eq('reviewee_id', user.id)
          .eq('technology', technology)
          .eq('recommendation', 'approve');
        
        requirements.details.peerEndorsements = { required: 2, completed: peerReviews?.length || 0, met: (peerReviews?.length || 0) >= 2 };
        
        break;
        
      // Add more level requirements as needed
    }

    // Check if all requirements are met
    requirements.allMet = Object.values(requirements.details).every(req => req.met);
    requirements.missing = Object.entries(requirements.details)
      .filter(([key, req]) => !req.met)
      .map(([key, req]) => key);

    return requirements;

  } catch (error) {
    console.error('Check level requirements error:', error);
    throw error;
  }
};

// Get user's verification dashboard data
const getVerificationDashboard = async (user) => {
  try {
    // Get skill levels
    const skillLevels = await getUserSkillLevels(user);
    
    // Get active learning progress
    const learningProgress = await getLearningProgress(user);
    
    // Get recent assessments
    const { data: recentAssessments, error: assessmentError } = await supabase
      .from('assessment_results')
      .select('*')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false })
      .limit(5);

    if (assessmentError) {
      console.error('Failed to fetch recent assessments:', assessmentError);
    }

    // Get mentoring relationships
    const { data: mentoring, error: mentoringError } = await supabase
      .from('mentoring_relationships')
      .select('*')
      .or(`mentor_id.eq.${user.id},mentee_id.eq.${user.id}`)
      .eq('status', 'active');

    if (mentoringError) {
      console.error('Failed to fetch mentoring relationships:', mentoringError);
    }

    return {
      skill_levels: skillLevels,
      learning_progress: learningProgress,
      recent_assessments: recentAssessments || [],
      mentoring_relationships: mentoring || [],
      overall_stats: {
        total_technologies: skillLevels.length,
        highest_level: Math.max(...skillLevels.map(s => s.current_level), 0),
        total_learning_hours: learningProgress.reduce((sum, p) => sum + (p.hours_completed || 0), 0),
        completed_assessments: recentAssessments?.length || 0
      }
    };

  } catch (error) {
    console.error('Get verification dashboard error:', error);
    throw error;
  }
};

// Submit portfolio project
const submitPortfolioProject = async (user, projectData) => {
  try {
    const {
      technology,
      level,
      project_name,
      description,
      project_url,
      repository_url,
      demo_url,
      technologies_used = [],
      project_type,
      complexity_score,
      estimated_hours
    } = projectData;

    if (!technology || level === undefined || !project_name) {
      throw new Error('technology, level, and project_name are required');
    }

    const { data: project, error } = await supabase
      .from('portfolio_projects')
      .insert({
        user_id: user.id,
        technology,
        level,
        project_name,
        description,
        project_url,
        repository_url,
        demo_url,
        technologies_used,
        project_type,
        complexity_score,
        estimated_hours
      })
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to submit portfolio project: ${error.message}`);
    }

    return {
      success: true,
      project_id: project.id,
      message: 'Portfolio project submitted successfully'
    };

  } catch (error) {
    console.error('Submit portfolio project error:', error);
    throw error;
  }
};

// Request level advancement
const requestLevelAdvancement = async (user, requestData) => {
  try {
    const { technology, target_level, evidence_submitted = {} } = requestData;

    if (!technology || target_level === undefined) {
      throw new Error('technology and target_level are required');
    }

    // Get current level
    const { data: currentSkill, error: skillError } = await supabase
      .from('user_skill_levels')
      .select('current_level')
      .eq('user_id', user.id)
      .eq('technology', technology)
      .single();

    if (skillError && skillError.code !== 'PGRST116') {
      throw new Error(`Failed to get current skill level: ${skillError.message}`);
    }

    const currentLevel = currentSkill ? currentSkill.current_level : 0;

    // Check requirements
    const requirements = await checkLevelRequirements(user, technology, target_level);

    const { data: request, error } = await supabase
      .from('level_advancement_requests')
      .insert({
        user_id: user.id,
        technology,
        current_level: currentLevel,
        target_level,
        requirements_met: requirements.details,
        evidence_submitted
      })
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to submit advancement request: ${error.message}`);
    }

    return {
      success: true,
      request_id: request.id,
      requirements_met: requirements.allMet,
      missing_requirements: requirements.missing,
      message: requirements.allMet
        ? 'Advancement request submitted for review'
        : 'Advancement request submitted, but some requirements are not yet met'
    };

  } catch (error) {
    console.error('Request level advancement error:', error);
    throw error;
  }
};

// Main handler function
exports.handler = async (event, context) => {
  // Handle CORS preflight
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers: corsHeaders,
      body: ''
    };
  }

  try {
    // Authenticate user
    const user = await authenticateUser(event.headers.authorization);
    
    // Parse request path and method
    const pathParts = event.path.split('/').filter(Boolean);
    const action = pathParts[pathParts.length - 1];
    const httpMethod = event.httpMethod;
    const body = event.body ? JSON.parse(event.body) : {};
    const queryParams = new URLSearchParams(event.queryStringParameters || {});

    let result;

    switch (action) {
      case 'dashboard':
        if (httpMethod !== 'GET') {
          throw new Error('Method not allowed');
        }
        result = await getVerificationDashboard(user);
        break;

      case 'skill-levels':
        if (httpMethod !== 'GET') {
          throw new Error('Method not allowed');
        }
        result = await getUserSkillLevels(user);
        break;

      case 'learning-paths':
        if (httpMethod !== 'GET') {
          throw new Error('Method not allowed');
        }
        const technology = queryParams.get('technology');
        const level = queryParams.get('level') ? parseInt(queryParams.get('level')) : null;
        result = await getLearningPaths(technology, level);
        break;

      case 'start-learning':
        if (httpMethod !== 'POST') {
          throw new Error('Method not allowed');
        }
        if (!body.path_id) {
          throw new Error('path_id is required');
        }
        result = await startLearningPath(user, body.path_id);
        break;

      case 'update-progress':
        if (httpMethod !== 'PUT') {
          throw new Error('Method not allowed');
        }
        if (!body.progress_id) {
          throw new Error('progress_id is required');
        }
        result = await updateLearningProgress(user, body.progress_id, body);
        break;

      case 'submit-assessment':
        if (httpMethod !== 'POST') {
          throw new Error('Method not allowed');
        }
        result = await submitAssessment(user, body);
        break;

      case 'learning-progress':
        if (httpMethod !== 'GET') {
          throw new Error('Method not allowed');
        }
        const pathId = queryParams.get('path_id');
        result = await getLearningProgress(user, pathId);
        break;

      case 'submit-project':
        if (httpMethod !== 'POST') {
          throw new Error('Method not allowed');
        }
        result = await submitPortfolioProject(user, body);
        break;

      case 'request-advancement':
        if (httpMethod !== 'POST') {
          throw new Error('Method not allowed');
        }
        result = await requestLevelAdvancement(user, body);
        break;

      default:
        throw new Error('Invalid action');
    }

    return {
      statusCode: 200,
      headers: corsHeaders,
      body: JSON.stringify({
        success: true,
        data: result,
        timestamp: new Date().toISOString()
      })
    };

  } catch (error) {
    console.error('Vetting Service API error:', error);
    
    return {
      statusCode: error.message.includes('Authentication') ? 401 : 
                  error.message.includes('not allowed') ? 405 : 400,
      headers: corsHeaders,
      body: JSON.stringify({
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      })
    };
  }
};
