/**
 * Unified Pool Dynamic Contributor Demonstration
 * 
 * Shows how ALL contributors (core team + gigwork) participate in the same
 * contribution-based revenue pool for maximum fairness and simplicity.
 */

import { dynamicContributorManagement } from '../utils/agreement/dynamicContributorManagement.js';

console.log('🚀 UNIFIED POOL CONTRIBUTOR SYSTEM DEMONSTRATION\n');
console.log('=' .repeat(80));

// ============================================================================
// UNIFIED POOL CONFIGURATION
// ============================================================================

console.log('1️⃣ INITIALIZING UNIFIED POOL VENTURE');

// Initialize venture with unified contribution pool
const unifiedVenture = dynamicContributorManagement.initializeScalableVenture({
  name: 'CloudSync Pro - Unified Pool',
  description: 'All contributors earn based on actual contribution - no artificial hierarchies',
  allianceId: 'alliance_unified_001',
  
  // Unified pool configuration
  maxContributors: 50,
  allowDynamicJoining: true,
  requireApprovalForNewContributors: false, // More open for gigwork
  autoGenerateAgreements: true,
  
  // UNIFIED REVENUE MODEL - Everyone in same pool!
  revenueModel: {
    calculationMethod: 'contribution_points', // Pure contribution-based
    coreTeamReservedPercentage: 0,           // No reserved percentage
    gigworkPoolPercentage: 90,               // 90% for ALL contributors
    platformFeePercentage: 10,               // 10% platform fee
    contributionPointsWeight: 1.0,           // 100% based on contribution points
    timeParticipationWeight: 0.0             // No time-based component
  },
  
  // Tranche configuration
  trancheConfig: {
    trancheType: 'release',
    trancheDuration: 60, // Shorter 2-month cycles for faster feedback
    revenueDistributionDelay: 14, // Faster distribution - 2 weeks after release
    allowRetrospectiveJoining: false
  },
  
  // Core team - but they earn like everyone else!
  coreTeam: [
    {
      email: '<EMAIL>',
      role: 'Technical Lead',
      revenueShare: 0, // No fixed share - earns based on contribution!
      responsibilities: ['Architecture', 'Code review', 'Technical leadership'],
      ipRights: 'co_owner',
      guaranteedMinimumShare: 0 // No guarantees - pure meritocracy
    },
    {
      email: '<EMAIL>',
      role: 'Product Lead',
      revenueShare: 0, // No fixed share
      responsibilities: ['Product strategy', 'User research', 'Feature planning'],
      ipRights: 'co_owner',
      guaranteedMinimumShare: 0
    }
  ],
  
  autoStartFirstTranche: true
});

console.log(`   ✅ Venture: ${unifiedVenture.name}`);
console.log(`   🎯 Revenue Model: Pure contribution-based (no fixed shares)`);
console.log(`   💰 Contributor Pool: ${unifiedVenture.revenueModel.gigworkPoolPercentage}% of revenue`);
console.log(`   🏢 Platform Fee: ${unifiedVenture.revenueModel.platformFeePercentage}%`);
console.log(`   👥 Starting Team: ${unifiedVenture.contributorPools.coreTeam.length} founders (earning like everyone else)`);
console.log(`   📅 Release Cycles: ${unifiedVenture.trancheConfig.trancheDuration} days\n`);

// ============================================================================
// ADDING DIVERSE CONTRIBUTORS
// ============================================================================

console.log('2️⃣ BUILDING DIVERSE CONTRIBUTOR TEAM');

// Add various types of contributors - all treated equally
const contributors = [
  // Full-time gigwork developer
  {
    email: '<EMAIL>',
    role: 'Senior Full-Stack Developer',
    skills: ['React', 'Node.js', 'PostgreSQL'],
    experienceLevel: 'senior',
    expectedContributionLevel: 'high',
    platformRating: 4.9,
    participationModel: 'continuous', // Works full-time on project
    responsibilities: ['Feature development', 'Code review', 'Mentoring']
  },
  
  // Part-time specialist
  {
    email: '<EMAIL>',
    role: 'Security Expert',
    skills: ['Security', 'Penetration Testing', 'Compliance'],
    experienceLevel: 'expert',
    expectedContributionLevel: 'medium',
    platformRating: 4.8,
    participationModel: 'tranche_based', // Works on security features only
    responsibilities: ['Security architecture', 'Penetration testing', 'Compliance']
  },
  
  // Junior developer learning
  {
    email: '<EMAIL>',
    role: 'Junior Developer',
    skills: ['JavaScript', 'CSS', 'Learning React'],
    experienceLevel: 'junior',
    expectedContributionLevel: 'medium',
    platformRating: 4.2,
    participationModel: 'tranche_based',
    responsibilities: ['Bug fixes', 'Documentation', 'Testing']
  },
  
  // Designer working part-time
  {
    email: '<EMAIL>',
    role: 'UX/UI Designer',
    skills: ['Figma', 'User Research', 'Prototyping'],
    experienceLevel: 'senior',
    expectedContributionLevel: 'medium',
    platformRating: 4.7,
    participationModel: 'milestone_based', // Works on design phases
    responsibilities: ['UI design', 'User research', 'Prototyping']
  },
  
  // Marketing specialist
  {
    email: '<EMAIL>',
    role: 'Growth Marketing',
    skills: ['Content Marketing', 'SEO', 'Analytics'],
    experienceLevel: 'intermediate',
    expectedContributionLevel: 'medium',
    platformRating: 4.5,
    participationModel: 'tranche_based',
    responsibilities: ['Content creation', 'SEO optimization', 'User acquisition']
  }
];

// Add all contributors to the venture
contributors.forEach(contributor => {
  const added = dynamicContributorManagement.addGigworkContributor(unifiedVenture.id, contributor);
  console.log(`   ✅ Added: ${added.email} (${added.experienceLevel} ${added.role})`);
});

console.log(`   👥 Total Team: ${Object.values(unifiedVenture.contributorPools).flat().length} contributors`);
console.log(`   🎯 All earning based on contribution points - no fixed shares!\n`);

// ============================================================================
// RECORDING DIVERSE CONTRIBUTIONS IN FIRST TRANCHE
// ============================================================================

console.log('3️⃣ RECORDING CONTRIBUTIONS IN v1.0 RELEASE');

const firstTranche = Array.from(dynamicContributorManagement.tranches.values())[0];

// Simulate realistic contributions from different types of contributors
const contributions = [
  // Founders working hard but earning based on contribution
  {
    contributor: '<EMAIL>',
    type: 'code',
    description: 'Core architecture and API development',
    hoursWorked: 80, // Working hard as founder
    difficultyLevel: 'expert',
    qualityRating: 5
  },
  {
    contributor: '<EMAIL>',
    type: 'planning',
    description: 'Product strategy and feature specification',
    hoursWorked: 60,
    difficultyLevel: 'hard',
    qualityRating: 5
  },
  
  // Full-time gigwork developer - high contribution
  {
    contributor: '<EMAIL>',
    type: 'code',
    description: 'Frontend components and state management',
    hoursWorked: 75, // Nearly full-time
    difficultyLevel: 'hard',
    qualityRating: 5
  },
  
  // Part-time security expert - specialized high-value work
  {
    contributor: '<EMAIL>',
    type: 'code',
    description: 'Security implementation and audit',
    hoursWorked: 30, // Part-time but high value
    difficultyLevel: 'expert',
    qualityRating: 5
  },
  
  // Junior developer - learning and contributing
  {
    contributor: '<EMAIL>',
    type: 'code',
    description: 'Bug fixes and documentation',
    hoursWorked: 40,
    difficultyLevel: 'easy',
    qualityRating: 4
  },
  
  // Designer - focused design work
  {
    contributor: '<EMAIL>',
    type: 'design',
    description: 'Complete UI/UX design system',
    hoursWorked: 50,
    difficultyLevel: 'hard',
    qualityRating: 5
  },
  
  // Marketing - content and growth
  {
    contributor: '<EMAIL>',
    type: 'content',
    description: 'Marketing content and user acquisition',
    hoursWorked: 35,
    difficultyLevel: 'medium',
    qualityRating: 4
  }
];

let totalPoints = 0;
const contributorPoints = {};

contributions.forEach(contrib => {
  // Find contributor by email
  const allContributors = Object.values(unifiedVenture.contributorPools).flat();
  const contributor = allContributors.find(c => c.email === contrib.contributor);
  
  if (contributor) {
    const contribution = dynamicContributorManagement.recordContribution(
      firstTranche.id,
      contributor.id,
      contrib
    );
    
    totalPoints += contribution.finalPoints;
    contributorPoints[contrib.contributor] = contribution.finalPoints;
    
    console.log(`   📊 ${contrib.contributor}: ${contribution.finalPoints.toFixed(1)} points (${contrib.hoursWorked}h, ${contrib.difficultyLevel})`);
  }
});

console.log(`   🎯 Total Contribution Points: ${totalPoints.toFixed(1)}\n`);

// ============================================================================
// UNIFIED REVENUE DISTRIBUTION
// ============================================================================

console.log('4️⃣ UNIFIED REVENUE DISTRIBUTION - EVERYONE EQUAL');

const trancheRevenue = 200000; // $200k revenue for v1.0

const revenueDistribution = dynamicContributorManagement.calculateTrancheRevenueDistribution(
  firstTranche.id,
  trancheRevenue
);

console.log(`   💰 Total Revenue: $${revenueDistribution.totalRevenue.toLocaleString()}`);
console.log(`   🏢 Platform Fee (${unifiedVenture.revenueModel.platformFeePercentage}%): $${revenueDistribution.platformFee.toLocaleString()}`);
console.log(`   👥 Contributor Pool (${unifiedVenture.revenueModel.gigworkPoolPercentage}%): $${revenueDistribution.gigworkPool.toLocaleString()}`);
console.log(`   📊 Total Contribution Points: ${revenueDistribution.totalContributionPoints.toFixed(1)}\n`);

console.log('   💵 UNIFIED DISTRIBUTION (All Based on Contribution):');

// Combine all distributions since everyone is in the same pool
const allDistributions = {
  ...revenueDistribution.coreTeamDistribution,
  ...revenueDistribution.gigworkDistribution
};

// Sort by revenue earned (highest first)
const sortedDistributions = Object.entries(allDistributions)
  .map(([contributorId, amount]) => {
    const allContributors = Object.values(unifiedVenture.contributorPools).flat();
    const contributor = allContributors.find(c => c.id === contributorId);
    const points = firstTranche.contributionPoints.get(contributorId) || 0;
    const percentage = ((points / revenueDistribution.totalContributionPoints) * 100);
    
    return {
      email: contributor?.email || 'Unknown',
      role: contributor?.role || 'Unknown',
      amount,
      points,
      percentage,
      isFounder: contributor?.email.includes('@founder.com') || false
    };
  })
  .sort((a, b) => b.amount - a.amount);

sortedDistributions.forEach((dist, index) => {
  const founderFlag = dist.isFounder ? ' 👑' : '';
  console.log(`      ${index + 1}. ${dist.email}${founderFlag}: $${dist.amount.toLocaleString()} (${dist.points.toFixed(1)} points, ${dist.percentage.toFixed(1)}%)`);
});

// ============================================================================
// ANALYSIS OF UNIFIED SYSTEM
// ============================================================================

console.log('\n5️⃣ UNIFIED SYSTEM ANALYSIS');

const founderEarnings = sortedDistributions.filter(d => d.isFounder);
const gigworkEarnings = sortedDistributions.filter(d => !d.isFounder);

console.log('   📊 EARNINGS ANALYSIS:');
console.log(`   👑 Founders: ${founderEarnings.length} people, $${founderEarnings.reduce((sum, d) => sum + d.amount, 0).toLocaleString()} total`);
console.log(`   🎯 Gigwork: ${gigworkEarnings.length} people, $${gigworkEarnings.reduce((sum, d) => sum + d.amount, 0).toLocaleString()} total`);

console.log('\n   🏆 TOP PERFORMERS (Regardless of Status):');
sortedDistributions.slice(0, 3).forEach((dist, index) => {
  const medal = ['🥇', '🥈', '🥉'][index];
  const status = dist.isFounder ? 'Founder' : 'Gigwork';
  console.log(`      ${medal} ${dist.email} (${status}): $${dist.amount.toLocaleString()}`);
});

console.log('\n   ✅ UNIFIED SYSTEM BENEFITS:');
console.log('      • Pure meritocracy - highest contributors earn most regardless of status');
console.log('      • Simple to understand - one formula for everyone');
console.log('      • Motivates all contributors equally');
console.log('      • No artificial hierarchies or complex pool management');
console.log('      • Scales naturally as team grows');
console.log('      • Fair for part-time and specialized contributors');

// ============================================================================
// CONFIGURATION OPTIONS
// ============================================================================

console.log('\n6️⃣ FLEXIBLE CONFIGURATION OPTIONS');

console.log('   🔧 REVENUE MODEL OPTIONS:');
console.log('   Option A: Pure Unified Pool (Current Demo)');
console.log('      • 90% contributor pool, 10% platform fee');
console.log('      • 100% contribution-based distribution');
console.log('      • No guaranteed minimums for anyone');
console.log('');
console.log('   Option B: Hybrid with Founder Safety Net');
console.log('      • 80% contribution pool, 10% founder minimum, 10% platform fee');
console.log('      • Founders get max(contribution_earned, minimum_guarantee)');
console.log('      • Still mostly merit-based but with founder protection');
console.log('');
console.log('   Option C: Separate Pools (Previous Demo)');
console.log('      • 50% core team, 40% gigwork, 10% platform fee');
console.log('      • More complex but provides guaranteed founder income');

console.log('\n' + '=' .repeat(80));
console.log('🎉 UNIFIED POOL DEMONSTRATION COMPLETE!\n');

console.log('🚀 KEY INSIGHTS:');
console.log('   • Unified pool creates true meritocracy');
console.log('   • Founders can still earn well if they contribute significantly');
console.log('   • High-performing gigwork contributors are properly rewarded');
console.log('   • System is much simpler to understand and manage');
console.log('   • Scales naturally without complex pool rebalancing');
console.log('   • More motivating for all types of contributors\n');

console.log('💡 RECOMMENDATION:');
console.log('   Start with unified pool for maximum fairness and simplicity.');
console.log('   Add founder safety net only if needed for initial funding/risk.');
console.log('   The system can be configured either way based on venture needs!\n');

console.log('=' .repeat(80));
console.log('✨ UNIFIED POOL SYSTEM - MAXIMUM FAIRNESS & SIMPLICITY! ✨');
console.log('=' .repeat(80));
