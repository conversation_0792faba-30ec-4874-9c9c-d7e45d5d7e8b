// Utility functions for mapping venture question answers to project data

/**
 * Maps venture question answers to complete project data structure
 * @param {Object} answers - Answers from the venture question flow
 * @returns {Object} Complete project data ready for database insertion
 */
export const mapVentureAnswersToProjectData = (answers) => {
  const baseData = {
    // Basic project information
    name: answers.ventureName || 'Untitled Venture',
    title: answers.ventureName || 'Untitled Venture',
    description: answers.ventureDescription || 'A collaborative venture project',
    project_type: mapProjectCategory(answers.projectCategory),
    
    // Timeline and scheduling
    estimated_duration: getTimelineInMonths(answers.timeline),
    start_date: new Date(),
    
    // Audience and visibility
    target_audience: answers.targetAudience || 'general',
    is_public: true,
    
    // Visual and categorization
    icon: answers.ventureIcon || '🚀',
    tags: answers.ventureTags || [],
    
    // Financial and legal structure
    royalty_model: createRoyaltyModel(answers),
    revenue_tranches: generateRevenueTranches(answers),
    
    // Project management structure
    contribution_tracking: generateContributionTracking(answers),
    milestones: generateMilestones(answers),
    
    // Agreement and legal
    agreement_type: answers.agreementType || 'simple',
    ip_ownership: answers.ipOwnership || 'shared',
    dispute_resolution: answers.disputeResolution || 'discussion'
  }
  
  // Add smart defaults based on project type and answers
  const smartDefaults = generateSmartDefaults(answers)
  
  return {
    ...baseData,
    ...smartDefaults
  }
}

/**
 * Maps project category to standardized project type
 * @param {string} category - Project category from questions
 * @returns {string} Standardized project type
 */
export const mapProjectCategory = (category) => {
  if (!category || category === 'unknown') {
    return 'software' // Default fallback
  }
  return category
}

/**
 * Converts timeline selection to months
 * @param {string} timeline - Timeline selection from questions
 * @returns {number} Duration in months
 */
export const getTimelineInMonths = (timeline) => {
  const timelineMap = {
    'quick': 1,
    'short': 2,
    'medium': 6,
    'long': 12,
    'flexible': 6
  }
  return timelineMap[timeline] || 6
}

/**
 * Creates royalty model based on revenue sharing preferences
 * @param {Object} answers - Venture answers containing revenue sharing info
 * @returns {Object} Royalty model configuration
 */
export const createRoyaltyModel = (answers) => {
  const { revenueSharing, budget, agreementType } = answers

  const baseModel = {
    model_type: 'contribution_based',
    base_percentage: 0,
    contribution_multiplier: 1.0,
    minimum_threshold: 0,
    calculation_method: 'dynamic'
  }

  // Map budget to royalty model type as expected by tests
  if (budget === 'funded') {
    return {
      ...baseModel,
      model_type: 'investment_based',
      contribution_multiplier: 0.5,
      minimum_threshold: 1000,
      calculation_method: 'tiered'
    }
  }

  if (budget === 'sweat_equity') {
    return {
      ...baseModel,
      model_type: 'equal_split',
      base_percentage: 50,
      contribution_multiplier: 0,
      calculation_method: 'fixed'
    }
  }

  if (budget === 'revenue_first') {
    return {
      ...baseModel,
      model_type: 'custom',
      calculation_method: 'manual'
    }
  }

  // Default for bootstrapped and others
  return baseModel
}

/**
 * Generates revenue tranches based on budget and timeline
 * @param {Object} answers - Venture answers
 * @returns {Array} Revenue tranche configuration
 */
export const generateRevenueTranches = (answers) => {
  const { budget, timeline, successMetrics } = answers
  
  const baseTranches = [
    {
      name: 'Initial Revenue',
      start_amount: 0,
      end_amount: 1000,
      percentage: 100,
      description: 'First revenue milestone'
    }
  ]
  
  if (successMetrics === 'revenue' || budget === 'revenue_first') {
    baseTranches.push(
      {
        name: 'Growth Phase',
        start_amount: 1000,
        end_amount: 10000,
        percentage: 80,
        description: 'Revenue growth phase'
      },
      {
        name: 'Scale Phase',
        start_amount: 10000,
        end_amount: null,
        percentage: 60,
        description: 'Scaling revenue phase'
      }
    )
  }
  
  return baseTranches
}

/**
 * Generates contribution tracking categories based on project type
 * @param {Object} answers - Venture answers
 * @returns {Object} Contribution tracking configuration
 */
export const generateContributionTracking = (answers) => {
  const { projectCategory } = answers
  
  const categoryMap = {
    software: ['Development', 'Design', 'Testing', 'Documentation', 'DevOps'],
    creative: ['Design', 'Content', 'Research', 'Review'],
    business: ['Strategy', 'Research', 'Communication', 'Management'],
    physical: ['Design', 'Prototyping', 'Manufacturing', 'Quality']
  }
  
  return {
    categories: categoryMap[projectCategory] || categoryMap.software,
    tracking_method: 'time_and_impact',
    weight_system: 'balanced'
  }
}

/**
 * Generates smart defaults based on project type and answers
 * @param {Object} answers - Venture answers
 * @returns {Object} Smart defaults to merge with base data
 */
export const generateSmartDefaults = (answers) => {
  const { projectCategory, timeline, budget } = answers

  const contributionTracking = generateContributionTracking(answers)
  const milestoneTemplates = getMilestoneTemplates(projectCategory, answers.successMetrics)
  const revenueTranches = generateRevenueTranches(answers)

  const defaults = {
    // Default team size based on project type
    max_team_size: projectCategory === 'software' ? 8 : 6,

    // Default privacy settings
    requires_approval: budget === 'funded',

    // Default communication settings
    notification_frequency: timeline === 'quick' ? 'daily' : 'weekly',

    // Include contribution categories for tests
    contribution_categories: contributionTracking.categories,

    // Include milestone templates for tests
    milestone_templates: milestoneTemplates,

    // Include revenue tranches for tests
    revenue_tranches: revenueTranches
  }

  return defaults
}

/**
 * Generates project milestones based on timeline and project type
 * @param {Object} answers - Venture answers
 * @returns {Array} Generated milestones
 */
export const generateMilestones = (answers) => {
  const { timeline, projectCategory, successMetrics } = answers
  const duration = getTimelineInMonths(timeline)
  
  const milestoneTemplates = getMilestoneTemplates(projectCategory, successMetrics)
  const milestoneCount = Math.min(milestoneTemplates.length, timeline === 'quick' ? 3 : timeline === 'long' ? 6 : 4)
  
  return milestoneTemplates.slice(0, milestoneCount).map((template, index) => ({
    name: template.name,
    description: template.description,
    target_date: new Date(Date.now() + (duration / milestoneCount) * (index + 1) * 30 * 24 * 60 * 60 * 1000),
    percentage: Math.round(((index + 1) / milestoneCount) * 100),
    is_required: template.required || false
  }))
}

/**
 * Gets milestone templates based on project type and success metrics
 * @param {string} projectCategory - Type of project
 * @param {string} successMetrics - Success measurement preference
 * @returns {Array} Milestone templates
 */
const getMilestoneTemplates = (projectCategory, successMetrics) => {
  const templates = {
    software: [
      { name: 'MVP Development', description: 'Complete minimum viable product', required: true },
      { name: 'Beta Testing', description: 'User testing and feedback collection' },
      { name: 'Launch', description: 'Public release and marketing', required: true },
      { name: 'User Growth', description: 'Achieve user acquisition targets' },
      { name: 'Revenue Generation', description: 'First revenue milestone' },
      { name: 'Scale Operations', description: 'Expand and optimize' }
    ],
    creative: [
      { name: 'Concept Development', description: 'Finalize creative concept', required: true },
      { name: 'Production', description: 'Create the creative work' },
      { name: 'Review & Refinement', description: 'Polish and improve' },
      { name: 'Release', description: 'Publish or distribute', required: true },
      { name: 'Promotion', description: 'Marketing and audience building' },
      { name: 'Community Building', description: 'Engage with audience' }
    ],
    business: [
      { name: 'Service Framework', description: 'Define service offerings', required: true },
      { name: 'Client Acquisition', description: 'Secure first clients' },
      { name: 'Delivery Excellence', description: 'Prove service quality' },
      { name: 'Scale Operations', description: 'Expand service capacity', required: true },
      { name: 'Market Expansion', description: 'Enter new markets' },
      { name: 'Strategic Partnerships', description: 'Build key relationships' }
    ]
  }
  
  let milestones = templates[projectCategory] || templates.software
  
  // Customize based on success metrics
  if (successMetrics === 'revenue') {
    // Add revenue-focused milestones and mark them as required
    milestones = milestones.map(m =>
      m.name.toLowerCase().includes('revenue') || m.name.toLowerCase().includes('scale')
        ? { ...m, required: true }
        : m
    )
    // Ensure we have revenue-focused milestones
    if (!milestones.some(m => m.name.toLowerCase().includes('revenue'))) {
      milestones.push({ name: 'Revenue Target', description: 'Achieve revenue goals', required: true })
    }
  } else if (successMetrics === 'users') {
    milestones = milestones.map(m =>
      m.name.toLowerCase().includes('user') || m.name.toLowerCase().includes('community')
        ? { ...m, required: true }
        : m
    )
    // Ensure we have user-focused milestones
    if (!milestones.some(m => m.name.toLowerCase().includes('user'))) {
      milestones.push({ name: 'User Acquisition', description: 'Achieve user growth targets', required: true })
    }
  }
  
  return milestones
}
