// Contribution Validation API function using ES modules
import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client
const initSupabase = () => {
  const supabaseUrl = Netlify.env.get("SUPABASE_URL");
  const supabaseKey = Netlify.env.get("SUPABASE_SERVICE_KEY") || Netlify.env.get("SUPABASE_ANON_KEY");

  if (!supabaseUrl || !supabaseKey) {
    throw new Error('Missing Supabase credentials');
  }

  return createClient(supabaseUrl, supabaseKey);
};

// Main function handler
export default async (req, context) => {
  // Set CORS headers for cross-origin access
  const headers = {
    'Content-Type': 'application/json',
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS'
  };

  // Handle OPTIONS request for CORS preflight
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      status: 204,
      headers
    });
  }

  try {
    // Initialize Supabase client
    const supabase = initSupabase();

    // Get user from context
    const { user } = context.clientContext;

    if (!user) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Unauthorized'
        }),
        {
          status: 401,
          headers
        }
      );
    }

    // Handle different HTTP methods
    if (req.method === 'GET') {
      // Get validation for a specific contribution or list validations
      const url = new URL(req.url);
      const contributionId = url.searchParams.get('contributionId');
      const projectId = url.searchParams.get('projectId');

      let query = supabase.from('contribution_validations').select(`
        id,
        contribution_id,
        validator_id,
        status,
        feedback,
        created_at,
        updated_at,
        contributions(
          id,
          project_id,
          user_id,
          task_name,
          description,
          status,
          validation_status
        )
      `);

      if (contributionId) {
        query = query.eq('contribution_id', contributionId);
      } else if (projectId) {
        query = query.eq('contributions.project_id', projectId);
      }

      const { data, error } = await query;

      if (error) {
        console.error('Error fetching validations:', error);
        return new Response(
          JSON.stringify({
            success: false,
            error: error.message
          }),
          {
            status: 500,
            headers
          }
        );
      }

      return new Response(
        JSON.stringify({
          success: true,
          data
        }),
        { headers }
      );

    } else if (req.method === 'POST') {
      // Create a new validation
      const body = await req.json();

      const { contributionId, status, feedback } = body;

      if (!contributionId || !status) {
        return new Response(
          JSON.stringify({
            success: false,
            error: 'Missing required fields'
          }),
          {
            status: 400,
            headers
          }
        );
      }

      // Check if the user is a project admin
      const { data: contribution, error: contributionError } = await supabase
        .from('contributions')
        .select('project_id')
        .eq('id', contributionId)
        .single();

      if (contributionError) {
        console.error('Error fetching contribution:', contributionError);
        return new Response(
          JSON.stringify({
            success: false,
            error: contributionError.message
          }),
          {
            status: 500,
            headers
          }
        );
      }

      const { data: projectContributor, error: projectContributorError } = await supabase
        .from('project_contributors')
        .select('permission_level, is_admin')
        .eq('project_id', contribution.project_id)
        .eq('user_id', user.sub)
        .single();

      if (projectContributorError) {
        console.error('Error fetching project contributor:', projectContributorError);
        return new Response(
          JSON.stringify({
            success: false,
            error: 'Unauthorized'
          }),
          {
            status: 401,
            headers
          }
        );
      }

      // Check if user is an admin or has owner/admin permission level
      const isAdmin = projectContributor.is_admin ||
                     ['Owner', 'Admin'].includes(projectContributor.permission_level);

      if (!isAdmin) {
        return new Response(
          JSON.stringify({
            success: false,
            error: 'Only project admins can validate contributions'
          }),
          {
            status: 403,
            headers
          }
        );
      }

      // Create the validation
      const { data: validation, error: validationError } = await supabase
        .from('contribution_validations')
        .insert({
          contribution_id: contributionId,
          validator_id: user.sub,
          status,
          feedback
        })
        .select()
        .single();

      if (validationError) {
        console.error('Error creating validation:', validationError);
        return new Response(
          JSON.stringify({
            success: false,
            error: validationError.message
          }),
          {
            status: 500,
            headers
          }
        );
      }

      return new Response(
        JSON.stringify({
          success: true,
          data: validation
        }),
        { headers }
      );

    } else if (req.method === 'PUT') {
      // Update an existing validation
      const body = await req.json();

      const { id, status, feedback } = body;

      if (!id || !status) {
        return new Response(
          JSON.stringify({
            success: false,
            error: 'Missing required fields'
          }),
          {
            status: 400,
            headers
          }
        );
      }

      // Update the validation
      const { data: validation, error: validationError } = await supabase
        .from('contribution_validations')
        .update({
          status,
          feedback,
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
        .select()
        .single();

      if (validationError) {
        console.error('Error updating validation:', validationError);
        return new Response(
          JSON.stringify({
            success: false,
            error: validationError.message
          }),
          {
            status: 500,
            headers
          }
        );
      }

      return new Response(
        JSON.stringify({
          success: true,
          data: validation
        }),
        { headers }
      );

    } else {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Method not allowed'
        }),
        {
          status: 405,
          headers
        }
      );
    }

  } catch (error) {
    console.error('Error in contribution-validation function:', error);

    return new Response(
      JSON.stringify({
        success: false,
        error: error.message
      }),
      {
        status: 500,
        headers
      }
    );
  }
};

// Configure the function path
export const config = {
  path: "/api/contribution-validation"
};
