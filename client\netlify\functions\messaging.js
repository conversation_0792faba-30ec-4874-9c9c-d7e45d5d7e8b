// Direct Messaging API
// Backend Specialist: Real-time messaging system for ally communication
const { createClient } = require('@supabase/supabase-js');

// Supabase client
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_KEY
);

// Helper function to get user from request
const getUserFromRequest = (event) => {
  const authHeader = event.headers.authorization;
  if (!authHeader) return null;
  
  try {
    const token = authHeader.replace('Bearer ', '');
    const payload = JSON.parse(Buffer.from(token.split('.')[1], 'base64').toString());
    return payload.sub;
  } catch {
    return null;
  }
};

// Get User's Conversations
const getConversations = async (event) => {
  try {
    const userId = getUserFromRequest(event);
    if (!userId) {
      return {
        statusCode: 401,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    const queryParams = new URLSearchParams(event.queryStringParameters || {});
    const type = queryParams.get('type') || 'all'; // 'direct', 'group', 'alliance', 'project', 'all'
    const limit = parseInt(queryParams.get('limit') || '20');

    let query = supabase
      .from('conversations')
      .select(`
        id,
        conversation_type,
        title,
        description,
        last_message_at,
        is_archived,
        alliance:teams(id, name),
        project:projects(id, name, title),
        participants:conversation_participants!inner(
          user_id,
          role,
          is_muted,
          last_read_at,
          user:users(
            id,
            display_name,
            avatar_url
          )
        )
      `)
      .eq('conversation_participants.user_id', userId)
      .is('conversation_participants.left_at', null)
      .order('last_message_at', { ascending: false })
      .limit(limit);

    if (type !== 'all') {
      query = query.eq('conversation_type', type);
    }

    const { data: conversations, error: conversationsError } = await query;

    if (conversationsError) {
      throw new Error(`Failed to fetch conversations: ${conversationsError.message}`);
    }

    // Get last message and unread count for each conversation
    const enhancedConversations = await Promise.all(
      conversations.map(async (conversation) => {
        // Get last message
        const { data: lastMessage } = await supabase
          .from('messages')
          .select(`
            id,
            content,
            message_type,
            created_at,
            sender:users!messages_sender_id_fkey(
              id,
              display_name
            )
          `)
          .eq('conversation_id', conversation.id)
          .eq('is_deleted', false)
          .order('created_at', { ascending: false })
          .limit(1)
          .single();

        // Get unread count
        const userParticipant = conversation.participants.find(p => p.user_id === userId);
        const { count: unreadCount } = await supabase
          .from('messages')
          .select('id', { count: 'exact' })
          .eq('conversation_id', conversation.id)
          .neq('sender_id', userId)
          .gt('created_at', userParticipant?.last_read_at || '1970-01-01')
          .eq('is_deleted', false);

        // Get other participants (exclude current user)
        const otherParticipants = conversation.participants.filter(p => p.user_id !== userId);

        return {
          id: conversation.id,
          type: conversation.conversation_type,
          title: conversation.title,
          description: conversation.description,
          last_message_at: conversation.last_message_at,
          is_archived: conversation.is_archived,
          is_muted: userParticipant?.is_muted || false,
          alliance: conversation.alliance,
          project: conversation.project,
          participants: otherParticipants.map(p => p.user),
          participant_count: conversation.participants.length,
          last_message: lastMessage,
          unread_count: unreadCount || 0,
          user_role: userParticipant?.role || 'member'
        };
      })
    );

    return {
      statusCode: 200,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        conversations: enhancedConversations,
        total: enhancedConversations.length
      })
    };

  } catch (error) {
    console.error('Get conversations error:', error);
    return {
      statusCode: 500,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ error: 'Failed to fetch conversations' })
    };
  }
};

// Start Direct Conversation
const startDirectConversation = async (event) => {
  try {
    const userId = getUserFromRequest(event);
    if (!userId) {
      return {
        statusCode: 401,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    const data = JSON.parse(event.body);
    
    if (!data.recipient_id) {
      return {
        statusCode: 400,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'recipient_id is required' })
      };
    }

    // Check if users are allies
    const { data: allyConnection } = await supabase
      .from('user_allies')
      .select('id')
      .or(`and(user_id.eq.${userId},ally_id.eq.${data.recipient_id}),and(user_id.eq.${data.recipient_id},ally_id.eq.${userId})`)
      .eq('status', 'accepted')
      .single();

    if (!allyConnection) {
      return {
        statusCode: 403,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Can only message allies. Send a friend request first.' })
      };
    }

    // Create or get existing direct conversation
    const { data: conversationId, error: conversationError } = await supabase
      .rpc('create_direct_conversation', {
        user1_id: userId,
        user2_id: data.recipient_id
      });

    if (conversationError) {
      throw new Error(`Failed to create conversation: ${conversationError.message}`);
    }

    // Get conversation details
    const { data: conversation, error: fetchError } = await supabase
      .from('conversations')
      .select(`
        id,
        conversation_type,
        created_at,
        participants:conversation_participants(
          user_id,
          role,
          user:users(
            id,
            display_name,
            avatar_url
          )
        )
      `)
      .eq('id', conversationId)
      .single();

    if (fetchError) {
      throw new Error(`Failed to fetch conversation: ${fetchError.message}`);
    }

    // Send initial message if provided
    if (data.initial_message) {
      const { data: message, error: messageError } = await supabase
        .from('messages')
        .insert([{
          conversation_id: conversationId,
          sender_id: userId,
          content: data.initial_message,
          message_type: 'text'
        }])
        .select()
        .single();

      if (messageError) {
        console.error('Failed to send initial message:', messageError.message);
      }
    }

    return {
      statusCode: 201,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        conversation: {
          id: conversation.id,
          type: conversation.conversation_type,
          participants: conversation.participants.map(p => p.user),
          created_at: conversation.created_at
        }
      })
    };

  } catch (error) {
    console.error('Start direct conversation error:', error);
    return {
      statusCode: 500,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ error: 'Failed to start conversation' })
    };
  }
};

// Get Conversation Messages
const getConversationMessages = async (event) => {
  try {
    const userId = getUserFromRequest(event);
    if (!userId) {
      return {
        statusCode: 401,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    const conversationId = event.path.split('/').pop();
    const queryParams = new URLSearchParams(event.queryStringParameters || {});
    const limit = parseInt(queryParams.get('limit') || '50');
    const before = queryParams.get('before'); // For pagination

    // Verify user has access to conversation
    const { data: participant, error: participantError } = await supabase
      .from('conversation_participants')
      .select('id, role')
      .eq('conversation_id', conversationId)
      .eq('user_id', userId)
      .is('left_at', null)
      .single();

    if (participantError) {
      return {
        statusCode: 403,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Access denied to this conversation' })
      };
    }

    // Get messages
    let query = supabase
      .from('messages')
      .select(`
        id,
        content,
        message_type,
        attachment_url,
        attachment_name,
        attachment_type,
        attachment_size,
        reply_to_id,
        thread_id,
        is_edited,
        edited_at,
        created_at,
        sender:users!messages_sender_id_fkey(
          id,
          display_name,
          avatar_url
        ),
        reactions:message_reactions(
          id,
          reaction,
          user:users(
            id,
            display_name
          )
        )
      `)
      .eq('conversation_id', conversationId)
      .eq('is_deleted', false)
      .order('created_at', { ascending: false })
      .limit(limit);

    if (before) {
      query = query.lt('created_at', before);
    }

    const { data: messages, error: messagesError } = await query;

    if (messagesError) {
      throw new Error(`Failed to fetch messages: ${messagesError.message}`);
    }

    // Update last read timestamp
    await supabase
      .from('conversation_participants')
      .update({ last_read_at: new Date().toISOString() })
      .eq('conversation_id', conversationId)
      .eq('user_id', userId);

    return {
      statusCode: 200,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        messages: messages.reverse(), // Return in chronological order
        total: messages.length,
        has_more: messages.length === limit
      })
    };

  } catch (error) {
    console.error('Get conversation messages error:', error);
    return {
      statusCode: 500,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ error: 'Failed to fetch messages' })
    };
  }
};

// Send Message
const sendMessage = async (event) => {
  try {
    const userId = getUserFromRequest(event);
    if (!userId) {
      return {
        statusCode: 401,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    const data = JSON.parse(event.body);
    
    if (!data.conversation_id || (!data.content && !data.attachment_url)) {
      return {
        statusCode: 400,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          error: 'conversation_id and either content or attachment_url are required' 
        })
      };
    }

    // Verify user can send messages to this conversation
    const { data: participant, error: participantError } = await supabase
      .from('conversation_participants')
      .select('id')
      .eq('conversation_id', data.conversation_id)
      .eq('user_id', userId)
      .is('left_at', null)
      .single();

    if (participantError) {
      return {
        statusCode: 403,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Cannot send messages to this conversation' })
      };
    }

    // Create message
    const messageData = {
      conversation_id: data.conversation_id,
      sender_id: userId,
      content: data.content || '',
      message_type: data.message_type || 'text',
      reply_to_id: data.reply_to_id || null,
      thread_id: data.thread_id || null,
      attachment_url: data.attachment_url || null,
      attachment_name: data.attachment_name || null,
      attachment_type: data.attachment_type || null,
      attachment_size: data.attachment_size || null,
      metadata: data.metadata || {}
    };

    const { data: message, error: messageError } = await supabase
      .from('messages')
      .insert([messageData])
      .select(`
        id,
        content,
        message_type,
        attachment_url,
        attachment_name,
        attachment_type,
        reply_to_id,
        thread_id,
        created_at,
        sender:users!messages_sender_id_fkey(
          id,
          display_name,
          avatar_url
        )
      `)
      .single();

    if (messageError) {
      throw new Error(`Failed to send message: ${messageError.message}`);
    }

    // Update conversation last_message_at
    await supabase
      .from('conversations')
      .update({ last_message_at: new Date().toISOString() })
      .eq('id', data.conversation_id);

    // Create activity feed entry
    await createMessageActivity(message, data.conversation_id);

    return {
      statusCode: 201,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ message })
    };

  } catch (error) {
    console.error('Send message error:', error);
    return {
      statusCode: 500,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ error: 'Failed to send message' })
    };
  }
};

// Helper function to create activity feed entry for messages
const createMessageActivity = async (message, conversationId) => {
  try {
    // Get conversation details for context
    const { data: conversation } = await supabase
      .from('conversations')
      .select('conversation_type, alliance_id, project_id')
      .eq('id', conversationId)
      .single();

    if (!conversation) return;

    const activityData = {
      activity_type: 'message_sent',
      activity_title: 'New message',
      activity_description: message.content?.substring(0, 100) || 'Sent a file',
      actor_id: message.sender.id,
      conversation_id: conversationId,
      message_id: message.id,
      alliance_id: conversation.alliance_id,
      project_id: conversation.project_id,
      visibility: conversation.conversation_type === 'direct' ? 'private' : 'alliance',
      metadata: {
        message_type: message.message_type,
        has_attachment: !!message.attachment_url
      }
    };

    await supabase
      .from('activity_feeds')
      .insert([activityData]);

  } catch (error) {
    console.error('Create message activity error:', error);
  }
};

// Route requests to appropriate handlers
exports.handler = async (event, context) => {
  // Add CORS headers
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS'
  };

  // Handle preflight requests
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers,
      body: ''
    };
  }

  const path = event.path.replace('/.netlify/functions/messaging', '');

  try {
    let response;

    if (event.httpMethod === 'GET') {
      if (path === '' || path === '/') {
        response = await getConversations(event);
      } else if (path.includes('/messages')) {
        response = await getConversationMessages(event);
      } else {
        response = {
          statusCode: 404,
          body: JSON.stringify({ error: 'Endpoint not found' })
        };
      }
    } else if (event.httpMethod === 'POST') {
      if (path === '/direct') {
        response = await startDirectConversation(event);
      } else if (path === '/send') {
        response = await sendMessage(event);
      } else {
        response = {
          statusCode: 404,
          body: JSON.stringify({ error: 'Endpoint not found' })
        };
      }
    } else {
      response = {
        statusCode: 405,
        body: JSON.stringify({ error: 'Method not allowed' })
      };
    }

    // Add CORS headers to response
    response.headers = { ...response.headers, ...headers };
    return response;

  } catch (error) {
    console.error('Messaging API error:', error);
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ error: 'Internal server error' })
    };
  }
};
