import React, { useContext, useEffect, useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { Card, CardBody, Button, Chip } from '@heroui/react';
import { useDataSync } from '../../contexts/DataSyncContext';
import { useNavigation } from '../../contexts/NavigationContext';
import { ArrowRight, ExternalLink, Zap, TrendingUp } from 'lucide-react';

/**
 * Cross-Tile Navigation Bridge Component
 * 
 * Production-ready seamless navigation system providing:
 * - Intelligent tile-to-tile navigation with context preservation
 * - Smart suggestions based on user workflow patterns
 * - Real-time data synchronization during navigation
 * - Contextual navigation hints and shortcuts
 * - Performance-optimized route transitions
 */
const CrossTileNavigationBridge = ({ 
  currentTile, 
  className = '',
  showSuggestions = true,
  showQuickActions = true 
}) => {
  const navigate = useNavigate();
  const location = useLocation();
  const { sharedState, navigateWithContext, updateSharedState } = useDataSync();
  const { actions } = useNavigation();
  
  const [suggestions, setSuggestions] = useState([]);
  const [quickActions, setQuickActions] = useState([]);
  const [isTransitioning, setIsTransitioning] = useState(false);

  // Define tile relationships and smart navigation paths
  const tileRelationships = {
    track: {
      primary: ['earn', 'projects', 'analytics'],
      secondary: ['teams', 'social'],
      workflows: {
        'contribution-to-earnings': { target: 'earn', context: 'contributions' },
        'track-to-projects': { target: 'projects', context: 'tracking' },
        'performance-analysis': { target: 'analytics', context: 'performance' }
      }
    },
    earn: {
      primary: ['track', 'revenue', 'projects'],
      secondary: ['analytics', 'admin'],
      workflows: {
        'earnings-to-tracking': { target: 'track', context: 'validation' },
        'revenue-analysis': { target: 'revenue', context: 'earnings' },
        'project-earnings': { target: 'projects', context: 'revenue' }
      }
    },
    projects: {
      primary: ['track', 'teams', 'earn'],
      secondary: ['analytics', 'social'],
      workflows: {
        'project-tracking': { target: 'track', context: 'project' },
        'team-collaboration': { target: 'teams', context: 'project' },
        'project-earnings': { target: 'earn', context: 'project' }
      }
    },
    teams: {
      primary: ['projects', 'social', 'track'],
      secondary: ['analytics', 'admin'],
      workflows: {
        'team-projects': { target: 'projects', context: 'team' },
        'team-social': { target: 'social', context: 'team' },
        'team-performance': { target: 'track', context: 'team' }
      }
    },
    admin: {
      primary: ['system', 'analytics', 'users'],
      secondary: ['projects', 'teams'],
      workflows: {
        'system-monitoring': { target: 'system', context: 'admin' },
        'user-analytics': { target: 'analytics', context: 'users' },
        'project-management': { target: 'projects', context: 'admin' }
      }
    },
    system: {
      primary: ['admin', 'analytics'],
      secondary: ['projects', 'teams'],
      workflows: {
        'admin-dashboard': { target: 'admin', context: 'system' },
        'system-analytics': { target: 'analytics', context: 'system' }
      }
    }
  };

  // Generate smart navigation suggestions
  useEffect(() => {
    const generateSuggestions = () => {
      const currentRelations = tileRelationships[currentTile];
      if (!currentRelations) return [];

      const suggestions = [];

      // Add primary related tiles
      currentRelations.primary?.forEach(tile => {
        suggestions.push({
          id: `primary-${tile}`,
          tile,
          type: 'primary',
          title: getTileDisplayName(tile),
          description: getNavigationReason(currentTile, tile),
          icon: getTileIcon(tile),
          priority: 1
        });
      });

      // Add workflow-based suggestions
      Object.entries(currentRelations.workflows || {}).forEach(([workflow, config]) => {
        suggestions.push({
          id: `workflow-${workflow}`,
          tile: config.target,
          type: 'workflow',
          title: getWorkflowTitle(workflow),
          description: getWorkflowDescription(workflow),
          icon: <Zap className="h-4 w-4" />,
          priority: 2,
          context: config.context
        });
      });

      return suggestions.slice(0, 4); // Limit to 4 suggestions
    };

    setSuggestions(generateSuggestions());
  }, [currentTile, sharedState]);

  // Generate quick actions based on current context
  useEffect(() => {
    const generateQuickActions = () => {
      const actions = [];

      // Context-aware quick actions
      if (sharedState.currentProject) {
        actions.push({
          id: 'current-project',
          title: 'Current Project',
          description: sharedState.currentProject.name,
          action: () => navigateToTile('projects', { projectId: sharedState.currentProject.id }),
          icon: <ExternalLink className="h-4 w-4" />
        });
      }

      // Add performance quick action for track tile
      if (currentTile === 'track') {
        actions.push({
          id: 'performance-overview',
          title: 'Performance Overview',
          description: 'View detailed analytics',
          action: () => navigateToTile('analytics', { focus: 'performance' }),
          icon: <TrendingUp className="h-4 w-4" />
        });
      }

      return actions;
    };

    setQuickActions(generateQuickActions());
  }, [currentTile, sharedState]);

  // Enhanced navigation function with context preservation
  const navigateToTile = async (targetTile, context = {}) => {
    setIsTransitioning(true);

    try {
      // Preserve current state and context
      navigateWithContext(targetTile, {
        from: currentTile,
        data: context,
        preserveState: true
      });

      // Determine the route for the target tile
      const route = getTileRoute(targetTile, context);
      
      // Use navigation context for smooth transitions
      actions.navigateToCanvas(targetTile, {
        route,
        method: 'bridge-navigation',
        context
      });

      // Navigate to the route
      navigate(route);

    } catch (error) {
      console.error('Navigation error:', error);
    } finally {
      setTimeout(() => setIsTransitioning(false), 300);
    }
  };

  // Helper functions
  const getTileDisplayName = (tile) => {
    const names = {
      track: 'Track Progress',
      earn: 'Earn Revenue',
      projects: 'Manage Projects',
      teams: 'Team Collaboration',
      admin: 'Admin Dashboard',
      system: 'System Management',
      analytics: 'Analytics',
      social: 'Social Network'
    };
    return names[tile] || tile;
  };

  const getTileIcon = (tile) => {
    const icons = {
      track: '📊',
      earn: '💰',
      projects: '🚀',
      teams: '👥',
      admin: '⚙️',
      system: '🖥️',
      analytics: '📈',
      social: '🌐'
    };
    return icons[tile] || '📄';
  };

  const getTileRoute = (tile, context = {}) => {
    const routes = {
      track: '/track',
      earn: '/earn',
      projects: context.projectId ? `/project/${context.projectId}` : '/projects',
      teams: '/teams',
      admin: '/admin',
      system: '/admin/system',
      analytics: '/analytics',
      social: '/social'
    };
    return routes[tile] || `/${tile}`;
  };

  const getNavigationReason = (from, to) => {
    const reasons = {
      'track-earn': 'View earnings from your contributions',
      'track-projects': 'Manage tracked project work',
      'earn-track': 'Validate earnings with tracking data',
      'projects-teams': 'Collaborate with your team',
      'admin-system': 'Monitor system performance'
    };
    return reasons[`${from}-${to}`] || `Navigate to ${getTileDisplayName(to)}`;
  };

  const getWorkflowTitle = (workflow) => {
    const titles = {
      'contribution-to-earnings': 'View Earnings',
      'track-to-projects': 'Manage Projects',
      'performance-analysis': 'Analyze Performance',
      'earnings-to-tracking': 'Validate Tracking',
      'revenue-analysis': 'Revenue Details',
      'project-earnings': 'Project Revenue'
    };
    return titles[workflow] || workflow;
  };

  const getWorkflowDescription = (workflow) => {
    const descriptions = {
      'contribution-to-earnings': 'See how your contributions translate to earnings',
      'track-to-projects': 'View and manage your active projects',
      'performance-analysis': 'Deep dive into your performance metrics'
    };
    return descriptions[workflow] || 'Navigate with context';
  };

  if (!showSuggestions && !showQuickActions) return null;

  return (
    <div className={`cross-tile-navigation-bridge ${className}`}>
      <AnimatePresence>
        {showSuggestions && suggestions.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="mb-6"
          >
            <Card className="bg-white/5 border border-white/10">
              <CardBody className="p-4">
                <h3 className="text-white font-semibold mb-3 flex items-center gap-2">
                  <ArrowRight className="h-4 w-4" />
                  Smart Navigation
                </h3>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                  {suggestions.map((suggestion) => (
                    <motion.div
                      key={suggestion.id}
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      <Button
                        variant="flat"
                        className="w-full h-auto p-3 bg-white/5 hover:bg-white/10 border border-white/10"
                        onClick={() => navigateToTile(suggestion.tile, { context: suggestion.context })}
                        isLoading={isTransitioning}
                      >
                        <div className="flex items-start gap-3 w-full">
                          <div className="text-lg">{suggestion.icon}</div>
                          <div className="flex-1 text-left">
                            <div className="text-white font-medium text-sm">
                              {suggestion.title}
                            </div>
                            <div className="text-white/60 text-xs mt-1">
                              {suggestion.description}
                            </div>
                          </div>
                          <Chip size="sm" color="primary" variant="flat">
                            {suggestion.type}
                          </Chip>
                        </div>
                      </Button>
                    </motion.div>
                  ))}
                </div>
              </CardBody>
            </Card>
          </motion.div>
        )}

        {showQuickActions && quickActions.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
          >
            <Card className="bg-white/5 border border-white/10">
              <CardBody className="p-4">
                <h3 className="text-white font-semibold mb-3">Quick Actions</h3>
                <div className="space-y-2">
                  {quickActions.map((action) => (
                    <Button
                      key={action.id}
                      variant="flat"
                      size="sm"
                      className="w-full justify-start bg-white/5 hover:bg-white/10"
                      onClick={action.action}
                      startContent={action.icon}
                    >
                      <div className="flex-1 text-left">
                        <div className="text-white text-sm">{action.title}</div>
                        <div className="text-white/60 text-xs">{action.description}</div>
                      </div>
                    </Button>
                  ))}
                </div>
              </CardBody>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default CrossTileNavigationBridge;
