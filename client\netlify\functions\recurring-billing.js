// Recurring Billing API
// Backend Specialist: Subscription and recurring fee management system
const { createClient } = require('@supabase/supabase-js');

// Supabase client
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_KEY
);

// Helper function to get user from request
const getUserFromRequest = (event) => {
  const authHeader = event.headers.authorization;
  if (!authHeader) return null;
  
  try {
    const token = authHeader.replace('Bearer ', '');
    const payload = JSON.parse(Buffer.from(token.split('.')[1], 'base64').toString());
    return payload.sub;
  } catch {
    return null;
  }
};

// Helper function to calculate next billing date
const calculateNextBillingDate = (frequency, currentDate, billingDay = 1) => {
  const date = new Date(currentDate);
  
  switch (frequency) {
    case 'weekly':
      date.setDate(date.getDate() + 7);
      break;
    case 'monthly':
      date.setMonth(date.getMonth() + 1);
      date.setDate(billingDay);
      break;
    case 'quarterly':
      date.setMonth(date.getMonth() + 3);
      date.setDate(billingDay);
      break;
    case 'semi_annually':
      date.setMonth(date.getMonth() + 6);
      date.setDate(billingDay);
      break;
    case 'annually':
      date.setFullYear(date.getFullYear() + 1);
      date.setDate(billingDay);
      break;
    default:
      date.setMonth(date.getMonth() + 1);
  }
  
  return date.toISOString().split('T')[0];
};

// Helper function to generate invoice number
const generateInvoiceNumber = (scheduleId, transactionDate) => {
  const date = new Date(transactionDate);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const scheduleShort = scheduleId.substring(0, 8);
  
  return `INV-${year}${month}${day}-${scheduleShort}`;
};

// Create Recurring Billing Schedule
const createBillingSchedule = async (event) => {
  try {
    const userId = getUserFromRequest(event);
    if (!userId) {
      return {
        statusCode: 401,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    const data = JSON.parse(event.body);
    
    // Validate required fields
    if (!data.billing_type || !data.service_description || !data.amount || !data.frequency) {
      return {
        statusCode: 400,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          error: 'billing_type, service_description, amount, and frequency are required' 
        })
      };
    }

    const amount = parseFloat(data.amount);
    if (isNaN(amount) || amount <= 0) {
      return {
        statusCode: 400,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Valid amount required' })
      };
    }

    // Validate billing entity (customer, alliance, or venture)
    if (!data.customer_id && !data.alliance_id && !data.venture_id) {
      return {
        statusCode: 400,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          error: 'Either customer_id, alliance_id, or venture_id is required' 
        })
      };
    }

    // Check permissions for alliance or venture billing
    if (data.alliance_id) {
      const { data: allianceMember } = await supabase
        .from('team_members')
        .select('role, status')
        .eq('team_id', data.alliance_id)
        .eq('user_id', userId)
        .eq('status', 'active')
        .single();

      if (!allianceMember || !['founder', 'owner', 'admin'].includes(allianceMember.role)) {
        return {
          statusCode: 403,
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ error: 'Insufficient permissions for alliance billing' })
        };
      }
    }

    if (data.venture_id) {
      const { data: venture } = await supabase
        .from('projects')
        .select('created_by, alliance_id')
        .eq('id', data.venture_id)
        .single();

      if (!venture || (venture.created_by !== userId && !venture.alliance_id)) {
        return {
          statusCode: 403,
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ error: 'Insufficient permissions for venture billing' })
        };
      }

      // Check alliance permissions if venture belongs to alliance
      if (venture.alliance_id) {
        const { data: allianceMember } = await supabase
          .from('team_members')
          .select('role, status')
          .eq('team_id', venture.alliance_id)
          .eq('user_id', userId)
          .eq('status', 'active')
          .single();

        if (!allianceMember || !['founder', 'owner', 'admin'].includes(allianceMember.role)) {
          return {
            statusCode: 403,
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ error: 'Insufficient permissions for venture billing' })
          };
        }
      }
    }

    // Calculate start date and next billing date
    const startDate = data.start_date || new Date().toISOString().split('T')[0];
    const billingDay = data.billing_day || 1;
    const nextBillingDate = calculateNextBillingDate(data.frequency, startDate, billingDay);

    // Create billing schedule
    const scheduleData = {
      customer_id: data.customer_id || null,
      alliance_id: data.alliance_id || null,
      venture_id: data.venture_id || null,
      billing_type: data.billing_type,
      service_description: data.service_description,
      amount: amount,
      currency: data.currency || 'USD',
      frequency: data.frequency,
      billing_day: billingDay,
      start_date: startDate,
      end_date: data.end_date || null,
      next_billing_date: nextBillingDate,
      auto_charge: data.auto_charge !== undefined ? data.auto_charge : true,
      send_invoice: data.send_invoice !== undefined ? data.send_invoice : true,
      grace_period_days: data.grace_period_days || 7,
      late_fee_amount: data.late_fee_amount || 0,
      late_fee_percentage: data.late_fee_percentage || 0,
      payment_method_id: data.payment_method_id || null,
      backup_payment_method_id: data.backup_payment_method_id || null,
      billing_metadata: data.billing_metadata || {},
      created_by: userId
    };

    const { data: schedule, error: scheduleError } = await supabase
      .from('recurring_billing_schedules')
      .insert([scheduleData])
      .select(`
        *,
        customer:users!recurring_billing_schedules_customer_id_fkey(
          id,
          display_name,
          email
        ),
        alliance:teams!recurring_billing_schedules_alliance_id_fkey(
          id,
          name
        ),
        venture:projects!recurring_billing_schedules_venture_id_fkey(
          id,
          name,
          title
        )
      `)
      .single();

    if (scheduleError) {
      throw new Error(`Failed to create billing schedule: ${scheduleError.message}`);
    }

    return {
      statusCode: 201,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ schedule })
    };

  } catch (error) {
    console.error('Create billing schedule error:', error);
    return {
      statusCode: 500,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ error: 'Failed to create billing schedule' })
    };
  }
};

// Process Billing Cycle
const processBillingCycle = async (event) => {
  try {
    const userId = getUserFromRequest(event);
    if (!userId) {
      return {
        statusCode: 401,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    // Check if user has finance admin permissions
    const { data: user } = await supabase
      .from('users')
      .select('role')
      .eq('id', userId)
      .single();

    if (!user || !['admin', 'finance_admin'].includes(user.role)) {
      return {
        statusCode: 403,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Finance admin permissions required' })
      };
    }

    const data = JSON.parse(event.body);
    const processDate = data.process_date || new Date().toISOString().split('T')[0];

    // Get billing schedules due for processing
    const { data: schedules, error: schedulesError } = await supabase
      .from('recurring_billing_schedules')
      .select(`
        *,
        customer:users!recurring_billing_schedules_customer_id_fkey(
          id,
          display_name,
          email
        ),
        alliance:teams!recurring_billing_schedules_alliance_id_fkey(
          id,
          name
        ),
        venture:projects!recurring_billing_schedules_venture_id_fkey(
          id,
          name,
          title
        )
      `)
      .eq('status', 'active')
      .lte('next_billing_date', processDate);

    if (schedulesError) {
      throw new Error(`Failed to fetch billing schedules: ${schedulesError.message}`);
    }

    const processedTransactions = [];
    const errors = [];

    // Process each billing schedule
    for (const schedule of schedules || []) {
      try {
        // Calculate billing period
        const billingPeriodStart = schedule.last_billing_date || schedule.start_date;
        const billingPeriodEnd = schedule.next_billing_date;
        
        // Create billing transaction
        const transactionData = {
          billing_schedule_id: schedule.id,
          billing_period_start: billingPeriodStart,
          billing_period_end: billingPeriodEnd,
          amount_due: schedule.amount,
          due_date: new Date(Date.now() + schedule.grace_period_days * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          invoice_number: generateInvoiceNumber(schedule.id, billingPeriodEnd),
          status: schedule.auto_charge ? 'processing' : 'pending'
        };

        const { data: transaction, error: transactionError } = await supabase
          .from('recurring_billing_transactions')
          .insert([transactionData])
          .select()
          .single();

        if (transactionError) {
          throw new Error(`Failed to create transaction: ${transactionError.message}`);
        }

        // Update schedule for next billing cycle
        const nextBillingDate = calculateNextBillingDate(
          schedule.frequency, 
          schedule.next_billing_date, 
          schedule.billing_day
        );

        const { error: updateError } = await supabase
          .from('recurring_billing_schedules')
          .update({
            last_billing_date: schedule.next_billing_date,
            next_billing_date: nextBillingDate,
            updated_at: new Date().toISOString()
          })
          .eq('id', schedule.id);

        if (updateError) {
          console.error(`Failed to update schedule ${schedule.id}:`, updateError);
        }

        processedTransactions.push({
          schedule_id: schedule.id,
          transaction_id: transaction.id,
          amount: schedule.amount,
          customer: schedule.customer?.display_name || schedule.alliance?.name || schedule.venture?.name,
          status: transaction.status
        });

        // Process automatic payment if enabled
        if (schedule.auto_charge && schedule.payment_method_id) {
          try {
            // TODO: Integrate with Plaid or payment processor
            // For now, mark as processing
            await supabase
              .from('recurring_billing_transactions')
              .update({
                status: 'processing',
                payment_method: 'auto_charge'
              })
              .eq('id', transaction.id);
          } catch (paymentError) {
            console.error(`Payment processing failed for transaction ${transaction.id}:`, paymentError);
          }
        }

        // Send invoice if enabled
        if (schedule.send_invoice) {
          try {
            // TODO: Integrate with email service and invoice generation
            // For now, mark invoice as sent
            await supabase
              .from('recurring_billing_transactions')
              .update({
                invoice_sent_at: new Date().toISOString()
              })
              .eq('id', transaction.id);
          } catch (invoiceError) {
            console.error(`Invoice sending failed for transaction ${transaction.id}:`, invoiceError);
          }
        }

      } catch (error) {
        console.error(`Error processing schedule ${schedule.id}:`, error);
        errors.push({
          schedule_id: schedule.id,
          error: error.message
        });
      }
    }

    return {
      statusCode: 200,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        processed_transactions: processedTransactions,
        errors: errors,
        summary: {
          total_schedules: schedules?.length || 0,
          successful_transactions: processedTransactions.length,
          failed_transactions: errors.length,
          total_amount: processedTransactions.reduce((sum, t) => sum + t.amount, 0)
        }
      })
    };

  } catch (error) {
    console.error('Process billing cycle error:', error);
    return {
      statusCode: 500,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ error: 'Failed to process billing cycle' })
    };
  }
};

// Get Billing Dashboard
const getBillingDashboard = async (event) => {
  try {
    const userId = getUserFromRequest(event);
    if (!userId) {
      return {
        statusCode: 401,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    const queryParams = new URLSearchParams(event.queryStringParameters || {});
    const customerId = queryParams.get('customer_id');
    const allianceId = queryParams.get('alliance_id');
    const status = queryParams.get('status');

    // Build query for billing schedules
    let query = supabase
      .from('recurring_billing_schedules')
      .select(`
        *,
        customer:users!recurring_billing_schedules_customer_id_fkey(
          id,
          display_name,
          email
        ),
        alliance:teams!recurring_billing_schedules_alliance_id_fkey(
          id,
          name
        ),
        venture:projects!recurring_billing_schedules_venture_id_fkey(
          id,
          name,
          title
        ),
        transactions:recurring_billing_transactions(
          id,
          amount_due,
          amount_paid,
          status,
          due_date,
          paid_date
        )
      `);

    // Apply filters based on user permissions
    const { data: user } = await supabase
      .from('users')
      .select('role')
      .eq('id', userId)
      .single();

    const isFinanceAdmin = user && ['admin', 'finance_admin'].includes(user.role);

    if (!isFinanceAdmin) {
      // Regular users can only see their own billing or alliance billing they manage
      query = query.or(`customer_id.eq.${userId},alliance_id.in.(${
        // Get alliances where user is admin
        await supabase
          .from('team_members')
          .select('team_id')
          .eq('user_id', userId)
          .eq('status', 'active')
          .in('role', ['founder', 'owner', 'admin'])
          .then(({ data }) => data?.map(m => m.team_id).join(',') || 'null')
      })`);
    }

    // Apply additional filters
    if (customerId) {
      query = query.eq('customer_id', customerId);
    }

    if (allianceId) {
      query = query.eq('alliance_id', allianceId);
    }

    if (status) {
      query = query.eq('status', status);
    }

    const { data: schedules, error: schedulesError } = await query
      .order('created_at', { ascending: false })
      .limit(100);

    if (schedulesError) {
      throw new Error(`Failed to fetch billing schedules: ${schedulesError.message}`);
    }

    // Calculate summary statistics
    const summary = {
      total_schedules: schedules?.length || 0,
      active_schedules: schedules?.filter(s => s.status === 'active').length || 0,
      paused_schedules: schedules?.filter(s => s.status === 'paused').length || 0,
      total_monthly_revenue: schedules?.filter(s => s.status === 'active' && s.frequency === 'monthly')
        .reduce((sum, s) => sum + (s.amount || 0), 0) || 0,
      total_annual_revenue: schedules?.filter(s => s.status === 'active')
        .reduce((sum, s) => {
          const multiplier = s.frequency === 'weekly' ? 52 : 
                           s.frequency === 'monthly' ? 12 :
                           s.frequency === 'quarterly' ? 4 :
                           s.frequency === 'semi_annually' ? 2 : 1;
          return sum + (s.amount * multiplier);
        }, 0) || 0
    };

    // Get recent transactions
    const { data: recentTransactions } = await supabase
      .from('recurring_billing_transactions')
      .select(`
        id,
        amount_due,
        amount_paid,
        status,
        due_date,
        paid_date,
        invoice_number,
        billing_schedule:recurring_billing_schedules(
          id,
          service_description,
          customer:users!recurring_billing_schedules_customer_id_fkey(
            display_name
          ),
          alliance:teams!recurring_billing_schedules_alliance_id_fkey(
            name
          )
        )
      `)
      .in('billing_schedule_id', schedules?.map(s => s.id) || [])
      .order('created_at', { ascending: false })
      .limit(20);

    return {
      statusCode: 200,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        schedules: schedules || [],
        recent_transactions: recentTransactions || [],
        summary,
        filters: {
          customer_id: customerId,
          alliance_id: allianceId,
          status
        }
      })
    };

  } catch (error) {
    console.error('Get billing dashboard error:', error);
    return {
      statusCode: 500,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ error: 'Failed to fetch billing dashboard' })
    };
  }
};

// Route requests to appropriate handlers
exports.handler = async (event, context) => {
  // Add CORS headers
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS'
  };

  // Handle preflight requests
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers,
      body: ''
    };
  }

  const path = event.path.replace('/.netlify/functions/recurring-billing', '');

  try {
    let response;

    if (event.httpMethod === 'GET') {
      if (path === '/dashboard' || path === '/dashboard/') {
        response = await getBillingDashboard(event);
      } else {
        response = {
          statusCode: 404,
          body: JSON.stringify({ error: 'Endpoint not found' })
        };
      }
    } else if (event.httpMethod === 'POST') {
      if (path === '/schedules' || path === '/schedules/') {
        response = await createBillingSchedule(event);
      } else if (path === '/process' || path === '/process/') {
        response = await processBillingCycle(event);
      } else {
        response = {
          statusCode: 404,
          body: JSON.stringify({ error: 'Endpoint not found' })
        };
      }
    } else {
      response = {
        statusCode: 405,
        body: JSON.stringify({ error: 'Method not allowed' })
      };
    }

    // Add CORS headers to response
    response.headers = { ...response.headers, ...headers };
    return response;

  } catch (error) {
    console.error('Recurring Billing API error:', error);
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ error: 'Internal server error' })
    };
  }
};
