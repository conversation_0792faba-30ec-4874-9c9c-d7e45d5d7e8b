// Alliance Management API
// Backend Specialist: Enhanced alliance system with comprehensive member management and business models
const { createClient } = require('@supabase/supabase-js');

// Supabase client
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_KEY
);

// Helper function to get user from request
const getUserFromRequest = (event) => {
  const authHeader = event.headers.authorization;
  if (!authHeader) return null;
  
  try {
    const token = authHeader.replace('Bearer ', '');
    const payload = JSON.parse(Buffer.from(token.split('.')[1], 'base64').toString());
    return payload.sub;
  } catch {
    return null;
  }
};

// Helper function to check alliance permissions
const checkAlliancePermission = async (userId, allianceId, requiredRoles = ['founder', 'owner', 'admin']) => {
  const { data: member } = await supabase
    .from('team_members')
    .select('role, status')
    .eq('team_id', allianceId)
    .eq('user_id', userId)
    .eq('status', 'active')
    .single();
  
  return member && requiredRoles.includes(member.role);
};

// Helper function to validate business model configuration
const validateBusinessModel = (businessModel) => {
  const validationErrors = [];
  
  if (!businessModel || typeof businessModel !== 'object') {
    return ['Business model must be a valid object'];
  }
  
  // Validate revenue sharing
  if (businessModel.revenue_sharing) {
    const { method, percentages } = businessModel.revenue_sharing;
    if (!['equal', 'contribution_based', 'custom'].includes(method)) {
      validationErrors.push('Invalid revenue sharing method');
    }
    if (method === 'custom' && (!percentages || typeof percentages !== 'object')) {
      validationErrors.push('Custom revenue sharing requires percentages object');
    }
  }
  
  // Validate commission rate
  if (businessModel.commission_rate !== undefined) {
    const rate = parseFloat(businessModel.commission_rate);
    if (isNaN(rate) || rate < 0 || rate > 100) {
      validationErrors.push('Commission rate must be between 0 and 100');
    }
  }
  
  // Validate recurring fee
  if (businessModel.recurring_fee !== undefined) {
    const fee = parseFloat(businessModel.recurring_fee);
    if (isNaN(fee) || fee < 0) {
      validationErrors.push('Recurring fee must be a positive number');
    }
  }
  
  return validationErrors;
};

// Get Alliance Members with Enhanced Details
const getAllianceMembers = async (event) => {
  try {
    const userId = getUserFromRequest(event);
    if (!userId) {
      return {
        statusCode: 401,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    const allianceId = event.path.split('/')[2]; // /alliance-management/{id}/members

    // Check if user is a member of the alliance
    const { data: userMembership } = await supabase
      .from('team_members')
      .select('role, status')
      .eq('team_id', allianceId)
      .eq('user_id', userId)
      .eq('status', 'active')
      .single();

    if (!userMembership) {
      return {
        statusCode: 403,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Access denied' })
      };
    }

    // Get all alliance members with detailed information
    const { data: members, error: membersError } = await supabase
      .from('team_members')
      .select(`
        id,
        user_id,
        role,
        status,
        permissions,
        joined_at,
        users(
          id,
          display_name,
          email,
          avatar_url,
          is_premium
        )
      `)
      .eq('team_id', allianceId)
      .order('joined_at', { ascending: true });

    if (membersError) {
      throw new Error(`Failed to fetch members: ${membersError.message}`);
    }

    // Get member statistics and contributions
    const enhancedMembers = await Promise.all(
      members.map(async (member) => {
        // Get member's contribution statistics
        const { data: contributions } = await supabase
          .from('contributions')
          .select('id, hours_spent, difficulty, validation_status')
          .eq('user_id', member.user_id)
          .in('project_id', 
            supabase
              .from('projects')
              .select('id')
              .eq('alliance_id', allianceId)
          );

        // Get member's recent activity
        const { data: recentActivity } = await supabase
          .from('activity_feeds')
          .select('activity_type, created_at')
          .eq('actor_id', member.user_id)
          .order('created_at', { ascending: false })
          .limit(5);

        // Calculate member metrics
        const totalContributions = contributions?.length || 0;
        const totalHours = contributions?.reduce((sum, c) => sum + (c.hours_spent || 0), 0) || 0;
        const averageDifficulty = contributions?.length > 0 ? 
          contributions.reduce((sum, c) => sum + (c.difficulty || 0), 0) / contributions.length : 0;
        const validatedContributions = contributions?.filter(c => c.validation_status === 'approved').length || 0;

        return {
          ...member,
          statistics: {
            total_contributions: totalContributions,
            total_hours: totalHours,
            average_difficulty: averageDifficulty,
            validation_rate: totalContributions > 0 ? (validatedContributions / totalContributions) * 100 : 0
          },
          recent_activity: recentActivity || [],
          permissions: member.permissions || {}
        };
      })
    );

    // Calculate alliance statistics
    const allianceStats = {
      total_members: members.length,
      active_members: members.filter(m => m.status === 'active').length,
      roles_distribution: members.reduce((acc, member) => {
        acc[member.role] = (acc[member.role] || 0) + 1;
        return acc;
      }, {}),
      total_contributions: enhancedMembers.reduce((sum, m) => sum + m.statistics.total_contributions, 0),
      total_hours: enhancedMembers.reduce((sum, m) => sum + m.statistics.total_hours, 0)
    };

    return {
      statusCode: 200,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        members: enhancedMembers,
        statistics: allianceStats,
        user_role: userMembership.role
      })
    };

  } catch (error) {
    console.error('Get alliance members error:', error);
    return {
      statusCode: 500,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ error: 'Failed to fetch alliance members' })
    };
  }
};

// Invite Member to Alliance
const inviteMember = async (event) => {
  try {
    const userId = getUserFromRequest(event);
    if (!userId) {
      return {
        statusCode: 401,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    const allianceId = event.path.split('/')[2]; // /alliance-management/{id}/invite
    const data = JSON.parse(event.body);

    // Validate required fields
    if (!data.email || !data.role) {
      return {
        statusCode: 400,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Email and role are required' })
      };
    }

    // Check if user has permission to invite members
    const hasPermission = await checkAlliancePermission(userId, allianceId, ['founder', 'owner', 'admin']);
    if (!hasPermission) {
      return {
        statusCode: 403,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Insufficient permissions to invite members' })
      };
    }

    // Validate role
    const validRoles = ['member', 'contributor', 'admin'];
    if (!validRoles.includes(data.role)) {
      return {
        statusCode: 400,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Invalid role specified' })
      };
    }

    // Check if user is already a member or has pending invitation
    const { data: existingMember } = await supabase
      .from('team_members')
      .select('id')
      .eq('team_id', allianceId)
      .eq('user_id', 
        supabase
          .from('users')
          .select('id')
          .eq('email', data.email)
          .single()
      )
      .single();

    if (existingMember) {
      return {
        statusCode: 400,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'User is already a member of this alliance' })
      };
    }

    const { data: existingInvitation } = await supabase
      .from('alliance_invitations')
      .select('id')
      .eq('alliance_id', allianceId)
      .eq('email', data.email)
      .eq('status', 'pending')
      .single();

    if (existingInvitation) {
      return {
        statusCode: 400,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Invitation already sent to this email' })
      };
    }

    // Create invitation
    const invitationData = {
      alliance_id: allianceId,
      email: data.email,
      role: data.role,
      invited_by: userId,
      message: data.message || null,
      status: 'pending',
      expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString() // 7 days
    };

    const { data: invitation, error: invitationError } = await supabase
      .from('alliance_invitations')
      .insert([invitationData])
      .select(`
        id,
        email,
        role,
        message,
        status,
        created_at,
        expires_at,
        invited_by_user:users!alliance_invitations_invited_by_fkey(
          id,
          display_name,
          avatar_url
        ),
        alliance:teams!alliance_invitations_alliance_id_fkey(
          id,
          name,
          description
        )
      `)
      .single();

    if (invitationError) {
      throw new Error(`Failed to create invitation: ${invitationError.message}`);
    }

    // TODO: Send invitation email notification
    // await sendInvitationEmail(invitation);

    return {
      statusCode: 201,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ invitation })
    };

  } catch (error) {
    console.error('Invite member error:', error);
    return {
      statusCode: 500,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ error: 'Failed to send invitation' })
    };
  }
};

// Update Member Role and Permissions
const updateMemberRole = async (event) => {
  try {
    const userId = getUserFromRequest(event);
    if (!userId) {
      return {
        statusCode: 401,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    const allianceId = event.path.split('/')[2]; // /alliance-management/{id}/members/{memberId}
    const memberId = event.path.split('/')[4];
    const data = JSON.parse(event.body);

    // Check if user has permission to update member roles
    const hasPermission = await checkAlliancePermission(userId, allianceId, ['founder', 'owner', 'admin']);
    if (!hasPermission) {
      return {
        statusCode: 403,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Insufficient permissions to update member roles' })
      };
    }

    // Get current member details
    const { data: currentMember, error: memberError } = await supabase
      .from('team_members')
      .select('user_id, role, status')
      .eq('id', memberId)
      .eq('team_id', allianceId)
      .single();

    if (memberError || !currentMember) {
      return {
        statusCode: 404,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Member not found' })
      };
    }

    // Prevent self-demotion from founder role
    if (currentMember.user_id === userId && currentMember.role === 'founder' && data.role !== 'founder') {
      return {
        statusCode: 400,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Founders cannot demote themselves' })
      };
    }

    // Validate new role
    const validRoles = ['founder', 'owner', 'admin', 'member', 'contributor'];
    if (data.role && !validRoles.includes(data.role)) {
      return {
        statusCode: 400,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Invalid role specified' })
      };
    }

    // Update member
    const updateData = {};
    if (data.role) updateData.role = data.role;
    if (data.permissions) updateData.permissions = data.permissions;
    if (data.status) updateData.status = data.status;

    const { data: updatedMember, error: updateError } = await supabase
      .from('team_members')
      .update(updateData)
      .eq('id', memberId)
      .eq('team_id', allianceId)
      .select(`
        id,
        user_id,
        role,
        status,
        permissions,
        joined_at,
        users(
          id,
          display_name,
          email,
          avatar_url
        )
      `)
      .single();

    if (updateError) {
      throw new Error(`Failed to update member: ${updateError.message}`);
    }

    return {
      statusCode: 200,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ member: updatedMember })
    };

  } catch (error) {
    console.error('Update member role error:', error);
    return {
      statusCode: 500,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ error: 'Failed to update member role' })
    };
  }
};

// Route requests to appropriate handlers
exports.handler = async (event, context) => {
  // Add CORS headers
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS'
  };

  // Handle preflight requests
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers,
      body: ''
    };
  }

  const path = event.path.replace('/.netlify/functions/alliance-management', '');

  try {
    let response;

    if (event.httpMethod === 'GET') {
      if (path.includes('/members') && !path.includes('/members/')) {
        response = await getAllianceMembers(event);
      } else {
        response = {
          statusCode: 404,
          body: JSON.stringify({ error: 'Endpoint not found' })
        };
      }
    } else if (event.httpMethod === 'POST') {
      if (path.includes('/invite')) {
        response = await inviteMember(event);
      } else {
        response = {
          statusCode: 404,
          body: JSON.stringify({ error: 'Endpoint not found' })
        };
      }
    } else if (event.httpMethod === 'PUT') {
      if (path.includes('/members/')) {
        response = await updateMemberRole(event);
      } else {
        response = {
          statusCode: 404,
          body: JSON.stringify({ error: 'Endpoint not found' })
        };
      }
    } else {
      response = {
        statusCode: 405,
        body: JSON.stringify({ error: 'Method not allowed' })
      };
    }

    // Add CORS headers to response
    response.headers = { ...response.headers, ...headers };
    return response;

  } catch (error) {
    console.error('Alliance Management API error:', error);
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ error: 'Internal server error' })
    };
  }
};
