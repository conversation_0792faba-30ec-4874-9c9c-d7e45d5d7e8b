/**
 * Royaltea Agreement Generator
 *
 * A complete system for generating customized legal agreements based on project data.
 * This generator handles variable replacement, smart inference, and proper formatting
 * of all agreement sections including exhibits.
 */

import { templateManager, TEMPLATE_TYPES } from './templateManager.js';

export class NewAgreementGenerator {
  constructor() {
    // Today's date as default
    const today = new Date();
    this.currentDate = today.toLocaleDateString('en-US', {
      month: 'long',
      day: 'numeric',
      year: 'numeric'
    });
  }

  /**
   * Load a template based on template type
   * @param {string} templateType - The template type to load
   * @returns {Promise<string>} - The template text
   */
  async loadTemplate(templateType = TEMPLATE_TYPES.STANDARD) {
    return templateManager.loadTemplate(templateType);
  }

  /**
   * Generate a complete agreement based on template and project data
   * @param {string} templateText - The agreement template text
   * @param {Object} project - The project data
   * @param {Object} options - Additional options
   * @returns {Promise<string>} - The customized agreement
   */
  async generateAgreement(templateText, project, options = {}) {
    if (!templateText || !project) {
      throw new Error('Template text and project data are required');
    }

    // If template type is specified in options, use that template
    if (options.templateType && !templateText.includes('TEMPLATE_PLACEHOLDER')) {
      console.warn('Template text was provided directly, ignoring templateType option');
    }

    // Extract options
    const {
      contributors = [],
      currentUser = null,
      royaltyModel = null,
      milestones = [],
      fullName = '',
      agreementDate = null, // Allow passing a specific date
      allianceInfo = null // Allow passing alliance information directly for testing
    } = options;

    // Set the agreement date if provided
    if (agreementDate) {
      const date = new Date(agreementDate);
      this.currentDate = date.toLocaleDateString('en-US', {
        month: 'long',
        day: 'numeric',
        year: 'numeric'
      });
    } else {
      // Use current date
      const today = new Date();
      this.currentDate = today.toLocaleDateString('en-US', {
        month: 'long',
        day: 'numeric',
        year: 'numeric'
      });
    }

    // Validate and prepare project data
    const validatedProject = this._validateProjectData(project);

    // Validate and prepare company information (now async)
    const companyInfo = await this._validateCompanyInfo(project, contributors, currentUser, allianceInfo);

    // Validate and prepare contributor information
    const contributorInfo = this._validateContributorInfo(fullName, currentUser);

    // Process the template with all replacements
    let processedAgreement = this._processTemplate(
      templateText,
      validatedProject,
      companyInfo,
      contributorInfo,
      royaltyModel,
      milestones
    );

    // Clean up the agreement text to fix formatting issues
    processedAgreement = this._cleanupAgreementText(processedAgreement);

    return processedAgreement;
  }

  /**
   * Clean up the agreement text to fix formatting issues
   * @param {string} text - The agreement text to clean up
   * @returns {string} - The cleaned up agreement text
   */
  _cleanupAgreementText(text) {
    if (!text) return '';

    let cleanedText = text;

    // Fix multiple consecutive newlines (more than 2)
    cleanedText = cleanedText.replace(/\n{3,}/g, '\n\n');

    // Fix empty bold text
    cleanedText = cleanedText.replace(/\*\*\s*\*\*/g, '');

    // Fix lines with only whitespace
    cleanedText = cleanedText.replace(/\n\s+\n/g, '\n\n');

    // Fix missing newlines before section headers
    cleanedText = cleanedText.replace(/([^\n])(## [A-Z])/g, '$1\n\n$2');

    // Fix missing newlines after section headers
    cleanedText = cleanedText.replace(/(## [^\n]+)\n([^\n])/g, '$1\n\n$2');

    // Fix missing newlines before subsection headers
    cleanedText = cleanedText.replace(/([^\n])(### [A-Z])/g, '$1\n\n$2');

    // Fix missing newlines after subsection headers
    cleanedText = cleanedText.replace(/(### [^\n]+)\n([^\n])/g, '$1\n\n$2');

    // Ensure there's a newline at the end of the document
    if (!cleanedText.endsWith('\n')) {
      cleanedText += '\n';
    }

    return cleanedText;
  }

  /**
   * Process the template with all replacements
   * @param {string} template - The agreement template
   * @param {Object} project - The validated project data
   * @param {Object} company - The company information
   * @param {Object} contributor - The contributor information
   * @param {Object} royaltyModel - The royalty model
   * @param {Array} milestones - The project milestones
   * @returns {string} - The processed agreement
   */
  _processTemplate(template, project, company, contributor, royaltyModel, milestones) {
    let processedTemplate = template;

    // Replace date in the agreement header
    processedTemplate = processedTemplate.replace(/\[ \], 20\[__\]/g, this.currentDate);
    processedTemplate = processedTemplate.replace(/\[Date\]/g, this.currentDate);

    // Replace date in the agreement title
    processedTemplate = processedTemplate.replace(
      /THIS CONTRIBUTOR AGREEMENT \(this "Agreement"\) is made as of .+? by and between/g,
      `THIS CONTRIBUTOR AGREEMENT (this "Agreement") is made as of ${this.currentDate} by and between`
    );

    // Replace date in other places
    processedTemplate = processedTemplate.replace(/Effective Date: .+?\n/g, `Effective Date: ${this.currentDate}\n`);

    // Replace company information
    processedTemplate = this._replaceCompanyInfo(processedTemplate, company);

    // Replace contributor information
    processedTemplate = this._replaceContributorInfo(processedTemplate, contributor);

    // Replace project information
    processedTemplate = this._replaceProjectInfo(processedTemplate, project);

    // Replace project type specific terminology
    processedTemplate = this._replaceProjectTypeTerminology(processedTemplate, project);

    // Generate and replace exhibits
    const exhibitI = this._generateExhibitI(project);
    const exhibitII = this._generateExhibitII(project, milestones);
    processedTemplate = this._replaceExhibits(processedTemplate, exhibitI, exhibitII);

    // Generate and replace Schedule A
    const scheduleA = this._generateScheduleA(project);
    processedTemplate = this._replaceScheduleA(processedTemplate, scheduleA);

    // Replace royalty model information
    processedTemplate = this._replaceRoyaltyModelInfo(processedTemplate, royaltyModel, project);

    // Final pass to catch any remaining placeholders
    processedTemplate = this._finalPlaceholderCleanup(processedTemplate, project, company, contributor);

    return processedTemplate;
  }

  /**
   * Final cleanup to catch any remaining placeholders
   * @param {string} template - The processed template
   * @param {Object} project - The project data
   * @param {Object} company - The company information
   * @param {Object} contributor - The contributor information
   * @returns {string} - The fully cleaned template
   */
  _finalPlaceholderCleanup(template, project, company, contributor) {
    let cleaned = template;

    // Replace any remaining date placeholders
    cleaned = cleaned.replace(/\[ \], 20\[__\]/g, this.currentDate);
    cleaned = cleaned.replace(/\[_+\]/g, contributor.name);
    cleaned = cleaned.replace(/\[__\]/g, '');
    cleaned = cleaned.replace(/\[\s*\]/g, '');

    // Replace any remaining project information
    cleaned = cleaned.replace(/\[Project Name\]/g, project.name);
    cleaned = cleaned.replace(/Village of The Ages/g, project.name);
    cleaned = cleaned.replace(/village simulation game where players guide communities through historical progressions and manage resource-based challenges/g, project.description);

    // Replace any remaining company information - ONLY if not already replaced
    if (!cleaned.includes(company.name)) {
      cleaned = cleaned.replace(/City of Gamers Inc\./gi, company.name);
      cleaned = cleaned.replace(/City of Gamers/gi, company.name);
      cleaned = cleaned.replace(/\bCOG\b/gi, company.name);
    }
    if (!cleaned.includes(company.signerName)) {
      cleaned = cleaned.replace(/Gynell Journigan/gi, company.signerName);
    }

    // Replace any remaining location information
    cleaned = cleaned.replace(/Florida/gi, company.state);
    cleaned = cleaned.replace(/Orlando/gi, company.county?.replace(' County', '') || 'the applicable jurisdiction');
    cleaned = cleaned.replace(/1205 43rd Street, Suite B, Orlando, Florida 32839/gi, company.address);

    // Replace any remaining contact information
    cleaned = cleaned.replace(/billing@cogfuture\.com/gi, company.billingEmail);
    cleaned = cleaned.replace(/\[Project Owner Email\]/gi, company.billingEmail);
    cleaned = cleaned.replace(/\[Project Address\]/gi, company.address);

    // Replace any remaining placeholders with empty strings
    cleaned = cleaned.replace(/\[.*?\]/g, '');

    // Replace "the applicable jurisdiction" with actual state if we have it
    if (company.state) {
      cleaned = cleaned.replace(/the applicable jurisdiction/gi, company.state);
    }

    return cleaned;
  }

  /**
   * Replace Schedule A in the agreement
   * @param {string} template - The agreement template
   * @param {string} scheduleA - The Schedule A content
   * @returns {string} - The processed template
   */
  _replaceScheduleA(template, scheduleA) {
    let processed = template;

    // Find and replace Schedule A - use a more specific regex to capture the entire content
    const scheduleARegex = /## SCHEDULE A[\s\S]*?(?=## SCHEDULE B|$)/;
    if (scheduleARegex.test(processed)) {
      processed = processed.replace(scheduleARegex, `## SCHEDULE A\n### Description of Services\n\n${scheduleA}\n\n`);
    } else {
      // If Schedule A is not found, add it before Schedule B
      const scheduleBIndex = processed.indexOf('## SCHEDULE B');
      if (scheduleBIndex !== -1) {
        processed = processed.substring(0, scheduleBIndex) +
          `## SCHEDULE A\n### Description of Services\n\n${scheduleA}\n\n` +
          processed.substring(scheduleBIndex);
      } else {
        // If Schedule B is not found, add Schedule A at the end
        processed += `\n\n## SCHEDULE A\n### Description of Services\n\n${scheduleA}\n\n`;
      }
    }

    return processed;
  }

  /**
   * Generate Schedule A content
   * @param {Object} project - The project data
   * @returns {string} - The Schedule A content
   */
  _generateScheduleA(project) {
    const projectName = project.name;
    const projectDescription = project.description;
    const projectType = project.projectType;

    // Create a project type specific description
    let projectTypeDescription = '';
    switch (projectType) {
      case 'game':
        projectTypeDescription = 'game development';
        break;
      case 'music':
        projectTypeDescription = 'music production';
        break;
      case 'software':
      case 'app':
        projectTypeDescription = 'software development';
        break;
      case 'film':
      case 'video':
        projectTypeDescription = 'video production';
        break;
      default:
        projectTypeDescription = 'project development';
    }

    return `This project involves ${projectTypeDescription} work on "${projectName}," a digital project where ${projectDescription}.

1. **Services**

   a. **General.** Pursuant to the terms and conditions of this Agreement and subject to Company's acceptance, Contributor shall:
      i. develop the Work Product following the requirements and technical specifications set forth in Exhibit I and in accordance with the roadmap set forth in Exhibit II and Good Industry Practice ("Developing Services"); and
      ii. provide the Support Services in accordance with Good Industry Practice.

   b. **Performance.** Contributor understands and agrees that Contributor is solely responsible for the control and supervision of the means by which the Services are provided consistent with the goal of successfully completing the Services on time. Contributor shall allocate sufficient resources to ensure that it performs its Services under this Agreement in accordance with the roadmap in Exhibit II and in accordance with Good Industry Practice.

   c. **Co-operation.**
      i. During the period from the Effective Date until Launch, unless directed otherwise by Company, Contributor shall attend at least weekly calls with Company, with frequency increasing during crunch periods as needed, and provide builds (application versions) every two (2) weeks with Company.
      ii. The Parties shall share responsibility for Work Product Management as agreed between the Parties from time to time and as outlined in Exhibit II. If the Parties are unable to reach agreement in respect of a decision in relation to Work Product Management, Company's decision shall prevail.
      iii. If there is additional need for development that is not in the agreed roadmap the Parties will negotiate in good faith the timeline for Contributor to deliver such further development. The Parties will also negotiate in good faith the costs to deliver such further development.

   d. **Cessation of Services following Launch & Right of First Refusal.**
      i. Following Launch, Company may determine that it no longer wishes to receive the Services and Contributor may determine that it no longer wishes to provide the Services, each at its sole discretion. In such case the relevant Party will notify the other Party in writing.
      ii. If Contributor notifies Company that it no longer wishes to provide Services to Company, Contributor's obligations to provide the Services will cease within 7 (seven) days of that notice (or such other period as agreed by the Parties) and this Agreement shall terminate automatically.
      iii. If Company intends to continue development of the Work Product after Contributor ceases providing Services, Company may notify Contributor in writing, and both Parties may negotiate in good faith the terms for such further development.

2. **Approval.**

   a. When Contributor considers that it has progressed the Work Product such that it reaches a Milestone, the Work Product shall be submitted to Company for written approval ("Approval"). Company will assess and/or test each delivered Milestone and will notify Contributor if it is accepted or rejected within 7 (seven) business days after receipt, though the parties acknowledge that in practice, feedback will typically be provided as soon as possible as determined by the development team. In case the Work Product does not, in Company's reasonable opinion, satisfy the Milestone or otherwise meet the technical specifications set forth in Exhibit I, Contributor will, at Company's request, promptly repair or redo the Services wholly or in part, with an initial response time of 7 (seven) days from the date on which feedback is received. For complex issues or features that reasonably require additional time, Contributor shall demonstrate meaningful progress during weekly meetings until the issue is resolved to Company's satisfaction.

   b. Notwithstanding Section 2(a), if Company fails to notify Contributor that the Milestone is rejected within 7 (seven) days of its submission it shall be deemed approved. In case of deemed acceptance, Company shall still be entitled to provide feedback on the delivered Milestone and Contributor shall promptly repair or redo the Services wholly or in part, whenever the Services are deemed to be incomplete or not in conformity with the provisions of this Agreement or incompatible for the intended purposes.

   c. Unless Section 2(b) applies, if Contributor fails to demonstrate meaningful progress on repairing or redoing the Services within the applicable timeframes, Company may suspend any payment obligation until such time as meaningful progress is demonstrated to Company's satisfaction. If the Contributor fails to demonstrate meaningful progress for a period exceeding 14 (fourteen) days despite weekly check-ins, the Company may terminate this Agreement. For purposes of this Agreement, "meaningful progress" shall include, but not be limited to, regular communication, clear articulation of challenges, implementation of agreed-upon solutions, and demonstration of advancement toward resolving the identified issues.

   d. **Source Code.** Contributor shall provide to Company not less than once every month (or such frequency agreed by the Parties), and each time the Work Product is submitted for Approval, an up-to-date copy of any source code for the Work Product which comprises Background IP of Contributor or Developed IP.

   e. **Change Control.** Any changes to the Specifications will be set forth in writing and signed by both Parties before they shall take effect. Contributor may not unreasonably decline to accept any change requests that reduce the cost of performance of the Services for Contributor. Contributor may not decline any change requests that increase the cost or magnitude of performance of the Services for Contributor, provided that the changes are reasonable in scope.`;
  }

  /**
   * Validate and prepare project data with smart defaults
   * @param {Object} project - The project data
   * @returns {Object} - Validated project data
   */
  _validateProjectData(project) {
    // Extract basic project information
    const name = project.name || project.title || "[Project Title]";
    const description = project.description || "a collaborative project";

    // Determine project type and normalize it
    let projectType = (project.project_type || "creative work").toLowerCase();

    // Smart inference for project type if not explicitly provided
    if (projectType === "creative work" && description) {
      if (description.match(/game|player|level/i)) projectType = "game";
      else if (description.match(/app|website|platform/i)) projectType = "software";
      else if (description.match(/song|music|audio/i)) projectType = "music";
      else if (description.match(/art|design|visual/i)) projectType = "art";
    }

    // Determine project length based on estimated duration
    const estimatedDuration = project.estimated_duration || 6;
    let projectLength = "medium";
    if (estimatedDuration <= 2) projectLength = "short";
    else if (estimatedDuration > 9) projectLength = "long";

    // Determine tech stack based on project type and description
    const techStack = this._determineTechStack(projectType, description, project);

    // Get default values based on project type
    const defaultRevenueShare = this._getDefaultRevenueShare(projectType);
    const defaultPayoutThreshold = this._getDefaultPayoutThreshold(projectType);
    const defaultMaxPayment = this._getDefaultMaxPayment(projectType);

    // Return validated project data with smart defaults
    return {
      name,
      description,
      projectType,
      projectLength,
      estimatedDuration,
      techStack,
      engine: project.engine || techStack.engine,
      platforms: project.platforms || techStack.platforms,
      revenueShare: project.revenueShare || defaultRevenueShare,
      payoutThreshold: project.payoutThreshold || defaultPayoutThreshold,
      maxPayment: project.maxPayment || defaultMaxPayment,
      ...project
    };
  }

  /**
   * Determine appropriate tech stack based on project type and description
   * @param {string} projectType - The project type
   * @param {string} description - The project description
   * @param {Object} project - The project data
   * @returns {Object} - Tech stack information
   */
  _determineTechStack(projectType, description, project) {
    const techStack = {
      engine: this._getDefaultEngine(projectType),
      platforms: this._getDefaultPlatforms(projectType),
      framework: '',
      database: '',
      languages: []
    };

    // Analyze description for more specific tech stack information
    if (projectType === 'game') {
      // Determine game engine based on description - explicit mentions take precedence
      if (description.match(/unreal|ue5|ue4/i)) {
        techStack.engine = 'Unreal Engine 5';
        techStack.languages = ['C++', 'Blueprint'];
      } else if (description.match(/unity/i)) {
        techStack.engine = 'Unity';
        techStack.languages = ['C#'];
      } else if (description.match(/godot/i)) {
        techStack.engine = 'Godot Engine';
        techStack.languages = ['GDScript'];
      } else {
        // If no explicit engine mention, infer from game characteristics

        // Factors favoring Unreal Engine
        const unrealFactors = [
          description.match(/3d/i) ? 1 : 0,
          description.match(/realistic|photorealistic|high fidelity|high-fidelity/i) ? 2 : 0,
          description.match(/open world|large scale|massive/i) ? 1 : 0,
          description.match(/fps|first person shooter|third person|action/i) ? 1 : 0,
          description.match(/console|playstation|xbox|ps5|series x/i) ? 1 : 0,
          description.match(/aaa|triple-a|high budget/i) ? 2 : 0,
          description.match(/cinematic|visual effects|vfx/i) ? 1 : 0
        ];

        // Factors favoring Unity
        const unityFactors = [
          description.match(/2d|pixel|sprite|isometric/i) ? 2 : 0,
          description.match(/mobile|android|ios|iphone|ipad/i) ? 2 : 0,
          description.match(/lightweight|casual|puzzle|simple/i) ? 1 : 0,
          description.match(/cross-platform|multiplatform/i) ? 1 : 0,
          description.match(/indie|low budget|small team/i) ? 1 : 0,
          description.match(/web|webgl|browser/i) ? 2 : 0,
          description.match(/prototype|rapid development/i) ? 1 : 0
        ];

        // Calculate scores
        const unrealScore = unrealFactors.reduce((sum, factor) => sum + factor, 0);
        const unityScore = unityFactors.reduce((sum, factor) => sum + factor, 0);

        // Decide based on scores
        if (unrealScore > unityScore) {
          techStack.engine = 'Unreal Engine 5';
          techStack.languages = ['C++', 'Blueprint'];
        } else {
          techStack.engine = 'Unity';
          techStack.languages = ['C#'];
        }
      }

      // Determine platforms based on description
      const platformFactors = {
        mobile: description.match(/mobile|android|ios|iphone|ipad/i) ? true : false,
        pc: description.match(/pc|windows|mac|desktop|steam/i) ? true : false,
        console: description.match(/console|playstation|xbox|switch|ps5|ps4/i) ? true : false,
        web: description.match(/web|browser|html5|webgl/i) ? true : false
      };

      // Generate platform string based on detected platforms
      let platformsArray = [];
      if (platformFactors.pc) platformsArray.push('PC');
      if (platformFactors.mobile) platformsArray.push('Mobile (iOS, Android)');
      if (platformFactors.console) platformsArray.push('Consoles');
      if (platformFactors.web) platformsArray.push('Web');

      // Set platforms string or use default if none detected
      if (platformsArray.length > 0) {
        techStack.platforms = platformsArray.join(', ');
      } else if (techStack.engine === 'Unity') {
        techStack.platforms = 'PC, Mobile';
      } else if (techStack.engine === 'Unreal Engine 5') {
        techStack.platforms = 'PC, Consoles';
      }

      // Add additional tech details based on engine
      if (techStack.engine === 'Unreal Engine 5') {
        techStack.additionalTech = 'Niagara VFX, Lumen lighting, Nanite geometry';
      } else if (techStack.engine === 'Unity') {
        techStack.additionalTech = 'Universal Render Pipeline, Shader Graph, Cinemachine';
      }
    } else if (projectType === 'software' || projectType === 'app') {
      // Determine software framework based on description
      if (description.match(/react|jsx/i)) {
        techStack.engine = 'React';
        techStack.languages = ['JavaScript', 'TypeScript'];
      } else if (description.match(/angular/i)) {
        techStack.engine = 'Angular';
        techStack.languages = ['TypeScript'];
      } else if (description.match(/vue/i)) {
        techStack.engine = 'Vue.js';
        techStack.languages = ['JavaScript'];
      } else if (description.match(/native|mobile app|ios app|android app/i)) {
        techStack.engine = 'React Native';
        techStack.languages = ['JavaScript', 'TypeScript'];
      } else if (description.match(/node|express|backend/i)) {
        techStack.engine = 'Node.js';
        techStack.languages = ['JavaScript'];
      }

      // Determine database based on description
      if (description.match(/sql|relational/i)) {
        techStack.database = 'PostgreSQL';
      } else if (description.match(/nosql|document|mongo/i)) {
        techStack.database = 'MongoDB';
      } else if (description.match(/real-?time/i)) {
        techStack.database = 'Firebase Realtime Database';
      }
    }

    // Override with explicit project data if provided
    if (project.engine) techStack.engine = project.engine;
    if (project.platforms) techStack.platforms = project.platforms;
    if (project.framework) techStack.framework = project.framework;
    if (project.database) techStack.database = project.database;
    if (project.languages) techStack.languages = project.languages;

    return techStack;
  }

  /**
   * Validate and prepare company information
   * @param {Object} project - The project data
   * @param {Array} contributors - The project contributors
   * @param {Object} currentUser - The current user
   * @param {Object} allianceInfo - Optional alliance information (for testing)
   * @returns {Promise<Object>} - Validated company information
   */
  async _validateCompanyInfo(project, contributors, currentUser, allianceInfo = null) {
    // Find project owner from contributors
    const owner = contributors.find(c => c.permission_level === 'Owner');

    // Use passed alliance information if available, otherwise try to fetch from database
    let allianceInfoToUse = allianceInfo; // Use passed alliance info (for testing)

    if (!allianceInfoToUse) {
      // Try to fetch alliance information if project has alliance_id or team_id
      const allianceId = project.alliance_id || project.team_id;

      if (allianceId) {
        try {
          // Import supabase dynamically to avoid circular dependencies
          const { supabase } = await import('../supabase/supabase.utils');

          const { data: alliance, error } = await supabase
            .from('teams')
            .select(`
              id,
              name,
              description,
              alliance_type,
              business_model,
              industry,
              created_by,
              team_members!inner (
                user_id,
                role,
                users (
                  id,
                  email,
                  user_metadata
                )
              )
            `)
            .eq('id', allianceId)
            .single();

          if (!error && alliance) {
            allianceInfoToUse = alliance;
            console.log('Found alliance information for agreement:', alliance.name);
          }
        } catch (error) {
          console.warn('Could not fetch alliance information:', error);
        }
      }
    } else {
      console.log('Using provided alliance information for agreement:', allianceInfoToUse.name);
    }

    // Use alliance information as company if available, otherwise fall back to project/owner info
    let companyName, signerName, signerTitle, billingEmail;

    if (allianceInfoToUse) {
      // Use alliance as the company
      companyName = allianceInfoToUse.name;

      // Find the alliance founder/owner for signing
      const allianceOwner = allianceInfoToUse.team_members?.find(m =>
        m.role === 'founder' || m.role === 'owner'
      );

      // Use alliance primary contact information for signing - CRITICAL for legal accuracy
      signerName = allianceInfoToUse.contact_information?.primaryContact?.name ||
                   allianceOwner?.users?.user_metadata?.full_name ||
                   allianceOwner?.users?.email?.split('@')[0] ||
                   owner?.display_name ||
                   owner?.users?.display_name ||
                   currentUser?.user_metadata?.full_name;

      // If no signer name found, throw error - this is critical for legal agreements
      if (!signerName) {
        throw new Error('CRITICAL: No alliance signer name found for agreement. Cannot generate legally valid agreement without proper signatory identification.');
      }

      signerTitle = allianceInfoToUse.contact_information?.primaryContact?.title ||
                    (allianceOwner?.role === 'founder' ? 'Founder' :
                     allianceOwner?.role === 'owner' ? 'Owner' : 'President');

      billingEmail = allianceInfoToUse.contact_information?.primaryContact?.email ||
                     allianceInfoToUse.contact_information?.email ||
                     allianceOwner?.users?.email ||
                     owner?.email ||
                     currentUser?.email;

      // If no billing email found, throw error
      if (!billingEmail) {
        throw new Error('CRITICAL: No alliance contact email found for agreement. Cannot generate legally valid agreement without proper contact information.');
      }
    } else {
      // Fall back to project/owner information - NEVER use hardcoded defaults
      companyName = project.company_name ||
                    owner?.company_name ||
                    owner?.display_name ||
                    owner?.users?.display_name;

      // If no company name found, throw error - this is a critical legal issue
      if (!companyName) {
        throw new Error('CRITICAL: No company name found for agreement. Cannot generate legally valid agreement without proper company identification.');
      }

      signerName = owner?.display_name ||
                   owner?.users?.display_name ||
                   currentUser?.user_metadata?.full_name;

      // If no signer name found, throw error
      if (!signerName) {
        throw new Error('CRITICAL: No signer name found for agreement. Cannot generate legally valid agreement without proper signatory identification.');
      }

      signerTitle = owner?.title || "Authorized Representative";

      billingEmail = project.contact_email ||
                     owner?.email ||
                     currentUser?.email;

      // If no billing email found, throw error
      if (!billingEmail) {
        throw new Error('CRITICAL: No contact email found for agreement. Cannot generate legally valid agreement without proper contact information.');
      }
    }

    // Get state with fallbacks - NEVER use hardcoded defaults
    const state = project.state ||
                  owner?.state ||
                  allianceInfoToUse?.business_address?.state ||
                  allianceInfoToUse?.legal_entity_info?.incorporationState;

    // If no state found, throw error - this affects jurisdiction
    if (!state) {
      throw new Error('CRITICAL: No jurisdiction state found for agreement. Cannot generate legally valid agreement without proper jurisdiction.');
    }

    // Get city with fallbacks
    const city = project.city ||
                 owner?.city ||
                 allianceInfoToUse?.business_address?.city ||
                 'Unknown City';

    // Get address with fallbacks - NEVER use hardcoded defaults
    const address = project.address ||
                    owner?.address ||
                    allianceInfoToUse?.business_address?.full_address ||
                    allianceInfoToUse?.business_address?.street;

    // If no address found, throw error - this is required for legal agreements
    if (!address) {
      throw new Error('CRITICAL: No business address found for agreement. Cannot generate legally valid agreement without proper business address.');
    }

    // Extract company information with strict validation - all fields must be present
    const companyInfo = {
      name: companyName,
      address: address,
      state: state,
      city: city,
      county: owner?.county || allianceInfoToUse?.business_address?.county || `${city} County`,
      billingEmail: billingEmail,
      signerName: signerName,
      signerTitle: signerTitle,
      projectType: project.projectType || 'creative work',
      isAlliance: !!allianceInfoToUse,
      allianceType: allianceInfoToUse?.alliance_type || null,
      industry: allianceInfoToUse?.industry || null
    };

    // Final validation - ensure all critical fields are present
    const requiredFields = ['name', 'address', 'state', 'billingEmail', 'signerName'];
    const missingFields = requiredFields.filter(field => !companyInfo[field]);

    if (missingFields.length > 0) {
      throw new Error(`CRITICAL: Missing required company information fields: ${missingFields.join(', ')}. Cannot generate legally valid agreement.`);
    }

    return companyInfo;
  }

  /**
   * Validate and prepare contributor information
   * @param {string} fullName - The contributor's full name
   * @param {Object} currentUser - The current user
   * @returns {Object} - Validated contributor information
   */
  _validateContributorInfo(fullName, currentUser) {
    return {
      name: fullName || currentUser?.user_metadata?.full_name || "[Contributor]",
      email: currentUser?.email || "[Contributor Email]",
      address: currentUser?.address || "[Contributor Address]",
      isCompany: false, // Default to individual contributor
      companyName: "",
      signerName: "",
      signerTitle: ""
    };
  }

  /**
   * Replace company information in the template
   * @param {string} template - The agreement template
   * @param {Object} company - The company information
   * @returns {string} - The processed template
   */
  _replaceCompanyInfo(template, company) {
    let processed = template;

    // CRITICAL: Replace company information FIRST before any other replacements to prevent corruption
    // Replace company name in header
    processed = processed.replace(/# CITY OF GAMERS INC\./g, `# ${company.name.toUpperCase()}`);

    // Replace company name in text with case-insensitive global search - EXACT MATCHES ONLY
    processed = processed.replace(/City of Gamers Inc\./gi, company.name);
    processed = processed.replace(/City of Gamers Inc/gi, company.name);
    processed = processed.replace(/City of Gamers/gi, company.name);
    processed = processed.replace(/\bCOG\b/gi, company.name);

    // Replace company address with more specific pattern to catch variations
    const addressPattern = /1205 43rd Street,\s*Suite B,\s*Orlando,\s*Florida\s*32839/gi;
    processed = processed.replace(addressPattern, company.address);
    processed = processed.replace(/\[Project Address\]/gi, company.address);

    // Replace state of incorporation with more variations
    processed = processed.replace(/Florida corporation/gi, `${company.state} corporation`);
    processed = processed.replace(/Florida/gi, company.state);
    processed = processed.replace(/\[State\]/gi, company.state);

    // Replace city
    processed = processed.replace(/Orlando/gi, company.city);
    processed = processed.replace(/\[City\]/gi, company.city);

    // Replace county
    processed = processed.replace(/Orange County/gi, company.county);
    processed = processed.replace(/\[County\]/gi, company.county);

    // Replace billing email with more variations
    processed = processed.replace(/billing@cogfuture\.com/gi, company.billingEmail);
    processed = processed.replace(/\[Project Owner Email\]/gi, company.billingEmail);
    processed = processed.replace(/\[Company Email\]/gi, company.billingEmail);
    processed = processed.replace(/\[Email\]/gi, company.billingEmail);

    // Replace signer name with more variations
    processed = processed.replace(/Gynell Journigan/gi, company.signerName);
    processed = processed.replace(/\[Project Owner\]/gi, company.signerName);
    processed = processed.replace(/\[Company Representative\]/gi, company.signerName);

    // Replace signer title with more variations
    processed = processed.replace(/President/gi, company.signerTitle);
    processed = processed.replace(/\[Title\]/gi, company.signerTitle);

    // Replace "the applicable jurisdiction" with actual state
    processed = processed.replace(/the applicable jurisdiction/gi, company.state);

    // Replace project type specific terminology in definitions
    const projectType = company.projectType || 'creative work';
    if (projectType !== 'game') {
      // Update Programs definition to remove game references
      processed = processed.replace(
        /development and coding of video games, software/g,
        projectType === 'software' ? 'development and coding of software applications' :
        projectType === 'music' ? 'production and editing of music tracks' :
        projectType === 'film' || projectType === 'video' ? 'production and editing of video content' :
        'development of creative works'
      );
    }

    return processed;
  }

  /**
   * Replace contributor information in the template
   * @param {string} template - The agreement template
   * @param {Object} contributor - The contributor information
   * @returns {string} - The processed template
   */
  _replaceContributorInfo(template, contributor) {
    let processed = template;

    // Replace contributor name in all variations
    if (contributor.name !== "[Contributor]") {
      processed = processed.replace(/\[_+\] \(the "Contributor"\)/g, `${contributor.name} (the "Contributor")`);
      processed = processed.replace(/\[_+\]/g, contributor.name);
      processed = processed.replace(/\[Contributor\]/g, contributor.name);
      processed = processed.replace(/\[CONTRIBUTOR\]/g, contributor.name.toUpperCase());
    }

    // Replace contributor email
    if (contributor.email !== "[Contributor Email]") {
      processed = processed.replace(/\[Contributor Email\]/g, contributor.email);
    }

    // Replace contributor address
    if (contributor.address !== "[Contributor Address]") {
      processed = processed.replace(/\[Contributor Address\]/g, contributor.address);
    }

    // Generate appropriate signature block based on whether contributor is a company
    if (contributor.isCompany) {
      const companyBlock = `${contributor.companyName}

By: _________________________
Name: ${contributor.signerName}
Title: ${contributor.signerTitle}
Date: _______________________
Address: ${contributor.address}`;

      // Replace signature block
      processed = processed.replace(/\[If a company\][\s\S]*?_____________________________/g, companyBlock);

      // Remove individual signature block
      processed = processed.replace(/\[If an individual\][\s\S]*?_____________________________\n/g, "");
    } else {
      const individualBlock = `Name: ${contributor.name}
Date: ________________________
Address: ${contributor.address}`;

      // Replace signature block
      processed = processed.replace(/\[If an individual\][\s\S]*?_____________________________/g, individualBlock);

      // Remove company signature block
      processed = processed.replace(/\[If a company\][\s\S]*?_____________________________\n/g, "");
    }

    // Replace any remaining contributor placeholders
    processed = processed.replace(/\[Contributor Name\]/g, contributor.name);
    processed = processed.replace(/\[CONTRIBUTOR NAME\]/g, contributor.name.toUpperCase());

    // Replace date placeholders in signature section
    const today = new Date();
    const formattedDate = today.toLocaleDateString('en-US', {
      month: 'long',
      day: 'numeric',
      year: 'numeric'
    });

    processed = processed.replace(/Date: _+/g, `Date: ${formattedDate}`);

    return processed;
  }

  /**
   * Replace project information in the template
   * @param {string} template - The agreement template
   * @param {Object} project - The project data
   * @returns {string} - The processed template
   */
  _replaceProjectInfo(template, project) {
    let processed = template;

    // Replace project name in all variations
    processed = processed.replace(/Village of The Ages/gi, project.name);
    processed = processed.replace(/\[Project Name\]/gi, project.name);
    processed = processed.replace(/\[PROJECT NAME\]/gi, project.name.toUpperCase());
    processed = processed.replace(/Project Name/g, project.name);

    // Replace project title in header if present
    const titleRegex = /<title>.*?<\/title>/;
    if (titleRegex.test(processed)) {
      processed = processed.replace(titleRegex, `<title>${project.name} - Contributor Agreement</title>`);
    }

    // Replace project description in all variations
    const defaultDescription = "a village simulation game where players guide communities through historical progressions";
    const extendedDefaultDescription = "a village simulation game where players guide communities through historical progressions and manage resource-based challenges";

    processed = processed.replace(new RegExp(defaultDescription, 'gi'), project.description);
    processed = processed.replace(new RegExp(extendedDefaultDescription, 'gi'), project.description);
    processed = processed.replace(/\[Project Description\]/gi, project.description);

    // Replace project type specific terms - ONLY in specific contexts to avoid corrupting company names
    if (project.projectType && project.projectType !== 'game') {
      // Replace game-specific terms with project type appropriate terms - use word boundaries to avoid corrupting company names
      processed = processed.replace(/\bgame\b/gi, project.projectType);
      processed = processed.replace(/\bplayer\b/gi, project.projectType === 'music' ? 'listener' :
                                            project.projectType === 'film' || project.projectType === 'video' ? 'viewer' :
                                            'user');
    }

    // Replace any remaining project placeholders
    processed = processed.replace(/\[Project Type\]/gi, project.projectType || 'creative work');

    // Replace engine/platform information if available
    if (project.techStack?.engine || project.engine) {
      const engine = project.techStack?.engine || project.engine;
      processed = processed.replace(/\[Engine\]/gi, engine);
      processed = processed.replace(/Unreal Engine 5/gi, engine);
      processed = processed.replace(/Unity/gi, engine);
    }

    if (project.techStack?.platforms || project.platforms) {
      const platforms = project.techStack?.platforms || project.platforms;
      processed = processed.replace(/\[Platforms\]/gi, platforms);
      processed = processed.replace(/PC, Mobile/gi, platforms);
    }

    return processed;
  }

  /**
   * Replace project type specific terminology
   * @param {string} template - The agreement template
   * @param {Object} project - The project data
   * @returns {string} - The processed template
   */
  _replaceProjectTypeTerminology(template, project) {
    const projectType = project.projectType;
    let processed = template;

    // Define terminology replacements based on project type
    const replacements = this._getProjectTypeReplacements(projectType);

    // Apply replacements - use word boundaries and NEVER corrupt company names
    Object.entries(replacements).forEach(([search, replace]) => {
      // CRITICAL: Never replace words that are part of company names
      if (search === 'game') {
        // Create a very specific regex that excludes company names
        // Only replace 'game' when it's not part of "Gamers" or company context
        const gameRegex = /\bgame\b(?!rs?\b|(?:\s+(?:development|design|production|studio|company|inc|llc|corp)))/gi;
        processed = processed.replace(gameRegex, replace);
      } else {
        // Use word boundary for whole word replacement for other terms
        const regex = new RegExp(`\\b${search}\\b`, 'gi');
        processed = processed.replace(regex, replace);
      }

      // Also try with capitalized first letter - but be extra careful with "Game"
      const capitalizedSearch = search.charAt(0).toUpperCase() + search.slice(1);
      if (search !== 'game') {
        const capitalizedRegex = new RegExp(`\\b${capitalizedSearch}\\b`, 'g');
        processed = processed.replace(capitalizedRegex, replace.charAt(0).toUpperCase() + replace.slice(1));
      }
    });

    // Add specific phrases that might not be caught by word boundary replacements
    if (projectType !== 'game') {
      // Replace common game-specific phrases
      const gameSpecificPhrases = [
        { search: 'game ready', replace: `${projectType} ready` },
        { search: 'gameplay', replace: replacements['gameplay'] || 'usage' },
        { search: 'game builds', replace: replacements['game builds'] || `${projectType} versions` },
        { search: 'game world', replace: replacements['game world'] || `${projectType} environment` },
        { search: 'player experience', replace: replacements['player experience'] || 'user experience' },
        { search: 'players', replace: replacements['players'] || 'users' },
        { search: 'levels', replace: replacements['levels'] || 'components' },
        { search: 'early access', replace: 'initial release' }
      ];

      gameSpecificPhrases.forEach(({ search, replace }) => {
        const phraseRegex = new RegExp(search, 'gi');
        processed = processed.replace(phraseRegex, replace);
      });

      // Replace Revenue Definition and Calculation section
      processed = this._replaceRevenueDefinition(processed, projectType);
    }

    return processed;
  }

  /**
   * Replace Revenue Definition and Calculation section based on project type
   * @param {string} template - The agreement template
   * @param {string} projectType - The project type
   * @returns {string} - The processed template
   */
  _replaceRevenueDefinition(template, projectType) {
    let processed = template;

    // Define platform fees replacement based on project type
    let platformFeesReplacement = '';
    switch (projectType) {
      case 'software':
      case 'app':
        platformFeesReplacement = 'app store commissions, payment processor fees';
        break;
      case 'music':
        platformFeesReplacement = 'streaming service fees, distribution costs';
        break;
      case 'film':
      case 'video':
        platformFeesReplacement = 'streaming platform fees, distribution costs';
        break;
      default:
        platformFeesReplacement = 'distribution platform fees';
    }

    // Replace platform fees in Revenue Definition section
    processed = processed.replace(
      /Platform fees \(e\.g\., Steam, Epic Games Store, console platform fees\)/g,
      `Platform fees (e.g., ${platformFeesReplacement})`
    );

    return processed;
  }

  /**
   * Get project type specific terminology replacements
   * @param {string} projectType - The project type
   * @returns {Object} - Map of search terms to replacements
   */
  _getProjectTypeReplacements(projectType) {
    // Default replacements (game-specific terms to generic terms)
    const defaultReplacements = {
      'game': 'project',
      'players': 'users',
      'gameplay': 'usage',
      'levels': 'components',
      'characters': 'elements',
      'village simulation': 'digital',
      'historical progressions': 'project objectives',
      'DLC content': 'additional content',
      'game builds': 'project versions'
    };

    // Project type specific replacements
    switch (projectType) {
      case 'music':
        return {
          'game': 'music',
          'players': 'listeners',
          'gameplay': 'listening experience',
          'levels': 'tracks',
          'characters': 'artists',
          'village simulation': 'music production',
          'historical progressions': 'musical development',
          'DLC content': 'additional releases',
          'game builds': 'music releases',
          'Platform fees (e.g., Steam, Epic Games Store, console platform fees)': 'Platform fees (e.g., Spotify, Apple Music, streaming platform fees)',
          'Steam, Epic Games Store': 'Streaming Services (Spotify, Apple Music, etc.)'
        };
      case 'software':
      case 'app':
        return {
          'game': 'software application',
          'players': 'users',
          'gameplay': 'user experience',
          'levels': 'modules',
          'characters': 'components',
          'village simulation': 'software development',
          'historical progressions': 'software iterations',
          'DLC content': 'updates and expansions',
          'game builds': 'software releases'
        };
      case 'film':
      case 'video':
        return {
          'game': 'film project',
          'players': 'viewers',
          'gameplay': 'viewing experience',
          'levels': 'scenes',
          'characters': 'actors',
          'village simulation': 'film production',
          'historical progressions': 'narrative development',
          'DLC content': 'additional content',
          'game builds': 'film cuts'
        };
      default:
        return defaultReplacements;
    }
  }

  /**
   * Replace exhibits in the agreement
   * @param {string} template - The agreement template
   * @param {string} exhibitI - The Exhibit I content
   * @param {string} exhibitII - The Exhibit II content
   * @returns {string} - The processed template
   */
  _replaceExhibits(template, exhibitI, exhibitII) {
    let processed = template;

    // Find and replace Exhibit I - use a more specific regex to capture the entire content
    const exhibitIRegex = /## EXHIBIT I[\s\S]*?(?=## EXHIBIT II|$)/;
    if (exhibitIRegex.test(processed)) {
      processed = processed.replace(exhibitIRegex, `## EXHIBIT I\n### SPECIFICATIONS\n\n${exhibitI}\n\n`);
    } else {
      // If Exhibit I is not found, add it before Exhibit II
      const exhibitIIIndex = processed.indexOf('## EXHIBIT II');
      if (exhibitIIIndex !== -1) {
        processed = processed.substring(0, exhibitIIIndex) +
          `## EXHIBIT I\n### SPECIFICATIONS\n\n${exhibitI}\n\n` +
          processed.substring(exhibitIIIndex);
      } else {
        // If Exhibit II is not found, add Exhibit I at the end
        processed += `\n\n## EXHIBIT I\n### SPECIFICATIONS\n\n${exhibitI}\n\n`;
      }
    }

    // Find and replace Exhibit II - use a more specific regex to capture the entire content including any existing milestones
    const exhibitIIRegex = /## EXHIBIT II[\s\S]*?(?=## SCHEDULE|$)/;
    if (exhibitIIRegex.test(processed)) {
      processed = processed.replace(exhibitIIRegex, `## EXHIBIT II\n### PRODUCT ROADMAP\n\n${exhibitII}\n\n`);
    } else {
      // If Exhibit II is not found, add it before Schedule A
      const scheduleAIndex = processed.indexOf('## SCHEDULE A');
      if (scheduleAIndex !== -1) {
        processed = processed.substring(0, scheduleAIndex) +
          `## EXHIBIT II\n### PRODUCT ROADMAP\n\n${exhibitII}\n\n` +
          processed.substring(scheduleAIndex);
      } else {
        // If Schedule A is not found, add Exhibit II at the end
        processed += `\n\n## EXHIBIT II\n### PRODUCT ROADMAP\n\n${exhibitII}\n\n`;
      }
    }

    return processed;
  }

  /**
   * Replace royalty model information in the agreement
   * @param {string} template - The agreement template
   * @param {Object} royaltyModel - The royalty model
   * @param {Object} project - The project data
   * @returns {string} - The processed template
   */
  _replaceRoyaltyModelInfo(template, royaltyModel, project) {
    if (!royaltyModel) return template;

    let processed = template;

    // Replace royalty percentage
    if (royaltyModel.contributor_percentage) {
      processed = processed.replace(/33%/g, `${royaltyModel.contributor_percentage}%`);
    }

    // Replace minimum payout threshold
    if (royaltyModel.min_payout) {
      processed = processed.replace(/\$100,000/g, `$${(royaltyModel.min_payout / 100).toFixed(2)}`);
    }

    // Replace maximum payout
    if (royaltyModel.max_payout) {
      processed = processed.replace(/\$1,000,000/g, `$${(royaltyModel.max_payout / 100).toFixed(2)}`);
    }

    // Replace weights in calculation example
    if (royaltyModel.configuration) {
      const { tasks_weight, hours_weight, difficulty_weight } = royaltyModel.configuration;

      if (tasks_weight) {
        processed = processed.replace(/Tasks Completed: 30%/g, `Tasks Completed: ${tasks_weight}%`);
        processed = processed.replace(/40 × 0\.3 = 12/g, `40 × ${tasks_weight/100} = ${Math.round(40 * tasks_weight/100)}`);
      }

      if (hours_weight) {
        processed = processed.replace(/Hours Worked: 30%/g, `Hours Worked: ${hours_weight}%`);
        processed = processed.replace(/20 × 0\.3 = 6/g, `20 × ${hours_weight/100} = ${Math.round(20 * hours_weight/100)}`);
      }

      if (difficulty_weight) {
        processed = processed.replace(/Task Difficulty: 40%/g, `Task Difficulty: ${difficulty_weight}%`);
        processed = processed.replace(/40 × 0\.4 = 16/g, `40 × ${difficulty_weight/100} = ${Math.round(40 * difficulty_weight/100)}`);
      }
    }

    // Check if Schedule B is missing or incomplete
    if (!processed.includes('## SCHEDULE B') || !processed.includes('COMPENSATION')) {
      // Generate Schedule B
      const scheduleB = this._generateScheduleB(royaltyModel, project);

      // Find where to insert Schedule B (after SCHEDULE A)
      const scheduleAIndex = processed.indexOf('## SCHEDULE A');
      if (scheduleAIndex !== -1) {
        // Find the end of SCHEDULE A
        const afterScheduleA = processed.indexOf('##', scheduleAIndex + 12);
        if (afterScheduleA !== -1) {
          // Insert Schedule B before the next section
          processed = processed.substring(0, afterScheduleA) + scheduleB + processed.substring(afterScheduleA);
        } else {
          // If no next section, append to the end
          processed += scheduleB;
        }
      } else {
        // If SCHEDULE A is not found, append to the end
        processed += scheduleB;
      }
    }

    return processed;
  }

  /**
   * Generate Schedule B content
   * @param {Object} royaltyModel - The royalty model data
   * @param {Object} project - The project data
   * @returns {string} - The Schedule B content
   */
  _generateScheduleB(royaltyModel, project) {
    if (!royaltyModel) {
      royaltyModel = {
        model_type: 'custom',
        model_schema: 'cog',
        contributor_percentage: 50,
        min_payout: 10000,
        max_payout: 1000000,
        configuration: {
          tasks_weight: 33,
          hours_weight: 33,
          difficulty_weight: 34
        }
      };
    }

    const contributorPercentage = royaltyModel.contributor_percentage || 50;
    const minPayout = royaltyModel.min_payout ? (royaltyModel.min_payout / 100).toFixed(2) : '0.00';
    const maxPayout = royaltyModel.max_payout ? (royaltyModel.max_payout / 100).toFixed(2) : 'No limit';

    let scheduleB = `
## SCHEDULE B
### COMPENSATION

1. **Revenue Share Model**

   a. **Revenue Share Percentage:** Contributor shall be entitled to ${contributorPercentage}% of Net Revenue generated by the Work Product, subject to the terms and conditions set forth in this Agreement.

   b. **Calculation Method:** The CoG Model will be used to calculate Contributor's share of revenue, with the following weights:
      - Tasks completed: ${royaltyModel.configuration?.tasks_weight || 33}%
      - Hours contributed: ${royaltyModel.configuration?.hours_weight || 33}%
      - Task difficulty: ${royaltyModel.configuration?.difficulty_weight || 34}%

   c. **Minimum Payout Threshold:** No payments shall be made until the Contributor's share of Net Revenue reaches $${minPayout}.

   d. **Maximum Payout Cap:** The total compensation to Contributor under this Agreement shall not exceed $${maxPayout === 'No limit' ? 'No limit' : maxPayout}.

2. **Payment Terms**

   a. **Payment Schedule:** Payments shall be made quarterly within 30 days after the end of each calendar quarter.

   b. **Payment Method:** Payments shall be made via electronic transfer to Contributor's designated account.

   c. **Reporting:** Company shall provide Contributor with a detailed report of revenue calculations with each payment.

3. **Revenue Tranches**

   a. **Definition:** Revenue shall be allocated according to the following tranches:
      - Initial Release: From launch date until 12 months post-launch
      - Ongoing Revenue: All revenue generated after the Initial Release period

   b. **Platform Fees:** Platform fees (e.g., ${project.projectType === 'game' ? 'Steam, Epic Games Store, console platform fees' : project.projectType === 'software' || project.projectType === 'app' ? 'app store commissions, payment processor fees' : project.projectType === 'music' ? 'streaming service fees, distribution costs' : 'distribution platform fees'}) shall be deducted before calculating Net Revenue.

4. **Contribution Tracking**

   a. **Task Tracking:** All tasks must be properly documented in the Company's project management system.

   b. **Time Tracking:** Hours must be logged accurately using the Company's time tracking system.

   c. **Approval Process:** All contributions must be approved by the project manager before being counted toward revenue share calculations.

5. **Termination and Buyout**

   a. **Early Termination:** If this Agreement is terminated prior to Launch, Contributor shall be entitled to a pro-rated share of revenue based on contributions made up to the termination date.

   b. **Buyout Option:** Company reserves the right to buy out Contributor's revenue share rights at any time for a lump sum payment equal to 3x the projected annual revenue share, based on the previous 12 months of revenue or reasonable projections if less than 12 months of data is available.

`;

    return scheduleB;
  }

  /**
   * Generate Exhibit I (Specifications)
   * @param {Object} project - The project data
   * @returns {string} - The Exhibit I content
   */
  _generateExhibitI(project) {
    const projectName = project.name;
    const projectDescription = project.description;
    const projectType = project.projectType;

    // Generate core features based on project type
    const coreFeatures = this._generateCoreFeatures(projectType);

    // Generate technical requirements based on project type
    const technicalRequirements = this._generateTechnicalRequirements(projectType, project);

    // Create a project type specific overview extension
    let overviewExtension = '';
    switch (projectType) {
      case 'game':
        overviewExtension = 'The project features engaging gameplay mechanics and progression systems designed to create a compelling player experience.';
        break;
      case 'music':
        overviewExtension = 'The project encompasses composition, recording, and production designed to create a compelling listening experience.';
        break;
      case 'software':
      case 'app':
        overviewExtension = 'The project features intuitive user interfaces and robust functionality designed to create a seamless user experience.';
        break;
      case 'film':
      case 'video':
        overviewExtension = 'The project encompasses compelling visual storytelling and production values designed to create an engaging viewing experience.';
        break;
      default:
        overviewExtension = 'The project is designed to meet specific goals and requirements through collaborative development.';
    }

    return `**${projectName} - Project Specifications**

**Project Overview:**
${projectDescription}. ${overviewExtension}

**Core Features:**

${coreFeatures}

**Technical Requirements:**

${technicalRequirements}`;
  }

  /**
   * Generate core features based on project type
   * @param {string} projectType - The project type
   * @returns {string} - The core features content
   */
  _generateCoreFeatures(projectType) {
    switch (projectType) {
      case 'game':
        return `1. **Core Gameplay Mechanics**
   - Primary gameplay systems
   - Player interaction model
   - Challenge and progression systems
   - Resource management

2. **Game World & Environment**
   - World design and structure
   - Environmental systems
   - Interactive elements
   - Visual style and atmosphere

3. **Player Experience**
   - Character development
   - Skill progression
   - Reward systems
   - Engagement mechanics

4. **Technical Features**
   - Performance optimization
   - Cross-platform compatibility
   - Multiplayer functionality (if applicable)
   - Save system and persistence`;
      case 'music':
        return `1. **Composition & Arrangement**
   - Melodic development
   - Harmonic structure
   - Rhythmic elements
   - Arrangement and orchestration

2. **Production Quality**
   - Recording techniques
   - Mixing standards
   - Mastering specifications
   - Sound design elements

3. **Distribution Format**
   - Digital streaming requirements
   - Physical media specifications
   - Metadata standards
   - Promotional materials

4. **Rights Management**
   - Licensing structure
   - Royalty tracking
   - Usage permissions
   - Attribution requirements`;
      case 'software':
      case 'app':
        return `1. **Core Functionality**
   - User authentication and management
   - Data processing capabilities
   - Integration with external services
   - Performance optimization

2. **User Interface**
   - Responsive design
   - Accessibility features
   - Navigation structure
   - Visual consistency

3. **Data Management**
   - Storage architecture
   - Backup and recovery
   - Security protocols
   - Data validation

4. **Deployment & Maintenance**
   - Deployment pipeline
   - Update mechanisms
   - Monitoring systems
   - Support infrastructure`;
      default:
        return `1. **Core Components**
   - Primary functionality
   - Essential features
   - Basic structure
   - Fundamental elements

2. **User Experience**
   - Interface design
   - Interaction patterns
   - Accessibility considerations
   - User flow optimization

3. **Technical Framework**
   - Architecture design
   - Component integration
   - Performance standards
   - Security measures

4. **Delivery & Support**
   - Deployment strategy
   - Maintenance plan
   - Update mechanism
   - Support structure`;
    }
  }

  /**
   * Generate technical requirements based on project type
   * @param {string} projectType - The project type
   * @param {Object} project - The project data
   * @returns {string} - The technical requirements content
   */
  _generateTechnicalRequirements(projectType, project) {
    // Get tech stack information
    const techStack = project.techStack || this._determineTechStack(projectType, project.description, project);

    // Use tech stack values or fallbacks
    const platforms = techStack.platforms || project.platforms || this._getDefaultPlatforms(projectType);
    const engine = techStack.engine || project.engine || this._getDefaultEngine(projectType);
    const database = techStack.database || '';
    const languages = techStack.languages || [];
    const languagesText = languages.length > 0 ? languages.join(', ') : '';

    switch (projectType) {
      case 'game':
        const additionalTech = techStack.additionalTech || '';
        return `- Platform: ${platforms}
- Engine: ${engine}${languagesText ? `\n- Programming: ${languagesText}` : ''}
- Minimum Specs: Standard hardware requirements for the target platforms
- Art Style: Stylized, readable visuals with distinctive aesthetics
- Audio: Atmospheric soundtrack with appropriate sound design
- Version Control: Git-based source control with proper branching strategy${additionalTech ? `\n- Engine Features: ${additionalTech}` : ''}`;
      case 'music':
        return `- Format: High-quality WAV and MP3 deliverables
- Sample Rate: 48kHz/24-bit minimum
- Channels: Stereo with optional surround mixes
- Production Tools: ${engine}
- Metadata: Complete ID3 tags and ISRC codes
- Stems: Individual track stems for licensing flexibility`;
      case 'software':
      case 'app':
        return `- Platform: ${platforms}
- Framework: ${engine}${languagesText ? `\n- Programming: ${languagesText}` : ''}
- Database: ${database || 'PostgreSQL with Redis caching'}
- APIs: RESTful with GraphQL options
- Security: OAuth 2.0, data encryption, and regular security audits
- CI/CD: Automated testing and deployment pipeline`;
      case 'film':
      case 'video':
        return `- Platform: ${platforms}
- Editing Software: ${engine}
- Resolution: 4K (3840x2160) minimum
- Format: H.264/H.265 encoding
- Audio: 48kHz/24-bit stereo audio
- Color: Rec. 709 color space with proper color grading`;
      default:
        return `- Platform: ${platforms}
- Tools: ${engine}${languagesText ? `\n- Programming: ${languagesText}` : ''}
- Standards: Industry standard best practices
- Integration: Compatible with required third-party services
- Performance: Optimized for target platforms
- Documentation: Comprehensive technical documentation`;
    }
  }

  /**
   * Generate Exhibit II (Product Roadmap)
   * @param {Object} project - The project data
   * @param {Array} milestones - The project milestones
   * @returns {string} - The Exhibit II content
   */
  _generateExhibitII(project, milestones = []) {
    const projectName = project.name;
    const projectType = project.projectType;
    const projectLength = project.projectLength;
    const durationMonths = project.estimatedDuration;

    // Generate development phases based on project type and length
    const developmentPhases = this._generateDevelopmentPhases(projectType, projectLength, durationMonths);

    // Generate milestones list
    let milestoneList = '';

    if (milestones && milestones.length > 0) {
      // Use provided milestones
      milestones.forEach((milestone, index) => {
        milestoneList += `${index + 1}. **Milestone ${index + 1}: ${milestone.title || milestone.name || 'Milestone'} (${milestone.deadline || milestone.due_date || 'TBD'})**\n`;

        if (milestone.description || milestone.tasks) {
          const tasks = Array.isArray(milestone.description || milestone.tasks) ?
            (milestone.description || milestone.tasks) :
            [(milestone.description || milestone.tasks || 'Task to be determined')];

          tasks.forEach(task => {
            milestoneList += `   - ${task}\n`;
          });
        } else {
          milestoneList += `   - Deliverable to be determined\n`;
        }

        milestoneList += '\n';
      });
    } else {
      // Generate default milestones based on project type and length
      milestoneList = this._generateDefaultMilestones(projectType, projectLength, durationMonths);
    }

    return `**${projectName} - Development Roadmap**

${developmentPhases}
**Milestones:**

${milestoneList}`;
  }

  /**
   * Generate development phases based on project type and length
   * @param {string} projectType - The project type
   * @param {string} projectLength - The project length (short, medium, long)
   * @param {number} durationMonths - The project duration in months
   * @returns {string} - The development phases content
   */
  _generateDevelopmentPhases(projectType, projectLength, durationMonths) {
    // Determine number of phases based on project length
    let numPhases = 4; // Default for medium projects
    if (projectLength === 'short') numPhases = 2;
    else if (projectLength === 'long') numPhases = 6;

    // Calculate phase duration
    const phaseDuration = Math.max(1, Math.ceil(durationMonths / numPhases));

    let phases = '';

    switch (projectType) {
      case 'game':
        if (projectLength === 'short') {
          phases = `**Phase 1: Foundation & Core Gameplay (Month 1)**
- Core game systems implementation
- Basic gameplay mechanics
- Initial art and audio assets
- Prototype development

**Phase 2: Polish & Finalization (Month ${phaseDuration + 1})**
- Feature completion
- Content integration
- Testing and bug fixing
- Release preparation

`;
        } else if (projectLength === 'long') {
          phases = `**Phase 1: Pre-Production (Month 1-${phaseDuration})**
- Concept development
- Technical planning
- Art style definition
- Prototype creation

**Phase 2: Core Systems (Month ${phaseDuration + 1}-${phaseDuration * 2})**
- Core gameplay implementation
- Basic systems integration
- Initial content creation
- Technical foundation

**Phase 3: Feature Development (Month ${phaseDuration * 2 + 1}-${phaseDuration * 3})**
- Complete feature implementation
- Content production
- System integration
- Initial testing

**Phase 4: Content Creation (Month ${phaseDuration * 3 + 1}-${phaseDuration * 4})**
- Level design
- Asset finalization
- Narrative implementation
- Audio integration

**Phase 5: Polish & Optimization (Month ${phaseDuration * 4 + 1}-${phaseDuration * 5})**
- Performance optimization
- Visual polish
- Gameplay balancing
- Quality assurance

**Phase 6: Finalization & Launch (Month ${phaseDuration * 5 + 1}-${durationMonths})**
- Final bug fixing
- Platform certification
- Marketing preparation
- Launch readiness

`;
        } else {
          phases = `**Phase 1: Foundation (Month 1-${phaseDuration})**
- Core game systems functional
- Basic gameplay mechanics implemented
- First playable prototype delivered

**Phase 2: Core Gameplay (Month ${phaseDuration + 1}-${phaseDuration * 2})**
- Primary gameplay loops completed
- Player progression framework implemented
- Environmental systems integrated

**Phase 3: Content Development (Month ${phaseDuration * 2 + 1}-${phaseDuration * 3})**
- Game world fully implemented
- Complete feature set integrated
- Full UI implementation

**Phase 4: Polish & Finalization (Month ${phaseDuration * 3 + 1}-${Math.max(phaseDuration * 4, durationMonths)})**
- Balance adjustments based on testing
- Performance optimization
- Final art and audio integration
- Release preparation

`;
        }
        break;

      case 'software':
      case 'app':
        if (projectLength === 'short') {
          phases = `**Phase 1: Design & Core Development (Month 1)**
- Requirements analysis
- Architecture design
- Core functionality implementation
- Initial testing

**Phase 2: Finalization & Deployment (Month ${phaseDuration + 1})**
- Feature completion
- Quality assurance
- Performance optimization
- Deployment preparation

`;
        } else if (projectLength === 'long') {
          phases = `**Phase 1: Requirements & Planning (Month 1-${phaseDuration})**
- Stakeholder interviews
- Requirements gathering
- Technical specification
- Architecture planning

**Phase 2: Design & Prototyping (Month ${phaseDuration + 1}-${phaseDuration * 2})**
- UI/UX design
- Database schema design
- API specification
- Prototype development

**Phase 3: Core Development (Month ${phaseDuration * 2 + 1}-${phaseDuration * 3})**
- Backend implementation
- Frontend development
- Database integration
- Core functionality

**Phase 4: Feature Implementation (Month ${phaseDuration * 3 + 1}-${phaseDuration * 4})**
- Advanced features
- Integration with external services
- User authentication
- Data management

**Phase 5: Testing & Optimization (Month ${phaseDuration * 4 + 1}-${phaseDuration * 5})**
- Quality assurance
- Performance optimization
- Security testing
- User acceptance testing

**Phase 6: Deployment & Support (Month ${phaseDuration * 5 + 1}-${durationMonths})**
- Deployment preparation
- Documentation
- Training materials
- Launch and initial support

`;
        } else {
          phases = `**Phase 1: Architecture & Design (Month 1-${phaseDuration})**
- Requirements analysis
- System architecture design
- Database schema development
- UI/UX prototyping

**Phase 2: Core Development (Month ${phaseDuration + 1}-${phaseDuration * 2})**
- Backend systems implementation
- Frontend framework development
- API integration
- Database implementation

**Phase 3: Feature Implementation (Month ${phaseDuration * 2 + 1}-${phaseDuration * 3})**
- User authentication
- Core functionality development
- Integration with external services
- Initial testing

**Phase 4: Finalization (Month ${phaseDuration * 3 + 1}-${durationMonths})**
- Quality assurance
- Performance optimization
- Documentation
- Deployment preparation

`;
        }
        break;

      default:
        if (projectLength === 'short') {
          phases = `**Phase 1: Initial Development (Month 1)**
- Project setup
- Core functionality implementation
- Initial testing
- Feedback collection

**Phase 2: Finalization (Month ${phaseDuration + 1})**
- Feature completion
- Quality assurance
- Final adjustments
- Delivery preparation

`;
        } else if (projectLength === 'long') {
          phases = `**Phase 1: Planning & Requirements (Month 1-${phaseDuration})**
- Project planning
- Requirements gathering
- Stakeholder alignment
- Technical specification

**Phase 2: Design & Architecture (Month ${phaseDuration + 1}-${phaseDuration * 2})**
- Conceptual design
- Technical architecture
- Component specification
- Prototype development

**Phase 3: Core Development (Month ${phaseDuration * 2 + 1}-${phaseDuration * 3})**
- Foundation implementation
- Core functionality
- Basic integration
- Initial testing

**Phase 4: Feature Implementation (Month ${phaseDuration * 3 + 1}-${phaseDuration * 4})**
- Advanced features
- Component integration
- System refinement
- Comprehensive testing

**Phase 5: Refinement & Optimization (Month ${phaseDuration * 4 + 1}-${phaseDuration * 5})**
- Performance optimization
- Quality assurance
- User acceptance testing
- Feedback implementation

**Phase 6: Finalization & Delivery (Month ${phaseDuration * 5 + 1}-${durationMonths})**
- Final adjustments
- Documentation
- Delivery preparation
- Handover process

`;
        } else {
          phases = `**Phase 1: Initial Phase (Month 1-${phaseDuration})**
- Project setup
- Requirements gathering
- Initial design
- Framework establishment

**Phase 2: Development Phase (Month ${phaseDuration + 1}-${phaseDuration * 2})**
- Core component implementation
- Integration of primary elements
- Initial testing
- Feedback collection

**Phase 3: Refinement Phase (Month ${phaseDuration * 2 + 1}-${phaseDuration * 3})**
- Feature completion
- Quality assurance
- Performance optimization
- User acceptance testing

**Phase 4: Completion Phase (Month ${phaseDuration * 3 + 1}-${durationMonths})**
- Final adjustments
- Documentation
- Delivery preparation
- Handover process

`;
        }
    }

    return phases;
  }

  /**
   * Generate default milestones based on project type and length
   * @param {string} projectType - The project type
   * @param {string} projectLength - The project length (short, medium, long)
   * @param {number} durationMonths - The project duration in months
   * @returns {string} - The default milestones content
   */
  _generateDefaultMilestones(projectType, projectLength, durationMonths) {
    // Determine number of milestones based on project length
    let numMilestones = 4; // Default for medium projects
    if (projectLength === 'short') numMilestones = 2;
    else if (projectLength === 'long') numMilestones = 6;

    // Calculate milestone spacing
    const milestoneSpacing = Math.max(1, Math.ceil(durationMonths / numMilestones));

    let milestones = '';

    switch (projectType) {
      case 'game':
        if (projectLength === 'short') {
          milestones = `1. **Milestone 1: First Playable (End of Month 1)**
   - Core mechanics implemented
   - Basic systems functional
   - Initial prototype playable

2. **Milestone 2: Release Candidate (End of Month ${durationMonths})**
   - All features implemented
   - Content complete
   - Performance optimized
   - Major bugs resolved

`;
        } else if (projectLength === 'long') {
          milestones = `1. **Milestone 1: Concept Approval (End of Month ${milestoneSpacing})**
   - Game design document completed
   - Technical specification approved
   - Art style defined
   - Initial prototype demonstrated

2. **Milestone 2: Vertical Slice (End of Month ${milestoneSpacing * 2})**
   - Core gameplay systems implemented
   - Representative level playable
   - Basic art and audio integrated
   - Technical foundation established

3. **Milestone 3: Alpha Version (End of Month ${milestoneSpacing * 3})**
   - All major features implemented
   - Game systems integrated
   - Placeholder content complete
   - Initial balancing

4. **Milestone 4: Beta Version (End of Month ${milestoneSpacing * 4})**
   - All content implemented
   - Full game playable
   - Performance optimization
   - Bug fixing underway

5. **Milestone 5: Release Candidate (End of Month ${milestoneSpacing * 5})**
   - All features polished
   - Performance optimized
   - Major bugs resolved
   - Platform certification ready

6. **Milestone 6: Gold Master (End of Month ${durationMonths})**
   - Final bug fixes complete
   - All platform requirements met
   - Marketing materials finalized
   - Ready for distribution

`;
        } else {
          milestones = `1. **Milestone 1: Foundation (End of Month ${milestoneSpacing})**
   - Core game systems functional
   - Basic gameplay mechanics implemented
   - First playable prototype delivered

2. **Milestone 2: Core Gameplay (End of Month ${milestoneSpacing * 2})**
   - Primary gameplay loops completed
   - Player progression framework implemented
   - Environmental systems integrated

3. **Milestone 3: Content Development (End of Month ${milestoneSpacing * 3})**
   - Game world fully implemented
   - Complete feature set integrated
   - Full UI implementation

4. **Milestone 4: Polish & Finalization (End of Month ${durationMonths})**
   - Balance adjustments based on testing
   - Performance optimization
   - Final art and audio integration
   - Release preparation

`;
        }
        break;

      case 'software':
      case 'app':
        if (projectLength === 'short') {
          milestones = `1. **Milestone 1: MVP Development (End of Month 1)**
   - Core functionality implemented
   - Basic user interface
   - Essential features working
   - Initial testing complete

2. **Milestone 2: Release Version (End of Month ${durationMonths})**
   - All features implemented
   - UI/UX polished
   - Performance optimized
   - Deployment ready

`;
        } else if (projectLength === 'long') {
          milestones = `1. **Milestone 1: Requirements & Design (End of Month ${milestoneSpacing})**
   - Requirements documentation complete
   - Technical architecture defined
   - UI/UX design approved
   - Development plan finalized

2. **Milestone 2: Prototype (End of Month ${milestoneSpacing * 2})**
   - Core architecture implemented
   - Basic functionality working
   - Database schema established
   - API endpoints defined

3. **Milestone 3: Alpha Version (End of Month ${milestoneSpacing * 3})**
   - Core features implemented
   - Basic UI functional
   - Database integration complete
   - Initial testing underway

4. **Milestone 4: Beta Version (End of Month ${milestoneSpacing * 4})**
   - All major features implemented
   - UI/UX refined
   - Integration with external services
   - Comprehensive testing

5. **Milestone 5: Release Candidate (End of Month ${milestoneSpacing * 5})**
   - All features complete
   - Performance optimized
   - Security testing passed
   - User acceptance testing

6. **Milestone 6: Production Release (End of Month ${durationMonths})**
   - Final bug fixes
   - Documentation complete
   - Deployment configuration finalized
   - Production environment ready

`;
        } else {
          milestones = `1. **Milestone 1: Architecture & Design (End of Month ${milestoneSpacing})**
   - Requirements analysis complete
   - System architecture designed
   - Database schema developed
   - UI/UX prototypes approved

2. **Milestone 2: Core Development (End of Month ${milestoneSpacing * 2})**
   - Backend systems implemented
   - Frontend framework developed
   - API integration complete
   - Database implementation finished

3. **Milestone 3: Feature Implementation (End of Month ${milestoneSpacing * 3})**
   - User authentication system
   - Core functionality developed
   - External services integrated
   - Initial testing complete

4. **Milestone 4: Finalization (End of Month ${durationMonths})**
   - Quality assurance passed
   - Performance optimized
   - Documentation completed
   - Deployment ready

`;
        }
        break;

      default:
        if (projectLength === 'short') {
          milestones = `1. **Milestone 1: Initial Development (End of Month 1)**
   - Project setup complete
   - Core functionality implemented
   - Initial testing performed
   - Feedback collected

2. **Milestone 2: Final Delivery (End of Month ${durationMonths})**
   - All features implemented
   - Quality assurance complete
   - Final adjustments made
   - Delivery package prepared

`;
        } else if (projectLength === 'long') {
          milestones = `1. **Milestone 1: Planning & Requirements (End of Month ${milestoneSpacing})**
   - Project plan finalized
   - Requirements documented
   - Stakeholder approval obtained
   - Technical specifications completed

2. **Milestone 2: Design & Architecture (End of Month ${milestoneSpacing * 2})**
   - Conceptual design approved
   - Technical architecture defined
   - Component specifications completed
   - Prototype developed

3. **Milestone 3: Core Development (End of Month ${milestoneSpacing * 3})**
   - Foundation implemented
   - Core functionality working
   - Basic integration complete
   - Initial testing performed

4. **Milestone 4: Feature Implementation (End of Month ${milestoneSpacing * 4})**
   - Advanced features developed
   - Components integrated
   - System refined
   - Comprehensive testing conducted

5. **Milestone 5: Refinement & Optimization (End of Month ${milestoneSpacing * 5})**
   - Performance optimized
   - Quality assurance completed
   - User acceptance testing passed
   - Feedback implemented

6. **Milestone 6: Finalization & Delivery (End of Month ${durationMonths})**
   - Final adjustments made
   - Documentation completed
   - Delivery package prepared
   - Handover process executed

`;
        } else {
          milestones = `1. **Milestone 1: Initial Phase (End of Month ${milestoneSpacing})**
   - Project setup complete
   - Requirements gathered
   - Initial design approved
   - Framework established

2. **Milestone 2: Development Phase (End of Month ${milestoneSpacing * 2})**
   - Core components implemented
   - Primary elements integrated
   - Initial testing performed
   - Feedback collected

3. **Milestone 3: Refinement Phase (End of Month ${milestoneSpacing * 3})**
   - Features completed
   - Quality assurance conducted
   - Performance optimized
   - User acceptance testing passed

4. **Milestone 4: Completion Phase (End of Month ${durationMonths})**
   - Final adjustments made
   - Documentation completed
   - Delivery package prepared
   - Handover process executed

`;
        }
    }

    return milestones;
  }

  /**
   * Get default engine based on project type
   * @param {string} projectType - The project type
   * @returns {string} - Default engine
   */
  _getDefaultEngine(projectType) {
    switch (projectType) {
      case 'game':
        return 'Unreal Engine 5';
      case 'software':
      case 'app':
        return 'React with Native mobile implementations';
      case 'music':
        return 'Pro Tools and industry standard DAWs';
      case 'film':
      case 'video':
        return 'Adobe Premiere Pro and After Effects';
      default:
        return 'Industry standard tools';
    }
  }

  /**
   * Get default platforms based on project type
   * @param {string} projectType - The project type
   * @returns {string} - Default platforms
   */
  _getDefaultPlatforms(projectType) {
    switch (projectType) {
      case 'game':
        return 'PC (Steam, Epic Games Store)';
      case 'software':
      case 'app':
        return 'Web, iOS, and Android';
      case 'music':
        return 'Streaming Services (Spotify, Apple Music, etc.)';
      case 'film':
      case 'video':
        return 'YouTube, Vimeo, and streaming platforms';
      default:
        return 'Multiple platforms as appropriate';
    }
  }

  /**
   * Get default revenue share based on project type
   * @param {string} projectType - The project type
   * @returns {string} - Default revenue share percentage
   */
  _getDefaultRevenueShare(projectType) {
    switch (projectType) {
      case 'game':
        return '33%';
      case 'software':
      case 'app':
        return '25%';
      case 'music':
        return '50%';
      case 'art':
        return '40%';
      default:
        return '33%';
    }
  }

  /**
   * Get default payout threshold based on project type
   * @param {string} projectType - The project type
   * @returns {string} - Default payout threshold
   */
  _getDefaultPayoutThreshold(projectType) {
    switch (projectType) {
      case 'game':
        return '$100,000';
      case 'software':
      case 'app':
        return '$50,000';
      case 'music':
        return '$500';
      case 'art':
        return '$1,000';
      default:
        return '$10,000';
    }
  }

  /**
   * Get default maximum payment based on project type
   * @param {string} projectType - The project type
   * @returns {string} - Default maximum payment
   */
  _getDefaultMaxPayment(projectType) {
    switch (projectType) {
      case 'game':
        return '$1,000,000';
      case 'software':
      case 'app':
        return '$500,000';
      case 'music':
        return '$50,000';
      case 'art':
        return '$100,000';
      default:
        return '$250,000';
    }
  }
}