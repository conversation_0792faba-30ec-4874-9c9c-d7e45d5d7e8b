/**
 * Open Source Compliance System
 * 
 * Comprehensive system for managing open source licenses and compliance in collaborative projects:
 * - Open source license detection and analysis
 * - License compatibility checking
 * - Compliance requirement tracking
 * - Attribution and notice generation
 * - Copyleft obligation management
 * - SBOM (Software Bill of Materials) generation
 */

// ============================================================================
// OPEN SOURCE LICENSE TYPES AND CATEGORIES
// ============================================================================

export const OPEN_SOURCE_LICENSES = {
  // Permissive Licenses
  MIT: { 
    name: 'MIT License', 
    type: 'permissive', 
    copyleft: false, 
    attribution: true,
    commercial: true,
    modification: true,
    distribution: true,
    patent_grant: false
  },
  APACHE_2_0: { 
    name: 'Apache License 2.0', 
    type: 'permissive', 
    copyleft: false, 
    attribution: true,
    commercial: true,
    modification: true,
    distribution: true,
    patent_grant: true
  },
  BSD_3_CLAUSE: { 
    name: 'BSD 3-Clause License', 
    type: 'permissive', 
    copyleft: false, 
    attribution: true,
    commercial: true,
    modification: true,
    distribution: true,
    patent_grant: false
  },
  
  // Copyleft Licenses
  GPL_2_0: { 
    name: 'GNU General Public License v2.0', 
    type: 'copyleft', 
    copyleft: 'strong', 
    attribution: true,
    commercial: true,
    modification: true,
    distribution: true,
    patent_grant: false
  },
  GPL_3_0: { 
    name: 'GNU General Public License v3.0', 
    type: 'copyleft', 
    copyleft: 'strong', 
    attribution: true,
    commercial: true,
    modification: true,
    distribution: true,
    patent_grant: true
  },
  LGPL_2_1: { 
    name: 'GNU Lesser General Public License v2.1', 
    type: 'copyleft', 
    copyleft: 'weak', 
    attribution: true,
    commercial: true,
    modification: true,
    distribution: true,
    patent_grant: false
  },
  
  // Creative Commons
  CC_BY_4_0: { 
    name: 'Creative Commons Attribution 4.0', 
    type: 'permissive', 
    copyleft: false, 
    attribution: true,
    commercial: true,
    modification: true,
    distribution: true,
    patent_grant: false
  },
  CC_BY_SA_4_0: { 
    name: 'Creative Commons Attribution-ShareAlike 4.0', 
    type: 'copyleft', 
    copyleft: 'strong', 
    attribution: true,
    commercial: true,
    modification: true,
    distribution: true,
    patent_grant: false
  },
  
  // Other Notable Licenses
  MPL_2_0: { 
    name: 'Mozilla Public License 2.0', 
    type: 'copyleft', 
    copyleft: 'weak', 
    attribution: true,
    commercial: true,
    modification: true,
    distribution: true,
    patent_grant: true
  },
  UNLICENSE: { 
    name: 'The Unlicense', 
    type: 'public_domain', 
    copyleft: false, 
    attribution: false,
    commercial: true,
    modification: true,
    distribution: true,
    patent_grant: false
  }
};

export const LICENSE_COMPATIBILITY_MATRIX = {
  // Permissive licenses are generally compatible with everything
  MIT: ['MIT', 'APACHE_2_0', 'BSD_3_CLAUSE', 'GPL_2_0', 'GPL_3_0', 'LGPL_2_1', 'MPL_2_0'],
  APACHE_2_0: ['MIT', 'APACHE_2_0', 'BSD_3_CLAUSE', 'GPL_3_0', 'LGPL_2_1', 'MPL_2_0'],
  BSD_3_CLAUSE: ['MIT', 'APACHE_2_0', 'BSD_3_CLAUSE', 'GPL_2_0', 'GPL_3_0', 'LGPL_2_1', 'MPL_2_0'],
  
  // GPL licenses have strict compatibility requirements
  GPL_2_0: ['GPL_2_0', 'LGPL_2_1'],
  GPL_3_0: ['GPL_3_0', 'APACHE_2_0'],
  LGPL_2_1: ['MIT', 'APACHE_2_0', 'BSD_3_CLAUSE', 'GPL_2_0', 'GPL_3_0', 'LGPL_2_1', 'MPL_2_0'],
  
  // Mozilla Public License
  MPL_2_0: ['MIT', 'APACHE_2_0', 'BSD_3_CLAUSE', 'GPL_3_0', 'LGPL_2_1', 'MPL_2_0'],
  
  // Creative Commons
  CC_BY_4_0: ['CC_BY_4_0', 'CC_BY_SA_4_0'],
  CC_BY_SA_4_0: ['CC_BY_SA_4_0'],
  
  // Public Domain
  UNLICENSE: ['MIT', 'APACHE_2_0', 'BSD_3_CLAUSE', 'GPL_2_0', 'GPL_3_0', 'LGPL_2_1', 'MPL_2_0', 'UNLICENSE']
};

export const COMPLIANCE_REQUIREMENTS = {
  ATTRIBUTION: 'attribution',
  NOTICE_PRESERVATION: 'notice_preservation',
  SOURCE_DISCLOSURE: 'source_disclosure',
  COPYLEFT_PROPAGATION: 'copyleft_propagation',
  PATENT_GRANT: 'patent_grant',
  TRADEMARK_RESTRICTION: 'trademark_restriction',
  WARRANTY_DISCLAIMER: 'warranty_disclaimer'
};

// ============================================================================
// OPEN SOURCE COMPLIANCE SYSTEM CLASS
// ============================================================================

export class OpenSourceComplianceSystem {
  constructor() {
    this.projectCompliance = new Map();
    this.licenseInventory = new Map();
    this.complianceReports = new Map();
    this.sbomRecords = new Map();
    this.violationTracking = new Map();
  }

  /**
   * Analyze project for open source compliance
   */
  analyzeProjectCompliance(projectDefinition) {
    const complianceAnalysis = {
      id: projectDefinition.id || this.generateComplianceId(),
      projectId: projectDefinition.projectId,
      projectName: projectDefinition.projectName,
      
      // Analysis metadata
      analysisDate: new Date().toISOString(),
      analysisVersion: '1.0',
      analysisScope: projectDefinition.analysisScope || 'full_project',
      
      // Discovered components
      discoveredComponents: this.scanForOpenSourceComponents(projectDefinition),
      
      // License analysis
      detectedLicenses: [],
      licenseConflicts: [],
      compatibilityIssues: [],
      
      // Compliance requirements
      complianceRequirements: [],
      attributionRequirements: [],
      copyleftObligations: [],
      
      // Risk assessment
      riskLevel: 'low',
      riskFactors: [],
      
      // Recommendations
      recommendations: [],
      actionItems: [],
      
      // SBOM generation
      sbom: null,
      
      // Status
      status: 'in_progress'
    };

    // Analyze each discovered component
    complianceAnalysis.discoveredComponents.forEach(component => {
      this.analyzeComponent(component, complianceAnalysis);
    });

    // Check for license conflicts
    complianceAnalysis.licenseConflicts = this.checkLicenseConflicts(complianceAnalysis.detectedLicenses);

    // Assess overall risk
    complianceAnalysis.riskLevel = this.assessComplianceRisk(complianceAnalysis);

    // Generate recommendations
    complianceAnalysis.recommendations = this.generateComplianceRecommendations(complianceAnalysis);

    // Generate SBOM
    complianceAnalysis.sbom = this.generateSBOM(complianceAnalysis);

    complianceAnalysis.status = 'completed';
    this.projectCompliance.set(complianceAnalysis.id, complianceAnalysis);

    return complianceAnalysis;
  }

  /**
   * Scan for open source components in project
   */
  scanForOpenSourceComponents(projectDefinition) {
    // Mock implementation - in production, this would scan actual project files
    const mockComponents = [
      {
        name: 'react',
        version: '18.2.0',
        type: 'npm_package',
        license: 'MIT',
        path: 'node_modules/react',
        homepage: 'https://reactjs.org/',
        repository: 'https://github.com/facebook/react'
      },
      {
        name: 'express',
        version: '4.18.2',
        type: 'npm_package',
        license: 'MIT',
        path: 'node_modules/express',
        homepage: 'https://expressjs.com/',
        repository: 'https://github.com/expressjs/express'
      },
      {
        name: 'lodash',
        version: '4.17.21',
        type: 'npm_package',
        license: 'MIT',
        path: 'node_modules/lodash',
        homepage: 'https://lodash.com/',
        repository: 'https://github.com/lodash/lodash'
      }
    ];

    return mockComponents.map(component => ({
      ...component,
      id: this.generateComponentId(),
      discoveredAt: new Date().toISOString(),
      licenseDetails: OPEN_SOURCE_LICENSES[component.license] || null,
      dependencies: [], // Would be populated with actual dependency scanning
      vulnerabilities: [] // Would be populated with security scanning
    }));
  }

  /**
   * Analyze individual component for compliance
   */
  analyzeComponent(component, complianceAnalysis) {
    const licenseKey = component.license;
    const licenseDetails = OPEN_SOURCE_LICENSES[licenseKey];

    if (!licenseDetails) {
      complianceAnalysis.riskFactors.push(`Unknown license: ${licenseKey} for component ${component.name}`);
      return;
    }

    // Add to detected licenses
    if (!complianceAnalysis.detectedLicenses.find(l => l.license === licenseKey)) {
      complianceAnalysis.detectedLicenses.push({
        license: licenseKey,
        details: licenseDetails,
        components: [component.name],
        count: 1
      });
    } else {
      const existingLicense = complianceAnalysis.detectedLicenses.find(l => l.license === licenseKey);
      existingLicense.components.push(component.name);
      existingLicense.count++;
    }

    // Check compliance requirements
    if (licenseDetails.attribution) {
      complianceAnalysis.attributionRequirements.push({
        component: component.name,
        license: licenseKey,
        requirement: 'Include copyright notice and license text',
        licenseText: this.getLicenseText(licenseKey),
        copyrightNotice: this.getCopyrightNotice(component)
      });
    }

    if (licenseDetails.copyleft) {
      complianceAnalysis.copyleftObligations.push({
        component: component.name,
        license: licenseKey,
        copyleftType: licenseDetails.copyleft,
        obligation: this.getCopyleftObligation(licenseDetails.copyleft),
        sourceDisclosureRequired: licenseDetails.copyleft === 'strong'
      });
    }

    // Add general compliance requirements
    const requirements = this.getComplianceRequirements(licenseDetails);
    complianceAnalysis.complianceRequirements.push(...requirements.map(req => ({
      component: component.name,
      license: licenseKey,
      requirement: req
    })));
  }

  /**
   * Check for license conflicts between components
   */
  checkLicenseConflicts(detectedLicenses) {
    const conflicts = [];
    
    for (let i = 0; i < detectedLicenses.length; i++) {
      for (let j = i + 1; j < detectedLicenses.length; j++) {
        const license1 = detectedLicenses[i];
        const license2 = detectedLicenses[j];
        
        if (!this.areLicensesCompatible(license1.license, license2.license)) {
          conflicts.push({
            license1: license1.license,
            license2: license2.license,
            conflictType: this.getConflictType(license1.details, license2.details),
            severity: this.getConflictSeverity(license1.details, license2.details),
            resolution: this.suggestConflictResolution(license1.license, license2.license),
            affectedComponents: {
              license1: license1.components,
              license2: license2.components
            }
          });
        }
      }
    }
    
    return conflicts;
  }

  /**
   * Check if two licenses are compatible
   */
  areLicensesCompatible(license1, license2) {
    const compatibleLicenses = LICENSE_COMPATIBILITY_MATRIX[license1] || [];
    return compatibleLicenses.includes(license2);
  }

  /**
   * Assess overall compliance risk
   */
  assessComplianceRisk(complianceAnalysis) {
    let riskScore = 0;
    
    // Risk factors
    if (complianceAnalysis.licenseConflicts.length > 0) {
      riskScore += complianceAnalysis.licenseConflicts.length * 3;
    }
    
    if (complianceAnalysis.copyleftObligations.length > 0) {
      riskScore += complianceAnalysis.copyleftObligations.length * 2;
    }
    
    if (complianceAnalysis.riskFactors.length > 0) {
      riskScore += complianceAnalysis.riskFactors.length;
    }
    
    // Determine risk level
    if (riskScore >= 10) return 'high';
    if (riskScore >= 5) return 'medium';
    return 'low';
  }

  /**
   * Generate compliance recommendations
   */
  generateComplianceRecommendations(complianceAnalysis) {
    const recommendations = [];
    
    // Attribution recommendations
    if (complianceAnalysis.attributionRequirements.length > 0) {
      recommendations.push({
        type: 'attribution',
        priority: 'high',
        description: 'Create comprehensive attribution file with all required notices',
        actionItems: [
          'Generate NOTICE file with all copyright notices',
          'Include license texts for all components',
          'Ensure attribution is visible in distributed software'
        ]
      });
    }
    
    // Copyleft recommendations
    if (complianceAnalysis.copyleftObligations.length > 0) {
      recommendations.push({
        type: 'copyleft',
        priority: 'high',
        description: 'Address copyleft license obligations',
        actionItems: [
          'Prepare source code disclosure for strong copyleft licenses',
          'Ensure modified copyleft components are properly marked',
          'Consider license compatibility for distribution'
        ]
      });
    }
    
    // Conflict resolution recommendations
    if (complianceAnalysis.licenseConflicts.length > 0) {
      recommendations.push({
        type: 'conflict_resolution',
        priority: 'critical',
        description: 'Resolve license conflicts before distribution',
        actionItems: complianceAnalysis.licenseConflicts.map(conflict => 
          `Resolve conflict between ${conflict.license1} and ${conflict.license2}: ${conflict.resolution}`
        )
      });
    }
    
    return recommendations;
  }

  /**
   * Generate Software Bill of Materials (SBOM)
   */
  generateSBOM(complianceAnalysis) {
    const sbom = {
      bomFormat: 'SPDX',
      specVersion: '2.3',
      dataLicense: 'CC0-1.0',
      SPDXID: 'SPDXRef-DOCUMENT',
      name: `${complianceAnalysis.projectName}-SBOM`,
      documentNamespace: `https://royaltea.app/sbom/${complianceAnalysis.projectId}`,
      creationInfo: {
        created: new Date().toISOString(),
        creators: ['Tool: Royaltea Agreement System'],
        licenseListVersion: '3.21'
      },
      
      // Package information
      packages: complianceAnalysis.discoveredComponents.map(component => ({
        SPDXID: `SPDXRef-Package-${component.name}`,
        name: component.name,
        downloadLocation: component.repository || 'NOASSERTION',
        filesAnalyzed: false,
        licenseConcluded: component.license,
        licenseDeclared: component.license,
        copyrightText: this.getCopyrightNotice(component),
        versionInfo: component.version,
        homepage: component.homepage || 'NOASSERTION',
        supplier: 'NOASSERTION'
      })),
      
      // Relationships
      relationships: complianceAnalysis.discoveredComponents.map(component => ({
        spdxElementId: 'SPDXRef-DOCUMENT',
        relationshipType: 'DESCRIBES',
        relatedSpdxElement: `SPDXRef-Package-${component.name}`
      })),
      
      // License information
      hasExtractedLicensingInfos: complianceAnalysis.detectedLicenses.map(license => ({
        licenseId: `LicenseRef-${license.license}`,
        extractedText: this.getLicenseText(license.license),
        name: license.details.name
      }))
    };
    
    this.sbomRecords.set(complianceAnalysis.id, sbom);
    return sbom;
  }

  /**
   * Generate attribution notice file
   */
  generateAttributionNotice(complianceAnalysisId) {
    const analysis = this.projectCompliance.get(complianceAnalysisId);
    if (!analysis) {
      throw new Error(`Compliance analysis ${complianceAnalysisId} not found`);
    }

    let noticeText = `THIRD-PARTY SOFTWARE NOTICES AND INFORMATION\n`;
    noticeText += `For ${analysis.projectName}\n\n`;
    noticeText += `This project incorporates components from the projects listed below.\n`;
    noticeText += `The original copyright notices and the licenses under which we received such components are set forth below.\n\n`;

    analysis.attributionRequirements.forEach((req, index) => {
      noticeText += `${index + 1}. ${req.component}\n`;
      noticeText += `License: ${req.license}\n`;
      if (req.copyrightNotice) {
        noticeText += `Copyright: ${req.copyrightNotice}\n`;
      }
      noticeText += `\n${req.licenseText}\n`;
      noticeText += `${'='.repeat(80)}\n\n`;
    });

    return noticeText;
  }

  /**
   * Track compliance violations
   */
  trackViolation(violationDefinition) {
    const violation = {
      id: violationDefinition.id || this.generateViolationId(),
      
      // Violation details
      projectId: violationDefinition.projectId,
      component: violationDefinition.component,
      license: violationDefinition.license,
      violationType: violationDefinition.violationType,
      description: violationDefinition.description,
      
      // Severity and impact
      severity: violationDefinition.severity || 'medium',
      impact: violationDefinition.impact || 'compliance_risk',
      
      // Discovery information
      discoveredBy: violationDefinition.discoveredBy,
      discoveredDate: new Date().toISOString(),
      discoveryMethod: violationDefinition.discoveryMethod || 'automated_scan',
      
      // Resolution tracking
      status: 'open',
      assignedTo: violationDefinition.assignedTo,
      dueDate: violationDefinition.dueDate,
      resolutionPlan: violationDefinition.resolutionPlan || [],
      
      // Resolution details
      resolvedDate: null,
      resolutionMethod: null,
      resolutionNotes: null,
      
      // Metadata
      createdAt: new Date().toISOString(),
      lastUpdated: new Date().toISOString()
    };

    this.violationTracking.set(violation.id, violation);
    return violation;
  }

  // ============================================================================
  // HELPER METHODS
  // ============================================================================

  /**
   * Get license text for a given license
   */
  getLicenseText(licenseKey) {
    // Mock implementation - in production, this would fetch actual license texts
    const licenseTexts = {
      MIT: `MIT License\n\nPermission is hereby granted, free of charge, to any person obtaining a copy...`,
      APACHE_2_0: `Apache License\nVersion 2.0, January 2004\nhttp://www.apache.org/licenses/\n\nTERMS AND CONDITIONS FOR USE...`,
      GPL_3_0: `GNU GENERAL PUBLIC LICENSE\nVersion 3, 29 June 2007\n\nCopyright (C) 2007 Free Software Foundation...`
    };
    
    return licenseTexts[licenseKey] || `License text for ${licenseKey} not available`;
  }

  /**
   * Get copyright notice for component
   */
  getCopyrightNotice(component) {
    // Mock implementation - in production, this would extract actual copyright notices
    return `Copyright (c) ${new Date().getFullYear()} ${component.name} contributors`;
  }

  /**
   * Get copyleft obligation description
   */
  getCopyleftObligation(copyleftType) {
    const obligations = {
      strong: 'Must provide source code for entire work when distributing',
      weak: 'Must provide source code for modified copyleft components only'
    };
    
    return obligations[copyleftType] || 'Copyleft obligations apply';
  }

  /**
   * Get compliance requirements for license
   */
  getComplianceRequirements(licenseDetails) {
    const requirements = [];
    
    if (licenseDetails.attribution) {
      requirements.push(COMPLIANCE_REQUIREMENTS.ATTRIBUTION);
      requirements.push(COMPLIANCE_REQUIREMENTS.NOTICE_PRESERVATION);
    }
    
    if (licenseDetails.copyleft) {
      requirements.push(COMPLIANCE_REQUIREMENTS.COPYLEFT_PROPAGATION);
      if (licenseDetails.copyleft === 'strong') {
        requirements.push(COMPLIANCE_REQUIREMENTS.SOURCE_DISCLOSURE);
      }
    }
    
    if (licenseDetails.patent_grant) {
      requirements.push(COMPLIANCE_REQUIREMENTS.PATENT_GRANT);
    }
    
    requirements.push(COMPLIANCE_REQUIREMENTS.WARRANTY_DISCLAIMER);
    
    return requirements;
  }

  /**
   * Get conflict type between licenses
   */
  getConflictType(license1Details, license2Details) {
    if (license1Details.copyleft && license2Details.copyleft) {
      return 'copyleft_incompatibility';
    }
    if (license1Details.copyleft || license2Details.copyleft) {
      return 'copyleft_permissive_conflict';
    }
    return 'general_incompatibility';
  }

  /**
   * Get conflict severity
   */
  getConflictSeverity(license1Details, license2Details) {
    if (license1Details.copyleft === 'strong' || license2Details.copyleft === 'strong') {
      return 'high';
    }
    if (license1Details.copyleft === 'weak' || license2Details.copyleft === 'weak') {
      return 'medium';
    }
    return 'low';
  }

  /**
   * Suggest conflict resolution
   */
  suggestConflictResolution(license1, license2) {
    const resolutions = {
      'GPL_2_0,GPL_3_0': 'Upgrade GPL 2.0 components to GPL 3.0 compatible versions',
      'GPL_2_0,APACHE_2_0': 'Replace GPL 2.0 components or use dual-licensed alternatives',
      'GPL_3_0,APACHE_2_0': 'Compatible - ensure proper attribution for Apache components'
    };
    
    const key = [license1, license2].sort().join(',');
    return resolutions[key] || 'Consult legal counsel for resolution strategy';
  }

  /**
   * Generate unique IDs
   */
  generateComplianceId() {
    return 'compliance_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  generateComponentId() {
    return 'component_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  generateViolationId() {
    return 'violation_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  /**
   * Get all compliance analyses
   */
  getAllComplianceAnalyses() {
    return Array.from(this.projectCompliance.values());
  }

  /**
   * Get compliance analysis by project
   */
  getComplianceByProject(projectId) {
    return Array.from(this.projectCompliance.values()).filter(analysis => 
      analysis.projectId === projectId
    );
  }

  /**
   * Get open violations
   */
  getOpenViolations() {
    return Array.from(this.violationTracking.values()).filter(violation => 
      violation.status === 'open'
    );
  }

  /**
   * Export compliance data
   */
  exportComplianceData() {
    return {
      projectCompliance: Array.from(this.projectCompliance.values()),
      licenseInventory: Array.from(this.licenseInventory.values()),
      complianceReports: Array.from(this.complianceReports.values()),
      sbomRecords: Array.from(this.sbomRecords.values()),
      violationTracking: Array.from(this.violationTracking.values()),
      exportedAt: new Date().toISOString()
    };
  }
}

// Export the open source compliance system
export const openSourceComplianceSystem = new OpenSourceComplianceSystem();
